# DDEV Local Development Environment for COMAVE Magento project

# Prerequisites
1. Install DDEV: https://ddev.readthedocs.io/en/stable/users/install/ddev-installation/
If you already have DDEV installed, ensure you update it from time to time: https://ddev.readthedocs.io/en/stable/users/install/ddev-upgrade/
2. Start DDEV (`ddev start`) or restart if already running (`ddev restart`), from your project root folder.
3. Adjust your local `app/etc/env.php`, according to `app/etc/env.ddev.sample.php` file
    - regading RabbitMQ, see `'queue'` configuration node and copy it into your `app/etc/env.php` file
4. `ddev status` to see your containers and connections details
5. Import your database dump, see ddev documentation or use your preferred db client

## RabbitMQ
RabbitMQ was added via this addon: https://github.com/b13/ddev-rabbitmq. You can find more reading the instructions from this repository.
You can access RabbitMQ UI here: https://comave-magento.ddev.site:15673/
User: rabbitmq, Pass: rabbitmq

## Magento Cron
The crontab entry is currently disabled.
If you want the Magento cron to run the same as in a production instance, you can enable cron by uncommenting the content of `.ddev/config.cron.yaml`, then restarting (`ddev restart`).
If you choose to enable cron, beware of performance hit you will get every minute on your local machine.
If you choose to leave cron disabled but still want to run cron on demand, you can:
- `bin/magento cron:run`, which will run cron's according to schedule
- `magerun2 sys:cron:run` and `magerun2 sys:cron` other commands, which allow you to run whatever cron you need

## Something is not working or not looking right ?
Also useful to make sure your changes do not break anything else!
Run inside container (ddev ssh):
- `composer install`
- `php bin/magento setup:upgrade`
- `php bin/magento setup:di:compile`
- `php bin/magento weltpixel:less:generate`
- `php bin/magento setup:static-content:deploy -f`

## Common ddev related issues
### Don't save big files into the project folder
Avoid saving database dumps, for example, in the project folder.
This will affect mutagen sync operation and can cause problems syncing files between host and guest.
