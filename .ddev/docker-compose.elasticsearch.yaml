#ddev-generated
services:
  elasticsearch:
    container_name: ddev-${DDEV_SITENAME}-elasticsearch
    hostname: ${DDEV_SITENAME}-elasticsearch
    image: elasticsearch:7.17.14
    expose:
      - "9200"
      - "9300"
    environment:
      - cluster.name=docker-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - VIRTUAL_HOST=$DDEV_HOSTNAME
      - HTTP_EXPOSE=9200:9200
      - HTTPS_EXPOSE=9201:9200
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
      com.ddev.approot: $DDEV_APPROOT
    volumes:
      - elasticsearch:/usr/share/elasticsearch/data
      - ".:/mnt/ddev_config"
    healthcheck:
      test: ["CMD-SHELL", "curl --fail -s elasticsearch:9200"]

volumes:
  elasticsearch:
