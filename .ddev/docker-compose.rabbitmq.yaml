#ddev-generated
services:
  rabbitmq:
    container_name: ddev-${DDEV_SITENAME}-rabbitmq
    hostname: ${DDEV_SITENAME}-rabbitmq
    image: "rabbitmq:3-management-alpine"
    expose:
      - 15672
    environment:
      - VIRTUAL_HOST=$DDEV_HOSTNAME
      - HTTP_EXPOSE=15672
      - HTTPS_EXPOSE=15673:15672
      - RABBITMQ_ERLANG_COOKIE=SWQOKODSQALRPCLNMEQG
      - RABBITMQ_DEFAULT_USER=rabbitmq
      - RABBITMQ_DEFAULT_PASS=rabbitmq
      - RABBITMQ_DEFAULT_VHOST=/
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
      com.ddev.approot: $DDEV_APPROOT
    volumes:
      - "rabbitmq:/var/lib/rabbitmq/mnesia"
      - ".:/mnt/ddev_config"
  web:
    links:
      - rabbitmq:rabbitmq
volumes:
  rabbitmq:
    name: "${DDEV_SITENAME}_rabbitmq"
