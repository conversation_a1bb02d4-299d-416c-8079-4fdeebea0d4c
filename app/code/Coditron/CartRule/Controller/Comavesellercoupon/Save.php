<?php
namespace Coditron\CartRule\Controller\Comavesellercoupon;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Coditron\CartRule\Model\ComavesellerCouponFactory;
use Coditron\CartRule\Model\CustomDiscountRuleCreator;
use Magento\Framework\App\RequestInterface;

class Save extends Action
{
    protected $comavesellercouponFactory;
    /**
     * @var CustomDiscountRuleCreator
     */
    private $customDiscountRuleCreator;

    protected $dataPersistor;
    protected $request;

    public function __construct(
        Context $context,
        \Magento\Framework\App\Request\DataPersistorInterface $dataPersistor,
        ComavesellerCouponFactory $comavesellercouponFactory,
        CustomDiscountRuleCreator $customDiscountRuleCreator,
        \Magento\Framework\App\ResourceConnection $resource,
         RequestInterface $request
    ) {
        
        $this->comavesellercouponFactory = $comavesellercouponFactory;
        $this->customDiscountRuleCreator = $customDiscountRuleCreator;
        $this->dataPersistor = $dataPersistor;
        $this->resource = $resource;
        $this->request = $request;
        $this->connection = $resource->getConnection();
        parent::__construct($context);
    }

    public function execute()
    {
        $tableName = 'coditron_cartrule_comaveseller_coupon';
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $data = $this->getRequest()->getPostValue();
        if ($data) {
            $model = $this->_objectManager->create(\Coditron\CartRule\Model\ComavesellerCoupon::class);

            if (!empty($data['entity_id'])) {
                
                $comaveseller_coupon_id = $data['entity_id'];
                $existingCoupon = $model->load($comaveseller_coupon_id);
                if ($existingCoupon->getComavesellerCouponId()) {
                     
                    $data['rule_id'] = $existingCoupon->getRuleId();
                    
                    $ruleData = $this->customDiscountRuleCreator->createCustomDiscountRule($data);
                    $couponCode = isset($ruleData['coupon_code']) ? $ruleData['coupon_code'] : null;
                    // Convert apply_on_last to boolean
                    $applyOnLast = !empty($data['apply_on_last']) && ($data['apply_on_last'] == 'true' || $data['apply_on_last'] == '1') ? 1 : 0;
                    
                    // Get the customer group IDs from $data
                    $customerGroupIds = isset($data['customer_group']) ? implode(',', $data['customer_group']) : '0,1,2,3'; // Default to all groups if not set

                    // Get the website IDs from $data
                    $websiteIds = isset($data['websites']) ? implode(',', $data['websites']) : '1'; // Default to website ID 1 if not set

                    //update the existing rule
                    if (!empty($couponCode)) {
                        
                        foreach ($couponCode as $coupon) {
                            
                            $filteredData = [
                                'rule_status' => $data['rule_status'],
                                'rule_name' => $data['rule_name'],
                                'seller_name' => $data['seller_name'],
                                'coupon_code' => $coupon,
                                'coupon_type' => $data['coupon_type'],
                                'coupon_value' => $data['discount_amount'],
                                'coupon_step' => $data['minimum_qty'],
                                'apply_on_last' => $applyOnLast,
                                'expire_at' => $data['expiry_date'],
                                'customer_group_ids' => $customerGroupIds,
                                'website_ids' => $websiteIds
                            ];
                        }
                    } else {
                        
                        $filteredData = [
                            'rule_status' => $data['rule_status'],
                            'rule_name' => $data['rule_name'],
                            'seller_name' => $data['seller_name'],
                            'coupon_code' => null,
                            'coupon_type' => $data['coupon_type'],
                            'coupon_value' => $data['discount_amount'],
                            'coupon_step' => $data['minimum_qty'],
                            'apply_on_last' => $applyOnLast,
                            'expire_at' => $data['expiry_date'],
                            'customer_group_ids' => $customerGroupIds,
                            'website_ids' => $websiteIds
                        ];
                    }
                    
                    $this->connection->update(
                            $this->resource->getTableName($tableName),
                            $filteredData,
                            ['comaveseller_coupon_id = ?' => $comaveseller_coupon_id]
                        );
                    
                } else {
                    $this->messageManager->addErrorMessage(__('This Comaveseller Coupon no longer exists.'));
                    return $resultRedirect->setPath('*/*/');
                }
            } else {
                
                $ruleData = $this->customDiscountRuleCreator->createCustomDiscountRule($data);
                $ruleId = $ruleData['rule_id'];
                $couponCode = isset($ruleData['coupon_code']) ? $ruleData['coupon_code'] : null;
                
                // Convert apply_on_last to boolean
                $applyOnLast = !empty($data['apply_on_last']) && ($data['apply_on_last'] == 'true' || $data['apply_on_last'] == '1') ? 1 : 0;
                $customerGroupIds = isset($data['customer_group']) ? $data['customer_group'] : [0, 1, 2, 3]; // Default to all groups if not set
                //Get the Website Ids from data
                $websiteIds = isset($data['websites']) ? $data['websites'] : [1];

                if (!empty($couponCode)) {
                    
                    foreach ($couponCode as $coupon) {
                        $filteredData[] = [
                            'rule_id' => $ruleId,
                            'rule_status' => $data['rule_status'],
                            'rule_name' => $data['rule_name'],
                            'seller_name' => $data['seller_name'],
                            'coupon_code' => $coupon,
                            'coupon_type' => $data['coupon_type'],
                            'coupon_value' => $data['discount_amount'],
                            'coupon_step' => $data['minimum_qty'],
                            'apply_on_last' => $applyOnLast,
                            'expire_at' => $data['expiry_date'],
                            'customer_group_ids' => $customerGroupIds,
                            'website_ids' => $websiteIds
                        ];
                    }
                } else {
                    
                    $filteredData[] = [
                        'rule_id' => $ruleId,
                        'rule_status' => $data['rule_status'],
                        'rule_name' => $data['rule_name'],
                        'seller_name' => $data['seller_name'],
                        'coupon_code' => null,
                        'coupon_type' => $data['coupon_type'],
                        'coupon_value' => $data['discount_amount'],
                        'coupon_step' => $data['minimum_qty'],
                        'apply_on_last' => $applyOnLast,
                        'expire_at' => $data['expiry_date'],
                        'customer_group_ids' => $customerGroupIds,
                        'website_ids' => $websiteIds
                    ];
                }
                
                $this->connection->insertMultiple($this->resource->getTableName($tableName), $filteredData);
            }

            try {
                
                $model->save();
                $this->messageManager->addSuccessMessage(__('You saved the Comaveseller Coupon.'));
                $this->dataPersistor->clear('coditron_cartrule_comaveseller_coupon');

                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath('*/*/edit', ['comaveseller_coupon_id' => $model->getId()]);
                }
                return $resultRedirect->setPath('coditron_cartrule/account/promotionalrule');
            } catch (LocalizedException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addExceptionMessage($e, __('Something went wrong while saving the Comaveseller Coupon.'));
            }

            $this->dataPersistor->set('coditron_cartrule_comaveseller_coupon', $data);
            return $resultRedirect->setPath('*/*/edit', ['comaveseller_coupon_id' => $this->getRequest()->getParam('comaveseller_coupon_id')]);
        }

        return $resultRedirect->setPath('coditron_cartrule/account/promotionalrule');
    }

}
