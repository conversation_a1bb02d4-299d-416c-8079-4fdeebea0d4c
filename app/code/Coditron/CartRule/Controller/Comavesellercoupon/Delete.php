<?php
namespace Coditron\CartRule\Controller\Comavesellercoupon;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Message\ManagerInterface;
use Coditron\CartRule\Model\ComavesellerCouponFactory;

class Delete extends Action
{
    /**
     * @var ComavesellerCouponFactory
     */
    protected $comavesellerCouponFactory;

    /**
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * @var ResourceConnection
     */
    protected $resourceConnection;

    /**
     * @param Action\Context $context
     * @param JsonFactory $jsonFactory
     * @param ComavesellerCouponFactory $comavesellerCouponFactory
     * @param ManagerInterface $messageManager
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        Context $context,
        JsonFactory $jsonFactory,
        ComavesellerCouponFactory $comavesellerCouponFactory,
        ManagerInterface $messageManager,
        ResourceConnection $resourceConnection
    ) {
        $this->jsonFactory = $jsonFactory;
        $this->comavesellerCouponFactory = $comavesellerCouponFactory;
        $this->messageManager = $messageManager;
        $this->resourceConnection = $resourceConnection;
        parent::__construct($context);
    }

    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        // check if we know what should be deleted
        $id = $this->getRequest()->getParam('id');
        if ($id) {
            try {
                // get the rule_id before deleting the row
                $connection = $this->resourceConnection->getConnection();
                $tableName = $this->resourceConnection->getTableName('coditron_cartrule_comaveseller_coupon');
                
                // Fetch rule_id
                $select = $connection->select()
                    ->from($tableName, ['rule_id'])
                    ->where('comaveseller_coupon_id = ?', $id);
                $ruleId = $connection->fetchOne($select);

                // delete record from coditron_cartrule_comaveseller_coupon
                $connection->delete($tableName, ["comaveseller_coupon_id = ?" => $id]);

                // delete record from salesrule
                if ($ruleId) {
                    $salesRuleTable = $this->resourceConnection->getTableName('salesrule');
                    $connection->delete($salesRuleTable, ["rule_id = ?" => $ruleId]);
                    $salesruleCouponTable = $this->resourceConnection->getTableName('salesrule_coupon');
                    $connection->delete($salesruleCouponTable, ["rule_id = ?" => $ruleId]);
                }

                // display success message
                $this->messageManager->addSuccessMessage(__('You deleted the Comaveseller Coupon and corresponding sales rule.'));
                // go to grid
                return $resultRedirect->setPath('coditron_cartrule/account/promotionalrule');
            } catch (\Exception $e) {
                // display error message
                $this->messageManager->addErrorMessage($e->getMessage());
                // go back to edit form
                return $resultRedirect->setPath('*/*/edit', ['comaveseller_coupon_id' => $id]);
            }
        }
        // display error message
        $this->messageManager->addErrorMessage(__('We can\'t find a Comaveseller Coupon to delete.'));
        // go to grid
        return $resultRedirect->setPath('coditron_cartrule/account/promotionalrule');
    }
}
