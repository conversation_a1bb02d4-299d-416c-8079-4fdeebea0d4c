<?php
declare(strict_types=1);

namespace Coditron\CartRule\Controller\Adminhtml\Comavesellercoupon;

use Magento\Backend\App\Action;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Message\ManagerInterface;
use Coditron\CartRule\Model\ComavesellerCouponFactory;

class Delete extends Action
{
    /**
     * @var ComavesellerCouponFactory
     */
    protected $comavesellerCouponFactory;

    /**
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * @var ResourceConnection
     */
    protected $resourceConnection;

    /**
     * @param Action\Context $context
     * @param ComavesellerCouponFactory $comavesellerCouponFactory
     * @param ManagerInterface $messageManager
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        Action\Context $context,
        ComavesellerCouponFactory $comavesellerCouponFactory,
        ManagerInterface $messageManager,
        ResourceConnection $resourceConnection
    ) {
        parent::__construct($context);
        $this->comavesellerCouponFactory = $comavesellerCouponFactory;
        $this->messageManager = $messageManager;
        $this->resourceConnection = $resourceConnection;
    }

    /**
     * Delete action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        // check if we know what should be deleted
        $id = $this->getRequest()->getParam('comaveseller_coupon_id');
        
        if ($id) {
            try {
                // delete record using ResourceConnection
                $connection = $this->resourceConnection->getConnection();
                $tableName = $this->resourceConnection->getTableName('coditron_cartrule_comaveseller_coupon'); 
                // Fetch rule_id
                $select = $connection->select()
                    ->from($tableName, ['rule_id'])
                    ->where('comaveseller_coupon_id = ?', $id);
                $ruleId = $connection->fetchOne($select);
                
                // delete record from coditron_cartrule_comaveseller_coupon
                $connection->delete($tableName, ["comaveseller_coupon_id = ?" => $id]);

                // delete record from salesrule
                if ($ruleId) {
                    $salesRuleTable = $this->resourceConnection->getTableName('salesrule');
                    $connection->delete($salesRuleTable, ["rule_id = ?" => $ruleId]);
                    $salesruleCouponTable = $this->resourceConnection->getTableName('salesrule_coupon');
                    $connection->delete($salesruleCouponTable, ["rule_id = ?" => $ruleId]);
                }

                // display success message
                $this->messageManager->addSuccessMessage(__('You deleted the Seller Coupon.'));
                // go to grid
                return $resultRedirect->setPath('*/*/');
            } catch (\Exception $e) {
                // display error message
                $this->messageManager->addErrorMessage($e->getMessage());
                // go back to edit form
                return $resultRedirect->setPath('*/*/edit', ['comaveseller_coupon_id' => $id]);
            }
        }
        // display error message
        $this->messageManager->addErrorMessage(__('We can\'t find a Comaveseller Coupon to delete.'));
        // go to grid
        return $resultRedirect->setPath('*/*/');
    }
}
