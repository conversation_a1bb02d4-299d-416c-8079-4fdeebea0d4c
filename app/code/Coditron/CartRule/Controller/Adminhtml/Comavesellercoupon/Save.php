<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Coditron\CartRule\Controller\Adminhtml\Comavesellercoupon;

use Magento\Framework\Exception\LocalizedException;
use Coditron\CartRule\Model\CustomDiscountRuleCreator;

class Save extends \Magento\Backend\App\Action
{

    /**
     * @var CustomDiscountRuleCreator
     */
    private $customDiscountRuleCreator;

    protected $dataPersistor;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\App\Request\DataPersistorInterface $dataPersistor
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\App\Request\DataPersistorInterface $dataPersistor,
        CustomDiscountRuleCreator $customDiscountRuleCreator,
        \Magento\Framework\App\ResourceConnection $resource
    ) {
        $this->dataPersistor = $dataPersistor;
        $this->customDiscountRuleCreator = $customDiscountRuleCreator;
        $this->connection = $resource->getConnection();
        $this->resource = $resource;
        parent::__construct($context);
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $tableName = 'coditron_cartrule_comaveseller_coupon';
        $filteredData = [];
        
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $data = $this->getRequest()->getPostValue();
        if ($data) {
            $id = $this->getRequest()->getParam('comaveseller_coupon_id');
            $model = $this->_objectManager->create(\Coditron\CartRule\Model\ComavesellerCoupon::class);
            if (!$model->getId() && $id) {
                $this->messageManager->addErrorMessage(__('This Comaveseller Coupon no longer exists.'));
                return $resultRedirect->setPath('*/*/');
            }
        
            $ruleData = $this->customDiscountRuleCreator->createCustomDiscountRule($data);
                $ruleId = $ruleData['rule_id'];
                $couponCode = isset($ruleData['coupon_code']) ? $ruleData['coupon_code'] : null;
                
                 // Convert apply_on_last to boolean
                $applyOnLast = !empty($data['apply_on_last']) && ($data['apply_on_last'] == 'true' || $data['apply_on_last'] == '1') ? 1 : 0;

                if(!empty($couponCode)){
                foreach ($couponCode as $coupon) {
                   $filteredData = [
                    'rule_id' => $ruleId,
                    'rule_status' => $data['rule_status'],
                    'rule_name' => $data['rule_name'],
                    'seller_name' => $data['seller_name'],
                    'coupon_code' => $coupon,
                    'coupon_type' => $data['coupon_type'],
                    'coupon_value' => $data['discount_amount'],
                    'coupon_step' => $data['minimum_qty'],
                    'apply_on_last' => $applyOnLast,
                    'expire_at' => $data['expiry_date']
                 ];
                }
                 
                }else{
                   $filteredData = [
                    'rule_id' => $ruleId,
                    'rule_status' => $data['rule_status'],
                    'rule_name' => $data['rule_name'],
                    'seller_name' => $data['seller_name'],
                    'coupon_code' => null,
                    'coupon_type' => $data['coupon_type'],
                    'coupon_value' => $data['discount_amount'],
                    'coupon_step' => $data['minimum_qty'],
                    'apply_on_last' => $applyOnLast,
                    'expire_at' => $data['expiry_date']
                 ]; 
                }
                
            $tableName = $this->resource->getTableName($tableName);
            $this->connection->insertMultiple($tableName, $filteredData);
            try {
                
                $model->save();
                $this->messageManager->addSuccessMessage(__('You saved the Comaveseller Coupon.'));
                $this->dataPersistor->clear('coditron_cartrule_comaveseller_coupon');
                
                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath('*/*/edit', ['comaveseller_coupon_id' => $model->getId()]);
                }
                return $resultRedirect->setPath('*/*/');
            } catch (LocalizedException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addExceptionMessage($e, __('Something went wrong while saving the Comaveseller Coupon.'));
            }
        
            $this->dataPersistor->set('coditron_cartrule_comaveseller_coupon', $data);
            return $resultRedirect->setPath('*/*/edit', ['comaveseller_coupon_id' => $this->getRequest()->getParam('comaveseller_coupon_id')]);
        }
        return $resultRedirect->setPath('*/*/');
    }
}

