<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Coditron\CartRule\Controller\Adminhtml\Comavesellercoupon;

class Edit extends \Coditron\CartRule\Controller\Adminhtml\Comavesellercoupon
{

    protected $resultPageFactory;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        parent::__construct($context, $coreRegistry);
    }

    /**
     * Edit action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        // 1. Get ID and create model
        $id = $this->getRequest()->getParam('comaveseller_coupon_id');
        $model = $this->_objectManager->create(\Coditron\CartRule\Model\ComavesellerCoupon::class);
        
        // 2. Initial checking
        if ($id) {
            $model->load($id);
            if (!$model->getId()) {
                $this->messageManager->addErrorMessage(__('This Comaveseller Coupon no longer exists.'));
                /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
                $resultRedirect = $this->resultRedirectFactory->create();
                return $resultRedirect->setPath('*/*/');
            }
        }
        $this->_coreRegistry->register('coditron_cartrule_comaveseller_coupon', $model);
        
        // 3. Build edit form
        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $resultPage = $this->resultPageFactory->create();
        $this->initPage($resultPage)->addBreadcrumb(
            $id ? __('Edit Comaveseller Coupon') : __('New Comaveseller Coupon'),
            $id ? __('Edit Comaveseller Coupon') : __('New Comaveseller Coupon')
        );
        $resultPage->getConfig()->getTitle()->prepend(__('Comaveseller Coupons'));
        $resultPage->getConfig()->getTitle()->prepend($model->getId() ? __('Edit Comaveseller Coupon %1', $model->getId()) : __('New Comaveseller Coupon'));
        return $resultPage;
    }
}

