<?php

namespace Coditron\CartRule\Controller\Account;

use Magento\Framework\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\App\RequestInterface;
use Magento\Customer\Model\Url;
use Magento\Customer\Model\Session;
use Webkul\Marketplace\Helper\Data as MpHelper;
use Magento\Framework\Controller\Result\ForwardFactory;

class Promotionalrule extends \Magento\Framework\App\Action\Action
{
    protected $resultPageFactory;
    protected $url;
    protected $session;
    protected $mpHelper;
    protected $resultForwardFactory;

    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        Url $url,
        Session $session,
        MpHelper $mpHelper,
        ForwardFactory $resultForwardFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->url = $url;
        $this->session = $session;
        $this->mpHelper = $mpHelper;
        $this->resultForwardFactory = $resultForwardFactory;
        parent::__construct($context);
    }

    public function dispatch(RequestInterface $request)
    {
        $loginUrl = $this->url->getLoginUrl();
        if (!$this->session->authenticate($loginUrl)) {
            $this->_actionFlag->set('', self::FLAG_NO_DISPATCH, true);
        }

        return parent::dispatch($request);
    }

    /**
     * All Request Action
     *
     * @return \Magento\Framework\Controller\Result\Redirect|\Magento\Framework\Controller\Result\Forward
     */
    public function execute()
    {
        
        $isPartner = $this->mpHelper->isSeller();
        if ($isPartner == 1) {
            $resultPage = $this->resultPageFactory->create();
           // if ($this->helper->getIsSeparatePanel()) {
                $resultPage->addHandle('cartrule_layout2_account_promotionalrule');
           // }
            $resultPage->getConfig()->getTitle()->set(__('Seller Coupons'));
            return $resultPage;
        } else {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }
        $resultForward = $this->resultForwardFactory->create();
        $resultForward->forward('noroute');
        return $resultForward;
    }
}
