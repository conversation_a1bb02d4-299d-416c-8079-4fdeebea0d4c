<?xml version="1.0" ?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">coditron_cartrule_comaveseller_coupon_form.comaveseller_coupon_form_data_source</item>
		</item>
		<item name="label" xsi:type="string" translate="true">General Information</item>
		<item name="template" xsi:type="string">templates/form/collapsible</item>
	</argument>
	<settings>
		<buttons>
			<button name="back" class="Coditron\CartRule\Block\Adminhtml\Comaveseller\Coupon\Edit\BackButton"/>
			<button name="delete" class="Coditron\CartRule\Block\Adminhtml\Comaveseller\Coupon\Edit\DeleteButton"/>
			<button name="save" class="Coditron\CartRule\Block\Adminhtml\Comaveseller\Coupon\Edit\SaveButton"/>
			<button name="save_and_continue" class="Coditron\CartRule\Block\Adminhtml\Comaveseller\Coupon\Edit\SaveAndContinueButton"/>
		</buttons>
		<namespace>coditron_cartrule_comaveseller_coupon_form</namespace>
		<dataScope>data</dataScope>
		<deps>
			<dep>coditron_cartrule_comaveseller_coupon_form.comaveseller_coupon_form_data_source</dep>
		</deps>
	</settings>
	<dataSource name="comaveseller_coupon_form_data_source">
		<argument name="data" xsi:type="array">
			<item name="js_config" xsi:type="array">
				<item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
			</item>
		</argument>
		<settings>
			<submitUrl path="*/*/save"/>
		</settings>
		<dataProvider name="comaveseller_coupon_form_data_source" class="Coditron\CartRule\Model\Comavesellercoupon\DataProvider">
			<settings>
				<requestFieldName>comaveseller_coupon_id</requestFieldName>
				<primaryFieldName>comaveseller_coupon_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<fieldset name="general">
		<settings>
			<label>General</label>
		</settings>
		<field name="seller_name" sortOrder="10" formElement="select">
		    <settings>
		        <validation>
		            <rule name="required-entry" xsi:type="boolean">true</rule>
		        </validation>
		        <dataType>text</dataType>
		        <label translate="true">Select Shop</label>
		    </settings>
		    <formElements>
		        <select>
		            <settings>
		                <options class="Coditron\CartRule\Model\Config\Source\Sellers"/>
		            </settings>
		        </select>
		    </formElements>
  		</field>
		<field name="rule_status">
		    <argument name="data" xsi:type="array">
		        <item name="config" xsi:type="array">
		            <item name="sortOrder" xsi:type="number">20</item>
		            <item name="dataType" xsi:type="string">boolean</item>
		            <item name="formElement" xsi:type="string">checkbox</item>
		            <item name="prefer" xsi:type="string">toggle</item>
		            <item name="label" xsi:type="string" translate="true">Enable</item>
		            <item name="valueMap" xsi:type="array">
		                <item name="true" xsi:type="number">1</item>
		                <item name="false" xsi:type="number">0</item>
		            </item>
		            <item name="valuesForOptions" xsi:type="array">
		                <item name="boolean" xsi:type="string">boolean</item>
		            </item>
		            <item name="default" xsi:type="number">1</item>
		            <item name="dataScope" xsi:type="string">rule_status</item>
		        </item>
		    </argument>
		</field>
		<field name="rule_name" formElement="input" sortOrder="30">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">comaveseller_coupon</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Rule Name</label>
				<dataScope>rule_name</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field name="coupon_type" sortOrder="40" formElement="select">
	      <settings>
	          <validation>
	              <rule name="required-entry" xsi:type="boolean">true</rule>
	          </validation>
	          <dataType>text</dataType>
	          <label translate="true">Coupon Type</label>
	      </settings>
	      <formElements>
	          <select>
	              <settings>
	                  <options class="Coditron\CartRule\Model\Source\Options"/>
	              </settings>
	          </select>
	      </formElements>
  		</field>
  		<field name="coupon_apply" sortOrder="50" formElement="select">
	      <settings>
	          <validation>
	              <rule name="required-entry" xsi:type="boolean">true</rule>
	          </validation>
	          <dataType>text</dataType>
	          <label translate="true">Coupon Apply</label>
	      </settings>
	      <formElements>
	          <select>
	              <settings>
	                  <options class="Coditron\CartRule\Model\Source\CouponOptions"/>
	              </settings>
	          </select>
	      </formElements>
  		</field>
  		<field name="minimum_qty">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Minimun Qty</item>
                    <item name="dataType" xsi:type="string">number</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">dataSource</item>
                    <item name="dataScope" xsi:type="string">minimum_qty</item>
                    <item name="sortOrder" xsi:type="number">60</item>
                    <item name="validation" xsi:type="array">
                        <item name="validate-number" xsi:type="boolean">true</item>
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="minimum_buy">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Minimun Buy</item>
                    <item name="dataType" xsi:type="string">number</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">dataSource</item>
                    <item name="dataScope" xsi:type="string">minimum_buy</item>
                    <item name="sortOrder" xsi:type="number">70</item>
                    <item name="validation" xsi:type="array">
                        <item name="validate-number" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
  		<field name="discount_amount" formElement="input" sortOrder="80">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">sales_rule</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-number" xsi:type="boolean">true</rule>
                    <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Discount Amount</label>
                <dataScope>discount_amount</dataScope>
            </settings>
        </field>
        <!-- <field name="apply_on_last">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Apply On Last Item</item>
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="source" xsi:type="string">dataSource</item>
                    <item name="dataScope" xsi:type="string">apply_on_last</item>
                    <item name="sortOrder" xsi:type="number">90</item>
                    <item name="value" xsi:type="boolean">false</item>
                </item>
            </argument>
        </field> -->
        <field name="apply_on_last" formElement="checkbox">
            <settings>
                <label translate="true">Apply On Last</label>
                <tooltip>
                    <description translate="true">This is my checkbox's description</description>
                </tooltip>
            </settings>
            <formElements>
                <checkbox>
                    <settings>
                        <prefer>checkbox</prefer>
                        <valueMap>
                            <map name="false" xsi:type="boolean">false</map>
                            <map name="true" xsi:type="boolean">true</map>
                        </valueMap>
                    </settings>
                </checkbox>
            </formElements>
        </field>
		<field name="expiry_date">
	        <argument name="data" xsi:type="array">
	            <item name="config" xsi:type="array">
	                <item name="sortOrder" xsi:type="number">100</item>
	                <item name="dataType" xsi:type="string">date</item>
	                <item name="formElement" xsi:type="string">date</item>
	                <item name="label" xsi:type="string" translate="true">Expire Date</item>
	                <item name="additionalClasses" xsi:type="string">admin__field-date</item>
	                <item name="options" xsi:type="array">
	                    <item name="dateFormat" xsi:type="string">yyyy-MM-dd</item>
	                    <item name="timeFormat" xsi:type="string">HH:mm:ss</item>
	                    <item name="showsTime" xsi:type="boolean">true</item>
	                </item>
	                <item name="validation" xsi:type="array">
	                    <item name="required-entry" xsi:type="boolean">true</item>
	                </item>
	                <item name="default" xsi:type="string">''</item>
	                <item name="dataScope" xsi:type="string">expiry_date</item>
	            </item>
	        </argument>
    	</field>
		<fieldset name="condition_data">
		    <argument name="data" xsi:type="array">
		        <item name="config" xsi:type="array">
		            <item name="collapsible" xsi:type="boolean">false</item>
		            <item name="label" xsi:type="string" translate="true">Condition</item>
		            <item name="sortOrder" xsi:type="number">110</item>
		        </item>
		    </argument>
		    <container name="conditions_serialized">
		        <argument name="data" xsi:type="array">
		            <item name="config" xsi:type="array">
		                <item name="sortOrder" xsi:type="number">120</item>
		            </item>
		        </argument>
		        <htmlContent name="html_content">
		            <argument name="block" xsi:type="object">Coditron\CartRule\Block\Adminhtml\Catalog\ConditionField\Condition</argument>
		        </htmlContent>
		    </container>
		</fieldset>
	</fieldset>
</form>
