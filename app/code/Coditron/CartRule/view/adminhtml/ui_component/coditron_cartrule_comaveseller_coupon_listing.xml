<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">coditron_cartrule_comaveseller_coupon_listing.coditron_cartrule_comaveseller_coupon_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <spinner>coditron_cartrule_comaveseller_coupon_columns</spinner>
        <deps>
            <dep>coditron_cartrule_comaveseller_coupon_listing.coditron_cartrule_comaveseller_coupon_listing_data_source</dep>
        </deps>
        <buttons>
            <button name="add">
                <url path="*/*/new"/>
                <class>primary</class>
                <label translate="true">Add new coupon</label>
            </button>
        </buttons>
    </settings>
    <dataSource name="coditron_cartrule_comaveseller_coupon_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">comaveseller_coupon_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>Coditron_CartRule::comaveseller_coupon</aclResource>
        <dataProvider name="coditron_cartrule_comaveseller_coupon_listing_data_source" class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>comaveseller_coupon_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters"/>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="coditron_cartrule_comaveseller_coupon_columns">
        <settings>
            <editorConfig>
                <param name="selectProvider" xsi:type="string">coditron_cartrule_comaveseller_coupon_listing.coditron_cartrule_comaveseller_coupon_listing.coditron_cartrule_comaveseller_coupon_columns.ids</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="indexField" xsi:type="string">comaveseller_coupon_id</param>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="coditron_cartrule/comavesellercoupon/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">coditron_cartrule_comaveseller_coupon_listing.coditron_cartrule_comaveseller_coupon_listing.coditron_cartrule_comaveseller_coupon_columns_editor</item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids">
            <settings>
                <indexField>comaveseller_coupon_id</indexField>
            </settings>
        </selectionsColumn>
        <column name="comaveseller_coupon_id">
            <settings>
                <filter>text</filter>
                <sorting>asc</sorting>
                <label translate="true">ID</label>
            </settings>
        </column>
        <column name="rule_id">
            <settings>
                <filter>text</filter>
                <label translate="true">rule_id</label>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                </editor>
            </settings>
        </column>
        <column name="rule_status">
            <settings>
                <filter>text</filter>
                <label translate="true">rule_status</label>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                </editor>
            </settings>
        </column>
        <column name="rule_name">
            <settings>
                <filter>text</filter>
                <label translate="true">rule_name</label>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                </editor>
            </settings>
        </column>
        <column name="created_at">
            <settings>
                <filter>text</filter>
                <label translate="true">created_at</label>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                </editor>
            </settings>
        </column>
        <actionsColumn name="actions" class="Coditron\CartRule\Ui\Component\Listing\Column\ComavesellercouponActions">
            <settings>
                <indexField>comaveseller_coupon_id</indexField>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>107</resizeDefaultWidth>
            </settings>
        </actionsColumn>
    </columns>
</listing>