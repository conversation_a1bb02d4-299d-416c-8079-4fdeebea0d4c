<?php
$writer = new \Zend_Log_Writer_Stream(BP . '/var/log/editCoupon.log');
            $logger = new \Zend_Log();
            $logger->addWriter($writer);
            $logger->info('order products');
// Instantiate the Products block class
$productsBlock = $block->getLayout()->createBlock(\Coditron\CartRule\Block\Products::class);

// Get the product options
$productOptions = $productsBlock->toOptionArray();
$sellerId = $productsBlock->getSellerId();
// Instantiate the Coupon block class
$couponBlock = $block->getLayout()->createBlock(\Coditron\CartRule\Block\CartRuleData::class);
$request = $couponBlock->getRequest();
$entityId = $request->getParam('id');
$couponData = $couponBlock->getCollectionById($entityId);

$rule_status = '';
$rule_name = '';
$coupon_type = '';
$coupon_apply = '';
$minimum_qty = '';
$minimum_buy = '';
$discount_amount = '';
$apply_on_last = '';
$expiry_date = '';
$selectedCustomerGroups = [];
$jsSkuValues = [];
$selectedWebsites = []; // Array to store selected websites

foreach($couponData as $coupon){
    $logger->info(print_r($coupon->getData(),true).' data');
    $rule_status = $coupon->getRuleStatus();
    $rule_name = $coupon->getRuleName();
    $coupon_type = $coupon->getCouponType();
    $coupon_apply = $coupon->getSimpleAction();
    $minimum_qty = $coupon->getCouponStep();
    $minimum_buy = $coupon->getDiscountQty();
    $discount_amount = $coupon->getCouponValue();
    $apply_on_last = $coupon->getApplyOnLast();
    $expiry_date = $coupon->getToDate();
    $conditionsSerialized = $coupon->getConditionsSerialized();
    $conditions = json_decode($conditionsSerialized, true);

    // Retrieve customer group IDs
    $selectedCustomerGroups = explode(',', $coupon->getCustomerGroupIds());
    // Retrieve website IDs
    $selectedWebsites = explode(',', $coupon->getWebsiteIds());
    // Initialize an array to store SKU values
    $skuValues = [];

    // Check if 'conditions' key exists and it's an array
    if (isset($conditions['conditions']) && is_array($conditions['conditions'])) {
        // Iterate through each condition
        foreach ($conditions['conditions'] as $condition) {
            // Check if the condition is of type 'Magento\\SalesRule\\Model\\Rule\\Condition\\Product'
            if (isset($condition['type']) && $condition['type'] === 'Magento\\SalesRule\\Model\\Rule\\Condition\\Product') {
                // Check if 'value' key exists and add SKU value to the array
                if (isset($condition['value'])) {
                    $skuValues[] = $condition['value'];
                }
            }
        }
    }
    $jsSkuValues = array_merge($jsSkuValues, $skuValues);
}

// Fetch customer groups
$customerGroups = $couponBlock->getCustomerGroups();

// Fetch websites
$websites = $couponBlock->getWebsites();

?>
<form action="<?php echo $block->getUrl('coditron_cartrule/comavesellercoupon/save') ?>" method="post">
    <!-- Buttons Container -->
    <div class="buttons-container">
        <button type="button" onclick="window.location.href='<?php echo $block->getUrl('coditron_cartrule/account/promotionalrule'); ?>'"><?php echo __('Back') ?></button>
        <!-- Submit button -->
        <button type="submit"><?php echo __('Save') ?></button>
    </div>
    <!-- Conditionally include the entity ID -->
    <?php if ($entityId): ?>
        <input type="hidden" name="entity_id" value="<?php echo $entityId; ?>">
    <?php endif; ?>
    <!-- Hidden input field for the seller ID -->
    <input type="hidden" name="seller_name" value="<?php echo $sellerId; ?>">
    <div class="field">
        <label class="label" for="rule-status" ><?php echo __('Rule Status') ?></label>
       
            <input type="hidden" name="rule_status" value="0"> <!-- Hidden field to set the default value to 0 -->
            <input type="checkbox" id="rule-status" name="rule_status" value="1" <?php if ($rule_status == 1) echo 'checked'; ?>>
        
    </div>
    <div class="field">
        <label class="label" for="rule-name"><?php echo __('Rule Name') ?></label>
        <div class="control">
            <input type="text" id="rule-name" name="rule_name" value="<?php echo $rule_name; ?>">
        </div>
    </div>
    <div class="field customer-grp">
        <label class="label" for="customer-group"><?php echo __('Customer Group') ?><span style="color: red;">*</span></label>
        <div class="control">
            <select id="customer-group" name="customer_group[]" multiple="multiple" required>
                <option value=""><?php echo __('-- Please Select --') ?></option>
                <?php foreach ($customerGroups as $group): ?>
                    <option value="<?php echo $group['value'] ?>" <?php if (in_array($group['value'], $selectedCustomerGroups)) echo 'selected'; ?>><?php echo $group['label'] ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
    <div class="field website-grp">
        <label class="label" for="website"><?php echo __('Website') ?><span style="color: red;">*</span></label>
        <div class="control">
            <select id="website" name="websites[]" multiple="multiple" required>
                <option value=""><?php echo __('-- Please Select --') ?></option>
                <?php foreach ($websites as $website): ?>
                    <option value="<?php echo $website['value'] ?>" <?php if (in_array($website['value'], $selectedWebsites)) echo 'selected'; ?>><?php echo $website['label'] ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
    <div class="field">
        <label class="label" for="coupon-type"><?php echo __('Coupon Type') ?><span style="color: red;">*</span></label>
        <div class="control">
            <select id="coupon-type" name="coupon_type" required>
                <option value=""><?php echo __('-- Please Select --') ?></option>
                <option value="0" <?php if ($coupon_type == 1) echo 'selected'; ?>><?php echo __('Auto') ?></option>
                <option value="1" <?php if ($coupon_type == 2) echo 'selected'; ?>><?php echo __('Specific coupon') ?></option>
            </select>
        </div>
    </div>
    <div class="field">
        <label class="label" for="coupon-apply"><?php echo __('Coupon Apply') ?><span style="color: red;">*</span></label>
        <div class="control">
            <select id="coupon-apply" name="coupon_apply" required>
                <option value=""><?php echo __('-- Please Select --') ?></option>
                <option value="by_percent" <?php if ($coupon_apply == 'by_percent') echo 'selected'; ?>><?php echo __('Percentage') ?></option>
                <option value="by_fixed" <?php if ($coupon_apply == 'by_fixed') echo 'selected'; ?>><?php echo __('Amount') ?></option>
            </select>
        </div>
    </div>
    <div class="field">
        <label class="label" for="minimum-qty"><?php echo __('Minimum Qty') ?><span style="color: red;">*</span></label>
        <div class="control">
            <input type="number" id="minimum-qty" name="minimum_qty" value="<?php echo $minimum_qty; ?>" required>
        </div>
    </div>
    <div class="field">
        <label class="label" for="minimum-buy"><?php echo __('Minimum Buy') ?></label>
        <div class="control">
            <input type="number" id="minimum-buy" name="minimum_buy" value="<?php echo $minimum_buy; ?>">
        </div>
    </div>
    <div class="field">
        <label class="label" for="discount-amount"><?php echo __('Discount Amount') ?><span style="color: red;">*</span></label>
        <div class="control">
            <input type="number" id="discount-amount" name="discount_amount" value="<?php echo $discount_amount; ?>" required>
        </div>
    </div>
    <div class="field">
        <label class="label" for="apply-on-last"><?php echo __('Apply On Last') ?></label>
        
            <input type="checkbox" id="apply-on-last" name="apply_on_last" value="1" <?php if ($apply_on_last == 1) echo 'checked'; ?>>
        
    </div>
    <div class="field">
        <label class="label" for="expiry-date"><?php echo __('Expire Date') ?></label>
        <div class="control">
            <input type="date" id="expiry-date" name="expiry_date" value="<?php echo $expiry_date; ?>">
        </div>
    </div>
    <!-- Dropdown Container -->
    <div class="dropdown">
         <label for="selected-products-input">Select Products:</label>
         <input type="text" id="selected-products-input" class="form-control" placeholder="Select Products" readonly>
        <div class="dropdown-menu">
            <!-- Checkboxes for product selection -->
            <?php foreach ($productOptions as $option): ?>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="product_<?php echo $option['value'] ?>" name="selected_products[]" value="<?php echo $option['value'] ?>">
                    <label class="form-check-label" for="product_<?php echo $option['value'] ?>"><?php echo $option['label'] ?></label>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</form>
<style>
    .dropdown {
        position: relative;
    }

    .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1000;
        display: none;
        padding: 5px;
        background-color: #fff;
        border: 1px solid #ccc;
    }

    .dropdown-menu.show {
        display: block;
    }
</style>
<script type="text/javascript">
    var jsSkuValues = <?php echo json_encode($jsSkuValues); ?>;
    require(['jquery', 'jquery/ui'], function($){
        $(document).ready(function() {
            $('#selected-products-input').on('click', function() {
                $('.dropdown-menu').toggleClass('show');
            });

            $(document).on('click', function(e) {
                if (!$(e.target).closest('.dropdown').length) {
                    $('.dropdown-menu').removeClass('show');
                }
            });

            $('.form-check-input').on('change', function() {
                var selectedProducts = [];
                $('.form-check-input:checked').each(function() {
                    var label = $(this).next('.form-check-label').text(); // Get the label text
                    
                    selectedProducts.push(label);
                });
                $('#selected-products-input').val(selectedProducts.join(', '));
            });

            // Pre-select products based on SKU values
            jsSkuValues.forEach(function(sku) {
                $('.form-check-input[value="' + sku + '"]').prop('checked', true);
            });
        });
    });
</script>
