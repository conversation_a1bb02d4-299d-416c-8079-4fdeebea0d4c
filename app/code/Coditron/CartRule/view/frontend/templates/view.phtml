
<a href="<?php echo $block->getUrl('coditron_cartrule/comavesellercoupon/newaction'); ?>" class="action primary">
    <span><?php echo __('Add New Coupon'); ?></span>
</a>
<?php
    $collection = $block->getCollection();
    if ($collection->getSize()) {
?>
<div class="table-wrapper orders-history">
    <table class="data table table-order-items history" id="my-orders-table">
        <caption class="table-caption"><?php echo __('Seller Coupons') ?></caption>
        <thead>
        <tr>
            <th scope="col" class="col id"><?php echo __('ID') ?></th>
            <th scope="col" class="col ruleid"><?php echo __('Rule Id') ?></th>
            <th scope="col" class="col rule_name"><?php echo __('Rule Name') ?></th>
            <th scope="col" class="col rule_status"><?php echo __('Rule Status') ?></th>
            <th scope="col" class="col coupon_code"><?php echo __('Coupon Code') ?></th>
            <th scope="col" class="col coupon_amount"><?php echo __('Discount Amount') ?></th>
            <th scope="col" class="col minimum_qty"><?php echo __('Minimum Quantity') ?></th>
            <th scope="col" class="col apply_on_last"><?php echo __('Apply On Last') ?></th>
            <th scope="col" class="col expireat"><?php echo __('Expire At') ?></th>
            <th scope="col" class="col actions"><?php echo __('Actions') ?></th>
        </tr>
        </thead>
        <tbody>
            <?php foreach ($collection as $item) : 
                ?>
                <tr>
                    <td><?php echo $item->getComavesellerCouponId(); ?></td>
                    <td><?php echo $item->getRuleId(); ?></td>
                    <td><?php echo $item->getRuleName(); ?></td>
                    <td><?php echo $item->getRuleStatus(); ?></td>
                    <td><?php echo is_null($item->getCouponCode()) ? 'No Coupon Code' : $item->getCouponCode(); ?></td>
                    <td><?php echo $item->getCouponValue(); ?></td>
                    <td><?php echo $item->getCouponStep(); ?></td>
                    <td><?php echo $item->getApplyOnLast() == 1 ? __('Yes') : __('No'); ?></td>
                    <td><?php echo $item->getExpireAt(); ?></td>
                    <td>
                        <a href="<?php echo $block->getUrl('coditron_cartrule/comavesellercoupon/edit', ['id' => $item->getComavesellerCouponId()]); ?>"><?php echo __('Edit'); ?></a>
                        |
                        <a href="<?php echo $block->getUrl('coditron_cartrule/comavesellercoupon/delete', ['id' => $item->getComavesellerCouponId()]); ?>"><?php echo __('Delete'); ?></a>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
<?php
    } else {
        echo '<p>No data found.</p>';
    }
?>
