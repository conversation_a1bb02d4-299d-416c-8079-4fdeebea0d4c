define(
[
   'jquery',
   'Magento_Checkout/js/view/summary/abstract-total',
   'Magento_Checkout/js/model/quote',
   'Magento_Checkout/js/model/totals',
   'Magento_Catalog/js/price-utils'
],
function ($,Component,quote,totals,priceUtils) {
    "use strict";
    return Component.extend({
        defaults: {
            template: 'Coditron/CartRule/checkout/cart/totals/customdiscount'
        },
        totals: quote.getTotals(),
        
        isDisplayedCustomdiscountTotal : function () {
            return true;
        },
        getCustomdiscountTotal: function () {
            var customDiscount = 0;
            var totals = quote.totals();
            if (totals && totals.total_segments) {
                var totalSegments = totals.total_segments;
                for (var i = 0; i < totalSegments.length; i++) {
                    var segment = totalSegments[i];
                    if (segment.code === "custom_discount") {
                        customDiscount = segment.value;
                        break; // Exit the loop if custom discount is found
                    }
                }
            }
            return this.getFormattedPrice(customDiscount);
        }
     });
});