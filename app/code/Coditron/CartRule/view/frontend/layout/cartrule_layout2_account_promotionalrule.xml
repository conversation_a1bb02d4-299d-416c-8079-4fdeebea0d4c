<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="offers_styles"/>
    <body>
        <referenceBlock name="seller.page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Restaurant Offers</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="seller.content" >
           <block class="Coditron\CartRule\Block\CartRuleData" name="rule.view" template="Coditron_CartRule::view.phtml">
            </block>
        </referenceContainer>
    </body>
</page>
