<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Coditron\CartRule\Api\ComavesellerCouponRepositoryInterface" type="Coditron\CartRule\Model\ComavesellerCouponRepository"/>
	<preference for="Coditron\CartRule\Api\Data\ComavesellerCouponInterface" type="Coditron\CartRule\Model\ComavesellerCoupon"/>
	<preference for="Coditron\CartRule\Api\Data\ComavesellerCouponSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
	<virtualType name="Coditron\CartRule\Model\ResourceModel\ComavesellerCoupon\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
		<arguments>
			<argument name="mainTable" xsi:type="string">coditron_cartrule_comaveseller_coupon</argument>
			<argument name="resourceModel" xsi:type="string">Coditron\CartRule\Model\ResourceModel\ComavesellerCoupon\Collection</argument>
		</arguments>
	</virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
		<arguments>
			<argument name="collections" xsi:type="array">
				<item name="coditron_cartrule_comaveseller_coupon_listing_data_source" xsi:type="string">Coditron\CartRule\Model\ResourceModel\ComavesellerCoupon\Grid\Collection</item>
			</argument>
		</arguments>
	</type>
</config>
