<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
	<route url="/V1/coditron-cartrule/comaveseller_coupon" method="POST">
		<service class="Coditron\CartRule\Api\ComavesellerCouponRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Coditron_CartRule::comaveseller_coupon_save"/>
		</resources>
	</route>
	<route url="/V1/coditron-cartrule/comaveseller_coupon/search" method="GET">
		<service class="Coditron\CartRule\Api\ComavesellerCouponRepositoryInterface" method="getList"/>
		<resources>
			<resource ref="Coditron_CartRule::comaveseller_coupon_view"/>
		</resources>
	</route>
	<route url="/V1/coditron-cartrule/comaveseller_coupon/:comavesellerCouponId" method="GET">
		<service class="Coditron\CartRule\Api\ComavesellerCouponRepositoryInterface" method="get"/>
		<resources>
			<resource ref="Coditron_CartRule::comaveseller_coupon_view"/>
		</resources>
	</route>
	<route url="/V1/coditron-cartrule/comaveseller_coupon/:comavesellerCouponId" method="PUT">
		<service class="Coditron\CartRule\Api\ComavesellerCouponRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Coditron_CartRule::comaveseller_coupon_update"/>
		</resources>
	</route>
	<route url="/V1/coditron-cartrule/comaveseller_coupon/:comavesellerCouponId" method="DELETE">
		<service class="Coditron\CartRule\Api\ComavesellerCouponRepositoryInterface" method="deleteById"/>
		<resources>
			<resource ref="Coditron_CartRule::comaveseller_coupon_delete"/>
		</resources>
	</route>
</routes>
