<?php
namespace Coditron\CartRule\Block;

use Webkul\Marketplace\Model\ProductFactory;
use Webkul\Marketplace\Helper\Data;
class Products extends \Magento\Framework\View\Element\Template
{   
    protected $productCollectionFactory;
    protected $userContext;
    protected $productModel;
    protected $webkulHelper;

    public function __construct(
       \Magento\Framework\View\Element\Template\Context $context,
       \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory,
       \Magento\Authorization\Model\UserContextInterface $userContext,
        ProductFactory $productModel = null,
        Data $webkulHelper,
       array $data = []
    ){
       $this->productCollectionFactory = $productCollectionFactory;
       $this->userContext = $userContext;
       $this->webkulHelper = $webkulHelper;
       $this->productModel = $productModel ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(ProductFactory::class);
       parent::__construct($context, $data);
    }  
    public function toOptionArray()
    {
       $customerId = $this->webkulHelper->getCustomerId();
       $productIds = [];

       $sellerProductColl = $this->productModel->create()
                ->getCollection()
                ->addFieldToFilter(
                    'status',
                    ['eq' => 1]
                )
                ->addFieldToFilter('seller_id',['eq' => $customerId])
                ->addFieldToSelect('mageproduct_id')
                ->distinct(true);
        foreach ($sellerProductColl as $product) {
            $productIds[] = $product->getMageproductId();
        }
       $collection = $this->productCollectionFactory->create();
       $collection->addAttributeToSelect('*');
       $collection->addFieldToFilter('entity_id', ['in' => $productIds]);      
       $options = [];

        foreach ($collection as $product) {
            $options[] = ['label' => $product->getName(), 'value' => $product->getSku()];
        }
        return $options;
    }

    public function getSellerId()
    {
        return $this->webkulHelper->getCustomerId();
    }
}