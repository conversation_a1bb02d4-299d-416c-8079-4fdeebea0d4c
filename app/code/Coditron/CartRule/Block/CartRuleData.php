<?php

namespace Co<PERSON>ron\CartRule\Block;

use Magento\Framework\View\Element\Template;
use Magento\Backend\Block\Template\Context;
use Coditron\CartRule\Model\ResourceModel\ComavesellerCoupon\CollectionFactory;
use Magento\Framework\App\RequestInterface;
use Webkul\Marketplace\Helper\Data;
use Magento\Customer\Model\ResourceModel\Group\CollectionFactory as CustomerGroupCollectionFactory;
use Magento\Store\Model\StoreManagerInterface;

class CartRuleData extends Template
{

    public $collection;
    protected $request;
    protected $webkulHelper;
    protected $customerGroupCollectionFactory;
    protected $storeManager;

    public function __construct(
        Context $context, 
        CollectionFactory $collectionFactory,
        RequestInterface $request,
        Data $webkulHelper,
        CustomerGroupCollectionFactory $customerGroupCollectionFactory,
        StoreManagerInterface $storeManager,
        array $data = []
    )
    {
        $this->collection = $collectionFactory;
        $this->request = $request;
        $this->webkulHelper = $webkulHelper;
        $this->customerGroupCollectionFactory = $customerGroupCollectionFactory;
        $this->storeManager = $storeManager;
        parent::__construct($context, $data);
    }

    public function getRequest()
    {
        return $this->request;
    }

    public function getCollection()
    {
        $customerId = $this->webkulHelper->getCustomerId();
        $collection = $this->collection->create();
        $collection->addFieldToFilter('seller_name', $customerId);
        $collection->getSize();
        return $collection;
    }
    
    public function getCollectionById($entityId){

        $collection = $this->collection->create();
        $collection->addFieldToFilter('comaveseller_coupon_id', $entityId);
        $collection->getSelect()->join(
            ['sales_rule' => $collection->getTable('salesrule')],
            'main_table.rule_id = sales_rule.rule_id',
            ['*'] // or you can specify the fields you need from the sales rule table
        );
        return $collection;

    }

     public function getCustomerGroups()
    {
        $customerGroupCollection = $this->customerGroupCollectionFactory->create();
        return $customerGroupCollection->toOptionArray();
    }

    public function getWebsites()
    {
        $websites = $this->storeManager->getWebsites();
        $websiteOptions = [];
        foreach ($websites as $website) {
            $websiteOptions[] = [
                'value' => $website->getId(),
                'label' => $website->getName()
            ];
        }
        return $websiteOptions;
    }
}