<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Coditron\CartRule\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface ComavesellerCouponRepositoryInterface
{

    /**
     * Save comaveseller_coupon
     * @param \Coditron\CartRule\Api\Data\ComavesellerCouponInterface $comavesellerCoupon
     * @return \Coditron\CartRule\Api\Data\ComavesellerCouponInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Coditron\CartRule\Api\Data\ComavesellerCouponInterface $comavesellerCoupon
    );

    /**
     * Retrieve comaveseller_coupon
     * @param string $comavesellerCouponId
     * @return \Coditron\CartRule\Api\Data\ComavesellerCouponInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($comavesellerCouponId);

    /**
     * Retrieve comaveseller_coupon matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Coditron\CartRule\Api\Data\ComavesellerCouponSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete comaveseller_coupon
     * @param \Coditron\CartRule\Api\Data\ComavesellerCouponInterface $comavesellerCoupon
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Coditron\CartRule\Api\Data\ComavesellerCouponInterface $comavesellerCoupon
    );

    /**
     * Delete comaveseller_coupon by ID
     * @param string $comavesellerCouponId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($comavesellerCouponId);
}

