<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Coditron\CartRule\Api\Data;

interface ComavesellerCouponSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get comaveseller_coupon list.
     * @return \Coditron\CartRule\Api\Data\ComavesellerCouponInterface[]
     */
    public function getItems();

    /**
     * Set rule_id list.
     * @param \Coditron\CartRule\Api\Data\ComavesellerCouponInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

