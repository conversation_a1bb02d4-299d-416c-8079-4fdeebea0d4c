<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Coditron\CartRule\Api\Data;

interface ComavesellerCouponInterface
{

    const RULE_ID = 'rule_id';
    const RULE_STATUS = 'rule_status';
    const RULE_NAME = 'rule_name';
    const CONDITIONS_SERIALIZED = 'conditions_serialized';
    const COMAVESELLER_COUPON_ID = 'comaveseller_coupon_id';
    const CREATED_AT = 'created_at';
    const ACTIONS_SERIALIZED = 'actions_serialized';

    /**
     * Get comaveseller_coupon_id
     * @return string|null
     */
    public function getComavesellerCouponId();

    /**
     * Set comaveseller_coupon_id
     * @param string $comavesellerCouponId
     * @return \Coditron\CartRule\ComavesellerCoupon\Api\Data\ComavesellerCouponInterface
     */
    public function setComavesellerCouponId($comavesellerCouponId);

    /**
     * Get rule_id
     * @return string|null
     */
    public function getRuleId();

    /**
     * Set rule_id
     * @param string $ruleId
     * @return \Coditron\CartRule\ComavesellerCoupon\Api\Data\ComavesellerCouponInterface
     */
    public function setRuleId($ruleId);

    /**
     * Get rule_status
     * @return string|null
     */
    public function getRuleStatus();

    /**
     * Set rule_status
     * @param string $ruleStatus
     * @return \Coditron\CartRule\ComavesellerCoupon\Api\Data\ComavesellerCouponInterface
     */
    public function setRuleStatus($ruleStatus);

    /**
     * Get rule_name
     * @return string|null
     */
    public function getRuleName();

    /**
     * Set rule_name
     * @param string $ruleName
     * @return \Coditron\CartRule\ComavesellerCoupon\Api\Data\ComavesellerCouponInterface
     */
    public function setRuleName($ruleName);

    /**
     * Get conditions_serialized
     * @return string|null
     */
    public function getConditionsSerialized();

    /**
     * Set conditions_serialized
     * @param string $conditionsSerialized
     * @return \Coditron\CartRule\ComavesellerCoupon\Api\Data\ComavesellerCouponInterface
     */
    public function setConditionsSerialized($conditionsSerialized);

    /**
     * Get actions_serialized
     * @return string|null
     */
    public function getActionsSerialized();

    /**
     * Set actions_serialized
     * @param string $actionsSerialized
     * @return \Coditron\CartRule\ComavesellerCoupon\Api\Data\ComavesellerCouponInterface
     */
    public function setActionsSerialized($actionsSerialized);

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt();

    /**
     * Set created_at
     * @param string $createdAt
     * @return \Coditron\CartRule\ComavesellerCoupon\Api\Data\ComavesellerCouponInterface
     */
    public function setCreatedAt($createdAt);
}

