<?php

namespace Coditron\CartRule\Model\Total\Quote;

/**
    * @param \Magento\Quote\Model\Quote $quote
    * @param \Magento\Quote\Api\Data\ShippingAssignmentInterface $shippingAssignment
    * @param \Magento\Quote\Model\Quote\Address\Total $total
    * @return $this|bool
    */
use Magento\Quote\Model\Quote;
use Magento\Quote\Api\Data\ShippingAssignmentInterface;
use Magento\Quote\Model\Quote\Address\Total;
use Webkul\Marketplace\Model\ProductFactory as MpProductModel;
use Coditron\CartRule\Model\ComavesellerCouponFactory;
use Magento\SalesRule\Model\ResourceModel\Rule\CollectionFactory as RuleCollectionFactory;
use Magento\Framework\App\ObjectManager;
use Magento\Store\Model\StoreManagerInterface;

class Custom extends \Magento\Quote\Model\Quote\Address\Total\AbstractTotal
{
   /**
    * @var \Magento\Framework\Pricing\PriceCurrencyInterface
    */
   protected $_priceCurrency;
   protected $mpProductModel;
   protected $comavesellerCouponFactory;
   protected $ruleCollectionFactory;
   protected $date;
   protected $storeManager;
   /**
    * Custom constructor.
    * @param \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency
    */
   public function __construct(
       \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency,
       MpProductModel $mpProductModel = null,
       ComavesellerCouponFactory $comavesellerCouponFactory,
       RuleCollectionFactory $ruleCollectionFactory,
       \Magento\Framework\Stdlib\DateTime\DateTime $date,
       StoreManagerInterface $storeManager
   ){
       $this->_priceCurrency = $priceCurrency;
       $this->mpProductModel = $mpProductModel ?: ObjectManager::getInstance()
            ->create(MpProductModel::class);
       $this->comavesellerCouponFactory = $comavesellerCouponFactory;
       $this->ruleCollectionFactory = $ruleCollectionFactory;
       $this->date = $date;
       $this->storeManager = $storeManager;
   }
   
   public function collect(
       Quote $quote,
       ShippingAssignmentInterface $shippingAssignment,
       Total $total
   )
   {
        parent::collect($quote, $shippingAssignment, $total);

        // Get quote items
        $items = $quote->getAllItems();
        // Calculate total quantity of quote items
        $websiteId = $this->storeManager->getStore()->getWebsiteId();
        $customerGroupId = $quote->getCustomerGroupId();
        $totalQty = 0;
        $discountValue = 0;
        $globalBaseDiscount = 0;
        $flag = 0;
        $currentDate = $this->date->gmtDate('Y-m-d');
         // Log product IDs of quote items and calculate total quantity
        foreach ($items as $item) {
            $totalQty += $item->getQty();
            $productId = $item->getProductId();
        }

        // Log product IDs of quote items
        foreach ($items as $item) {
            $productId = $item->getProductId();
            // Get Seller Id
            $queryData = $this->mpProductModel->create()
                ->getCollection()
                ->addFieldToFilter('mageproduct_id', ['eq' => $productId])
                ->addFieldToFilter('status', ['eq' => 1])
                ->addFieldToSelect('seller_id')
                ->setOrder('mageproduct_id');

            $proSellerId = '';
            foreach ($queryData as $proData) {
                $proSellerId = $proData->getSellerId();   
            }

            $rules = $this->getSalesRules($proSellerId);

            if(sizeof($rules)>0){
                foreach ($rules as $rule) {
                    $currentRuleId = $rule->getData('rule_id');
                    $toDate = $rule->getData('to_date');
                    
                    if ($rule->getConditions()->validate($item)) {
                        $ruleIdCollection = $this->comavesellerCouponFactory->create()->getCollection()
                                            ->addFieldToSelect("apply_on_last")
                                            ->addFieldToFilter("rule_id", $currentRuleId)
                                            ->load();
                        $apply_on_last = $ruleIdCollection->getFirstItem()->getData('apply_on_last');
                        $minimum_qty = $rule->getDiscountStep();
                        $discountAmount = $rule->getDiscountAmount();
                        $customerGroupIds = $rule->getData('customer_group_ids');
                        $websiteIds = $rule->getData('website_ids');
                        $isActive = $rule->getData('is_active');
                        $discountValue = $discountAmount;
                        $discountType =  $rule->getSimpleAction();
                       
                        // Get shipping address from shipping assignment
                        $shippingAddress = $shippingAssignment->getShipping()->getAddress();
                        
                        // Check address type
                        $addressType = $shippingAddress->getAddressType();
                        
                        if ($addressType == 'billing') {
                           
                            if($totalQty >= $minimum_qty && strtotime($currentDate) <= strtotime($toDate) && in_array($customerGroupId, $customerGroupIds) && in_array($websiteId, $websiteIds) && $isActive == 1){
                                if($apply_on_last == 1 && $flag == 0){
                                 // Find the item with the lowest total price
                                $lowestPriceItem = null;
                                $lowestPriceItemTotalPrice = null;

                                foreach ($items as $compareItem) {
                                    $compareItemTotalPrice = $compareItem->getPrice() * $compareItem->getQty();
                                    if ($lowestPriceItem === null || $compareItemTotalPrice < $lowestPriceItemTotalPrice) {
                                        $lowestPriceItem = $compareItem;
                                        $lowestPriceItemTotalPrice = $compareItemTotalPrice;
                                    }
                                }
                                if ($lowestPriceItem) {
                                    // Apply discount to the item with the lowest price
                                    $itemPrice = $lowestPriceItem->getPrice();
                                    
                                    // Calculate discount based on the rule's discount type
                                    if ($discountType == 'by_percent') {
                                        $discountAmount = ($itemPrice * $discountAmount) / 100;
                                    } 
                                        $baseDiscount = $discountAmount;

                                    // Apply the discount
                                    $globalBaseDiscount += $baseDiscount;
                                    $this->applyDiscount($baseDiscount, $total, $quote, $discountValue);
                                    $flag = 1;
                                }

                            }else if($apply_on_last == 0){
                                if($discountType =='by_percent'){
                                    $discountAmount = ($item->getPrice() * $discountAmount) / 100;
                                }
                                $baseDiscount = $discountAmount;
                                // Apply discount based on item quantity
                                $itemQty = $item->getQty();
                                while ($itemQty > 0) {
                                    $globalBaseDiscount += $baseDiscount;
                                    $this->applyDiscount($baseDiscount, $total, $quote, $discountValue);
                                    $itemQty--; // Decrease the item quantity
                                }
                            }

                            $quote->setCustomData($globalBaseDiscount);
                            }else{
                                $quote->setCustomData($globalBaseDiscount);
                            }
                        }
                    }
                }
            }else{
                $quote->setCustomData($globalBaseDiscount);
            }
        }
        return $this;
   }

   protected function applydiscount($baseDiscount, $total, $quote, $discountValue){
        $discount =  $this->_priceCurrency->convert($baseDiscount);
        $total->addTotalAmount('customdiscount', -$discount);
        $total->addBaseTotalAmount('customdiscount', -$baseDiscount);
        $total->setBaseGrandTotal($total->getBaseGrandTotal() - $baseDiscount);
        $quote->setCustomDiscount(-$discount);

    }

   public function fetch(
    \Magento\Quote\Model\Quote $quote,
    \Magento\Quote\Model\Quote\Address\Total $total
    ) {
        
        // Access your custom data
        $discountAmount = $quote->getCustomData();
        return [
            'code' => 'custom_discount',
            'title' => __('Custom Discount'),
            'value' => -$discountAmount
        ];
    }
   
    protected function getSalesRules($proSellerId)
    {
        // Get all rule IDs for the given seller
        $ruleIdCollection = $this->comavesellerCouponFactory->create()->getCollection()
            ->addFieldToSelect("rule_id")
            ->addFieldToFilter("seller_name", $proSellerId)
            ->load();

        // Extract all rule IDs from the collection
        $ruleIds = [];
        foreach ($ruleIdCollection as $item) {
            $ruleIds[] = $item->getData('rule_id');
        }

        // If there are no rule IDs, return an empty array
        if (empty($ruleIds)) {
            return [];
        }

        // Get the rules based on the collected rule IDs
        $collection = $this->ruleCollectionFactory->create()
            ->addFieldToFilter('is_active', 1)
            ->addFieldToFilter('rule_id', ['in' => $ruleIds])
            ->load();

        return $collection->getItems();
    }
}