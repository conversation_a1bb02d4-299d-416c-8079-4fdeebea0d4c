<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Coditron\CartRule\Model;

use Coditron\CartRule\Api\ComavesellerCouponRepositoryInterface;
use Coditron\CartRule\Api\Data\ComavesellerCouponInterface;
use Coditron\CartRule\Api\Data\ComavesellerCouponInterfaceFactory;
use Coditron\CartRule\Api\Data\ComavesellerCouponSearchResultsInterfaceFactory;
use Coditron\CartRule\Model\ResourceModel\ComavesellerCoupon as ResourceComavesellerCoupon;
use Coditron\CartRule\Model\ResourceModel\ComavesellerCoupon\CollectionFactory as ComavesellerCouponCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class ComavesellerCouponRepository implements ComavesellerCouponRepositoryInterface
{

    /**
     * @var ResourceComavesellerCoupon
     */
    protected $resource;

    /**
     * @var ComavesellerCouponInterfaceFactory
     */
    protected $comavesellerCouponFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;

    /**
     * @var ComavesellerCoupon
     */
    protected $searchResultsFactory;

    /**
     * @var ComavesellerCouponCollectionFactory
     */
    protected $comavesellerCouponCollectionFactory;


    /**
     * @param ResourceComavesellerCoupon $resource
     * @param ComavesellerCouponInterfaceFactory $comavesellerCouponFactory
     * @param ComavesellerCouponCollectionFactory $comavesellerCouponCollectionFactory
     * @param ComavesellerCouponSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        ResourceComavesellerCoupon $resource,
        ComavesellerCouponInterfaceFactory $comavesellerCouponFactory,
        ComavesellerCouponCollectionFactory $comavesellerCouponCollectionFactory,
        ComavesellerCouponSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->comavesellerCouponFactory = $comavesellerCouponFactory;
        $this->comavesellerCouponCollectionFactory = $comavesellerCouponCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(
        ComavesellerCouponInterface $comavesellerCoupon
    ) {
        try {
            $this->resource->save($comavesellerCoupon);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the comavesellerCoupon: %1',
                $exception->getMessage()
            ));
        }
        return $comavesellerCoupon;
    }

    /**
     * @inheritDoc
     */
    public function get($comavesellerCouponId)
    {
        $comavesellerCoupon = $this->comavesellerCouponFactory->create();
        $this->resource->load($comavesellerCoupon, $comavesellerCouponId);
        if (!$comavesellerCoupon->getId()) {
            throw new NoSuchEntityException(__('comaveseller_coupon with id "%1" does not exist.', $comavesellerCouponId));
        }
        return $comavesellerCoupon;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->comavesellerCouponCollectionFactory->create();
        
        $this->collectionProcessor->process($criteria, $collection);
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);
        
        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }
        
        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(
        ComavesellerCouponInterface $comavesellerCoupon
    ) {
        try {
            $comavesellerCouponModel = $this->comavesellerCouponFactory->create();
            $this->resource->load($comavesellerCouponModel, $comavesellerCoupon->getComavesellerCouponId());
            $this->resource->delete($comavesellerCouponModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the comaveseller_coupon: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($comavesellerCouponId)
    {
        return $this->delete($this->get($comavesellerCouponId));
    }
}

