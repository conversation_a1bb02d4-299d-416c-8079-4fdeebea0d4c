<?php
namespace Coditron\CartRule\Model\Source;


use Webkul\Marketplace\Model\ProductFactory;
use Webkul\Marketplace\Helper\Data;
class Products implements \Magento\Framework\Option\ArrayInterface {

    protected $_productCollectionFactory;
    protected $customerSession;
    protected $userContext;
    protected $productModel;
    protected $webkulHelper;

    public function __construct(
        \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Authorization\Model\UserContextInterface $userContext,
        ProductFactory $productModel = null,
        Data $webkulHelper
    ) {

        $this->_productCollectionFactory = $productCollectionFactory;
        $this->customerSession = $customerSession;
        $this->userContext = $userContext;
        $this->webkulHelper = $webkulHelper;
        $this->productModel = $productModel ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(ProductFactory::class);
        
    }

    public function toOptionArray()
    {
        $customerId = $this->webkulHelper->getCustomerId();
        $productIds = [];
        $sellerProductColl = $this->productModel->create()
                ->getCollection()
                ->addFieldToFilter(
                    'status',
                    ['eq' => 1]
                )
                ->addFieldToFilter('seller_id',['eq' => $customerId])
                ->addFieldToSelect('mageproduct_id')
                ->distinct(true);
        foreach ($sellerProductColl as $product) {
            $productIds[] = $product->getMageproductId();
        }
        $collection = $this->_productCollectionFactory->create();
        $collection->addFieldToFilter('entity_id', ['in' => $productIds]);
        $options = [];

        foreach ($collection as $product) {
            $options[] = ['label' => $product->getSku(), 'value' => $product->getSku()];
        }
      
        return $options;
    }

}