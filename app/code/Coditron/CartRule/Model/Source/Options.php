<?php 

namespace Coditron\CartRule\Model\Source;

use Magento\Framework\Data\OptionSourceInterface;


class Options implements OptionSourceInterface
{
   
  public function toOptionArray(){
    
         return [
            ['label' => __('-- Please Select --'), 'value' => ''],
            ['label' => __('Auto'), 'value' => '0'],
            ['label' => __('Specific coupon'), 'value' => '1'],
        ];
    }
}