<?php 

namespace Coditron\CartRule\Model\Source;

use Magento\Framework\Data\OptionSourceInterface;


class CouponOptions implements OptionSourceInterface
{
   
  public function toOptionArray(){
    
         return [
            ['label' => __('-- Please Select --'), 'value' => ''],
            ['label' => __('Percentage'), 'value' => 'by_percent'],
            ['label' => __('Amount'), 'value' => 'by_fixed'],
        ];
    }
}