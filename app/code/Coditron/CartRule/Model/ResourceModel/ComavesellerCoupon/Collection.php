<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Coditron\CartRule\Model\ResourceModel\ComavesellerCoupon;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'comaveseller_coupon_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \Coditron\CartRule\Model\ComavesellerCoupon::class,
            \Coditron\CartRule\Model\ResourceModel\ComavesellerCoupon::class
        );
    }
}

