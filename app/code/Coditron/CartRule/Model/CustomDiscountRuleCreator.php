<?php

namespace Coditron\CartRule\Model;

use Magento\SalesRule\Model\RuleFactory;
use Magento\SalesRule\Model\ResourceModel\Rule as RuleResource;
use Magento\SalesRule\Model\CouponGenerator;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\SalesRule\Api\CouponRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;

class CustomDiscountRuleCreator
{
    protected $ruleFactory;
    protected $ruleResource;
    protected $couponGenerator;
    protected $searchCriteriaBuilder;
    protected $couponRepository;

    public function __construct(
        RuleFactory $ruleFactory,
        RuleResource $ruleResource,
        CouponGenerator $couponGenerator,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        CouponRepositoryInterface $couponRepository
    ) {
        $this->ruleFactory = $ruleFactory;
        $this->ruleResource = $ruleResource;
        $this->couponGenerator = $couponGenerator;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->couponRepository = $couponRepository;
    }

    public function createCustomDiscountRule($data)
    {
        $qty = 1;
        $length = 10;
        $couponCode = null;

        if ($data['coupon_type'] == 0) {
            $couponType = \Magento\SalesRule\Model\Rule::COUPON_TYPE_NO_COUPON;
        } else {
            $couponType = \Magento\SalesRule\Model\Rule::COUPON_TYPE_SPECIFIC;
        }
       
        if ((isset($data['rule']['conditions']['1--1']['value']) && !empty($data['rule']['conditions']['1--1']['value'])) || (isset($data['selected_products']) && !empty($data['selected_products']))) {
            if (isset($data['rule']['conditions']['1--1']['value']) && !empty($data['rule']['conditions']['1--1']['value'])) {
                $skuArray = array_map('trim', explode(',', $data['rule']['conditions']['1--1']['value']));
            } elseif (isset($data['selected_products']) && is_array($data['selected_products']) && !empty($data['selected_products'])) {
                $skuArray = array_map('trim', $data['selected_products']);
            }
            
            // Check if entity_id is provided and load existing rule
            if (isset($data['rule_id']) && !empty($data['rule_id'])) {
               
                $rule = $this->ruleFactory->create()->load($data['rule_id']);
                if (!$rule->getId()) {
                    throw new \Magento\Framework\Exception\LocalizedException(__('The sales rule with the specified ID does not exist.'));
                }
            } else {
                $rule = $this->ruleFactory->create();
            }
                // Get the customer group IDs from $data
                $customerGroupIds = isset($data['customer_group']) ? $data['customer_group'] : [0, 1, 2, 3]; // Default to all groups if not set
                //Get the Website Ids from data
                $websiteIds = isset($data['websites']) ? $data['websites'] : [1];
                $rule->setName($data['rule_name'])
                ->setDescription('Discount on specific SKUs')
                ->setIsActive($data['rule_status'])
                ->setWebsiteIds($websiteIds) // Adjust as needed
                ->setCustomerGroupIds($customerGroupIds) // Adjust as needed
                ->setFromDate('')
                ->setToDate($data['expiry_date'])
                ->setUsesPerCustomer(0)
                ->setIsAdvanced(1)
                ->setSortOrder(0)
                ->setSimpleAction($data['coupon_apply'])
                ->setDiscountAmount($data['discount_amount'])
                ->setDiscountQty($data['minimum_buy'])
                ->setDiscountStep($data['minimum_qty'])
                ->setSimpleFreeShipping(0)
                ->setApplyToShipping(0)
                ->setTimesUsed(0)
                ->setIsRss(0)
                ->setCouponType($couponType)
                ->setUseAutoGeneration(1);

            $conditions = [
                'type' => \Magento\SalesRule\Model\Rule\Condition\Product\Combine::class,
                'attribute' => null,
                'operator' => null,
                'value' => '1',
                'is_value_processed' => null,
                'aggregator' => 'any', // 'all' for AND, 'any' for OR
                'conditions' => []
            ];

            foreach ($skuArray as $sku) {
                $conditions['conditions'][] = [
                    'type' => \Magento\SalesRule\Model\Rule\Condition\Product::class,
                    'attribute' => 'sku',
                    'operator' => '==',
                    'value' => $sku
                ];
            }

            $rule->getConditions()->setConditions([]);
            $rule->getConditions()->loadArray($conditions);
            $rule->getActions()->setConditions([]);
            $rule->getActions()->loadArray($conditions);

            $this->ruleResource->save($rule);
            $ruleId = $rule->getId();

            if ($couponType == \Magento\SalesRule\Model\Rule::COUPON_TYPE_SPECIFIC) {
                $params = ['length' => $length, 'prefix' => strip_tags($data['rule_name'])];
                $couponCode = $this->generateCouponCode($qty, $ruleId, $params);
            }
        }

        if ($couponCode) {
            return ['rule_id' => $ruleId, 'coupon_code' => $couponCode];
        } else {
            return ['rule_id' => $ruleId];
        }
    }

    private function generateCouponCode(int $qty, int $ruleId, array $params = [])
    {
        if (!$qty || !$ruleId) {
            return false;
        }
        $couponArray = [];
        // Search for existing coupons with the given ruleId
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('rule_id', $ruleId, 'eq')
            ->create();

        $couponList = $this->couponRepository->getList($searchCriteria);
        $coupons = $couponList->getItems();
        
        if (!empty($coupons)) {
           
            foreach ($coupons as $coupon) {
                $couponArray[] = $coupon->getCode();
                return $couponArray;
            }
            
        } else {
            // Create new coupon
            $params['rule_id'] = $ruleId;
            $params['qty'] = $qty;
            $coupons = $this->couponGenerator->generateCodes($params);
            return $coupons;
        }
    }
}
