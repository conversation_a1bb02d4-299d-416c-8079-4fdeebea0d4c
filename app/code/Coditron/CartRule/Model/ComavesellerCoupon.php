<?php

namespace Coditron\CartRule\Model;

use Coditron\CartRule\Model\ResourceModel\ComavesellerCoupon as ComavesellerCouponResourceModel;
use Magento\Quote\Model\Quote\Address;
use Magento\Rule\Model\AbstractModel;

class ComavesellerCoupon extends AbstractModel
{
    protected $_eventPrefix = 'coditron_cartRule';
    protected $_eventObject = 'rule';
    protected $condCombineFactory;
    protected $condProdCombineF;
    protected $validatedAddresses = [];
    protected $_selectProductIds;
    protected $_displayProductIds;
    // Define constants for the field names
    const COMAVESELLER_COUPON_ID = 'comaveseller_coupon_id';
    const RULE_ID = 'rule_id';
    const SELLER_NAME = 'seller_name';
    const RULE_STATUS = 'rule_status';
    const RULE_NAME = 'rule_name';
    const COUPON_TYPE = 'coupon_type';
    const COUPON_APPLY = 'coupon_apply';
    const MINIMUM_QTY = 'minimum_qty';
    const MINIMUM_BUY = 'minimum_buy';
    const DISCOUNT_AMOUNT = 'discount_amount';
    const APPLY_ON_LAST = 'apply_on_last';
    const EXPIRY_DATE = 'expiry_date';
    const CONDITIONS_SERIALIZED = 'conditions_serialized';

    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\FormFactory $formFactory,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $localeDate,
        \Magento\CatalogRule\Model\Rule\Condition\CombineFactory $condCombineFactory,
        \Magento\SalesRule\Model\Rule\Condition\Product\CombineFactory $condProdCombineF,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        $this->condCombineFactory = $condCombineFactory;
        $this->condProdCombineF = $condProdCombineF;
        parent::__construct($context, $registry, $formFactory, $localeDate, $resource, $resourceCollection, $data);
    }

    protected function _construct()
    {
        parent::_construct();
        $this->_init(ComavesellerCouponResourceModel::class);
        $this->setIdFieldName('rule_id');
    }

    public function getConditionsInstance()
    {
        return $this->condCombineFactory->create();
    }

    public function getActionsInstance()
    {
        return $this->condCombineFactory->create();
    }

    public function hasIsValidForAddress($address)
    {
        $addressId = $this->_getAddressId($address);
        return isset($this->validatedAddresses[$addressId]) ? true : false;
    }

    public function setIsValidForAddress($address, $validationResult)
    {
        $addressId = $this->_getAddressId($address);
        $this->validatedAddresses[$addressId] = $validationResult;
        return $this;
    }

    public function getIsValidForAddress($address)
    {
        $addressId = $this->_getAddressId($address);
        return isset($this->validatedAddresses[$addressId]) ? $this->validatedAddresses[$addressId] : false;
    }

    private function _getAddressId($address)
    {
        if ($address instanceof Address) {
            return $address->getId();
        }
        return $address;
    }

    public function getConditionsFieldSetId($formName = '')
    {
        return $formName . 'rule_conditions_fieldset_' . $this->getId();
    }

    public function getActionFieldSetId($formName = '')
    {
        return $formName . 'rule_actions_fieldset_' . $this->getId();
    }

    public function getMatchProductIds()
    {
        $productCollection = \Magento\Framework\App\ObjectManager::getInstance()->create(
            '\Magento\Catalog\Model\ResourceModel\Product\Collection'
        );
        $productFactory = \Magento\Framework\App\ObjectManager::getInstance()->create(
            '\Magento\Catalog\Model\ProductFactory'
        );
        $this->_selectProductIds = [];
        $this->setCollectedAttributes([]);
        $this->getConditions()->collectValidatedAttributes($productCollection);
        \Magento\Framework\App\ObjectManager::getInstance()->create(
            '\Magento\Framework\Model\ResourceModel\Iterator'
        )->walk(
            $productCollection->getSelect(),
            [[$this, 'callbackValidateProductCondition']],
            [
                'attributes' => $this->getCollectedAttributes(),
                'product' => $productFactory->create(),
            ]
        );
        return $this->_selectProductIds;
    }

    public function callbackValidateProductCondition($args)
    {
        $product = clone $args['product'];
        $product->setData($args['row']);
        $websites = $this->_getWebsitesMap();
        foreach ($websites as $websiteId => $defaultStoreId) {
            $product->setStoreId($defaultStoreId);
            if ($this->getConditions()->validate($product)) {
                $this->_selectProductIds[] = $product->getId();
            }
        }
    }

    protected function _getWebsitesMap()
    {
        $map = [];
        $websites = \Magento\Framework\App\ObjectManager::getInstance()->create(
            '\Magento\Store\Model\StoreManagerInterface'
        )->getWebsites();
        foreach ($websites as $website) {
            if ($website->getDefaultStore() === null) {
                continue;
            }
            $map[$website->getId()] = $website->getDefaultStore()->getId();
        }
        return $map;
    }

     // Rest of your interface methods here
    public function getComavesellerCouponId()
    {
        return $this->getData(self::COMAVESELLER_COUPON_ID);
    }

    public function setComavesellerCouponId($comavesellerCouponId)
    {
        return $this->setData(self::COMAVESELLER_COUPON_ID, $comavesellerCouponId);
    }

    public function getRuleId()
    {
        return $this->getData('rule_id');
    }

    public function setRuleId($ruleId)
    {
        return $this->setData('rule_id', $ruleId);
    }

    public function getRuleStatus()
    {
        return $this->getData(self::RULE_STATUS);
    }

    public function setRuleStatus($ruleStatus)
    {
        return $this->setData(self::RULE_STATUS, $ruleStatus);
    }

    public function getRuleName()
    {
        return $this->getData(self::RULE_NAME);
    }

    public function setRuleName($ruleName)
    {
        return $this->setData(self::RULE_NAME, $ruleName);
    }

    public function getCouponType()
    {
        return $this->getData(self::COUPON_TYPE);
    }

    public function setCouponType($couponType)
    {
        return $this->setData(self::COUPON_TYPE, $couponType);
    }

    public function getCouponApply()
    {
        return $this->getData(self::COUPON_APPLY);
    }

    public function setCouponApply($couponApply)
    {
        return $this->setData(self::COUPON_APPLY, $couponApply);
    }

    public function getMinimumQty()
    {
        return $this->getData(self::MINIMUM_QTY);
    }

    public function setMinimumQty($minimumQty)
    {
        return $this->setData(self::MINIMUM_QTY, $minimumQty);
    }

    public function getMinimumBuy()
    {
        return $this->getData(self::MINIMUM_BUY);
    }

    public function setMinimumBuy($minimumBuy)
    {
        return $this->setData(self::MINIMUM_BUY, $minimumBuy);
    }

    public function getDiscountAmount()
    {
        return $this->getData(self::DISCOUNT_AMOUNT);
    }

    public function setDiscountAmount($discountAmount)
    {
        return $this->setData(self::DISCOUNT_AMOUNT, $discountAmount);
    }

    public function getApplyOnLast()
    {
        return $this->getData(self::APPLY_ON_LAST);
    }

    public function setApplyOnLast($applyOnLast)
    {
        return $this->setData(self::APPLY_ON_LAST, $applyOnLast);
    }

    public function getExpiryDate()
    {
        return $this->getData(self::EXPIRY_DATE);
    }

    public function setExpiryDate($expiryDate)
    {
        return $this->setData(self::EXPIRY_DATE, $expiryDate);
    }

    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    public function loadByColumn($column, $value)
    {
        $this->_getResource()->load($this, $value, $column);
        return $this;
    }
}