<?php
namespace Coditron\CartRule\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;
use Webkul\Marketplace\Model\ProductFactory;

class Sellers implements OptionSourceInterface
{
    /**
     * @var \Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory
     */
    protected $_sellerlistCollectionFactory;

    /**
     * @var ProductFactory
     */
    protected $productModel;

    public function __construct(
        \Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory $sellerlistCollectionFactory,
         ProductFactory $productModel = null
    )
    {
        $this->_sellerlistCollectionFactory = $sellerlistCollectionFactory;
        $this->productModel = $productModel ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(ProductFactory::class);
    }

    public function toOptionArray()
    {
        $sellerArr = [];
        $options = [];
        $options[] = [
            'value' => '',
            'label' => 'Please Select'
        ];
        $sellerProductColl = $this->productModel->create()
            ->getCollection()
            ->addFieldToFilter(
                'status',
                ['eq' => 1]
            )
            ->addFieldToSelect('seller_id')
            ->distinct(true);
        $sellerArr = $sellerProductColl->getAllSellerIds();
        $storeCollection = $this->_sellerlistCollectionFactory->create()
                ->addFieldToSelect(
                    '*'
                )
                ->addFieldToFilter(
                    'seller_id',
                    ['in' => $sellerArr]
                )
                ->addFieldToFilter(
                    'is_seller',
                    ['eq' => 1]
                );
            $storeSellerIDs = $storeCollection->getAllIds();
            $storeMainSellerIDs = $storeCollection->getAllSellerIds();

            $sellerArr = array_diff($sellerArr, $storeMainSellerIDs);

            $adminStoreCollection = $this->_sellerlistCollectionFactory
            ->create()
            ->addFieldToSelect(
                '*'
            )->addFieldToFilter(
                'seller_id',
                ['in' => $sellerArr]
            );
            if (!empty($storeSellerIDs)) {
                $adminStoreCollection->addFieldToFilter(
                    'entity_id',
                    ['nin' => $storeSellerIDs]
                );
            }
            $adminStoreCollection->addFieldToFilter(
                'is_seller',
                ['eq' => 1]
            )->addFieldToFilter(
                'store_id',
                0
            )->setOrder(
                'entity_id',
                'desc'
            );
            $adminStoreSellerIDs = $adminStoreCollection->getAllIds();
            $allSellerIDs = array_merge($storeSellerIDs, $adminStoreSellerIDs);

            $collection = $this->_sellerlistCollectionFactory->create()
                ->addFieldToSelect(
                    '*'
                )->addFieldToFilter(
                    'is_seller',
                    ['eq' => 1]
                );

        foreach ($collection as $seller) {
            $options[] = [
                'value' => $seller->getSellerId(),
                'label' => $seller->getShopUrl()
            ];

        }
        return $options;
    }
}
