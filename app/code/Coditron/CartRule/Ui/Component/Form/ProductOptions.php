<?php

namespace Coditron\CartRule\Ui\Component\Form;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\Data\OptionSourceInterface;

class ProductOptions implements OptionSourceInterface
{
    protected $productRepository;

    public function __construct(ProductRepositoryInterface $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    public function toOptionArray()
    {
        $options = [];
        $products = $this->productRepository->getList();

        foreach ($products->getItems() as $product) {
            $options[] = [
                'label' => $product->getName(),
                'value' => $product->getSku()
            ];
        }

        return $options;
    }
}
