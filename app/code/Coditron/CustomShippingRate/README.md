
As a Magento / Adobe Commerce merchant completing with other major brands, providing simple flat rate shipping is essential to your business success. Our Magento 2 custom flat shipping method extension adds two essential functionality to your Magento store which gives you fully take control your store shipping options by providing the ability to quickly add custom flat fee shipping rates to both admin order creation or display a simple list of flat rates to your website customers. So whether you are using United States Postal Services, United Parcel Service, Federal Express, DHL or any other carriers, our Magento2 shipping extension makes it easy for you to quickly add simple flat rate shipping fees to your Magento site.

### Custom Shipping Rate for Admin Order
Whether you are creating a new order or canceling and rewriting existing orders in Magento Admin, our admin shipping extension gives you the ability to apply a custom shipping rate, method, and description to any order. This free extension is essential for businesses that do a lot of phone orders or mail orders and want to offer special shipping cost for individual customers. With our admin shipping plugin extension changing shipping amount for a particular order is as easy as entering the shipping amount instead of choosing predefined standard shipping rates and invoice your customer as you would with any standard shipping rate.

### Order Package Tracking
Want to add FedEx, UPS, USPS or DHL order package tracking to your Magento store? Learn more about our extension. Improve your average customer acquisition cost by upselling more products and services to each customer.

### Custom Shipping Rate for Admin Order
Whether you are creating a new order or canceling and rewriting existing orders in Magento Admin, our admin shipping extension gives you the ability to apply a custom shipping rate, method, and description to any order. This free extension is essential for businesses that do a lot of phone orders or mail orders and want to offer special shipping cost for individual customers. With our admin shipping plugin extension changing shipping amount for a particular order is as easy as entering the shipping amount instead of choosing predefined standard shipping rates and invoice your customer as you would with any standard shipping rate.


### Frontend Shipping Methods
Instead of using complex shipping table rates, our extension simplifies your checkout process by giving you the ability to create an unlimited number of flat price shipping methods. Perfect for e-commerce stores that offer flat shipping price for all their products. Improve your order conversion rate by offering your customers a simple checkout process using our shipping extension


### How to Configure Magento / Adobe Commerce Shipping Rate
Setting up our shipping method is as simple as entering four options for your different rate.



### Installation

##### Using Composer (recommended)

```
composer require coditron/magento2-customshippingrate
```

Support
---
If you encounter any problems or bugs, please contact to coditron

Need help setting up or want to customize this extension to meet your business needs? <NAME_EMAIL> and if we like your idea we will add this feature for free or at a discounted rate.


© Coditron Technologies | [www.coditron.com](https://www.coditron.com)
