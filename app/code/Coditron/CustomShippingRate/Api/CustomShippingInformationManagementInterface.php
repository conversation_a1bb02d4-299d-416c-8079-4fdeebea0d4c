<?php
namespace Coditron\CustomShippingRate\Api;

/**
 * @api
 */
interface CustomShippingInformationManagementInterface
{
    /**
     * Get shipping information for a specified cart.
     *
     * @param string $cartId The cart ID (can be numeric or a masked ID string)
     * @return \Coditron\CustomShippingRate\Api\Data\CustomShippingInformationInterface
     */
    public function getShippingInformation($cartId);
}
