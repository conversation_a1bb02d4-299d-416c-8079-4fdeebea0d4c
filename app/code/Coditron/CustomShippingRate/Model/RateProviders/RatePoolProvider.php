<?php

declare(strict_types=1);

namespace Coditron\CustomShippingRate\Model\RateProviders;

use Coditron\CustomShippingRate\Api\ShippingRateProviderInterface;
use Coditron\CustomShippingRate\Api\ShippingRateProviderInterfaceFactory;
use Psr\Log\LoggerInterface;

class RatePoolProvider
{
    /**
     * @param LoggerInterface $logger
     * @param RatesByServiceFactory $defaultProviderFactory
     * @param ShippingRateProviderInterfaceFactory[] $providers
     */
    public function __construct(
        private readonly RatesByServiceFactory $defaultProviderFactory,
        private readonly LoggerInterface $logger,
        private readonly array $providers = [],
    ) {
    }

    /**
     * @param string $sellerKey
     * @param array $data
     * @return ShippingRateProviderInterface
     */
    public function getBySeller(string $sellerKey, array $data = []): ShippingRateProviderInterface
    {
        $sellerKey = strtolower(
            preg_replace('/_\d+/', '', $sellerKey)
        );

        if (isset($this->providers[$sellerKey])) {
            $providerFactory = $this->providers[$sellerKey];
            $this->logger->info(
                '[CoditronRates] Found rate provider by seller key',
                [
                    'sellerKey' => $sellerKey,
                    'provider' => get_class($providerFactory)
                ]
            );

            return $providerFactory->create(['data' => $data]);
        }

        $sellerIdentifier = current(explode('|', $sellerKey));

        if (isset($this->providers[$sellerIdentifier])) {
            $providerFactory = $this->providers[$sellerIdentifier];
            $this->logger->info(
                '[CoditronRates] Found rate provider by seller identifier',
                [
                    'sellerIdentifier' => $sellerIdentifier,
                    'provider' => get_class($providerFactory)
                ]
            );

            return $providerFactory->create(['data' => $data]);
        }

        $this->logger->info(
            '[CoditronRates] Falling back to table rates',
            [
                'sellerIdentifier' => $sellerIdentifier,
            ]
        );

        return $this->defaultProviderFactory->create([
            'data' => $data
        ]);
    }
}
