<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Model;

use Coditron\CustomShippingRate\Api\CustomShippingInformationManagementInterface;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\QuoteIdMaskFactory;
use Coditron\CustomShippingRate\Api\Data\CustomShippingInformationInterfaceFactory;

class CustomShippingInformationManagement implements CustomShippingInformationManagementInterface
{
    public function __construct(
        protected CartRepositoryInterface $quoteRepository,
        protected QuoteIdMaskFactory $quoteIdMaskFactory,
        protected CustomShippingInformationInterfaceFactory $customShippingInformationFactory
    ) {
    }

    /** @inheirtDoc   */
    public function getShippingInformation($cartId)
    {
        if (!is_numeric($cartId)) {
            $quoteIdMask = $this->quoteIdMaskFactory->create()->load($cartId, 'masked_id');
            if (!$quoteIdMask->getQuoteId()) {
                throw new \Magento\Framework\Exception\NoSuchEntityException(
                    __('No such entity with masked_id = %1', $cartId)
                );
            }
            $quote = $this->quoteRepository->get($quoteIdMask->getQuoteId());
        } else {
            $quote = $this->quoteRepository->getActive($cartId);
        }

        $shippingData = $quote->getShippingData();
        $customShippingInformation = $this->customShippingInformationFactory->create();
        $customShippingInformation->setShippingData($shippingData);

        return $customShippingInformation;
    }
}
