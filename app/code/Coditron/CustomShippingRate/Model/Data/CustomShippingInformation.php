<?php
namespace Coditron\CustomShippingRate\Model\Data;

use Coditron\CustomShippingRate\Api\Data\CustomShippingInformationInterface;
use Magento\Framework\DataObject;

class CustomShippingInformation extends DataObject implements CustomShippingInformationInterface
{
    /**
     * @inheritDoc
     */
    public function setShippingData($shippingData)
    {
        return $this->setData('shipping_data', $shippingData);
    }

    /**
     * @inheritDoc
     */
    public function getShippingData()
    {
        return $this->getData('shipping_data');
    }
}