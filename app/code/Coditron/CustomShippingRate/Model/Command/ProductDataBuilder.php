<?php

declare(strict_types=1);

namespace Coditron\CustomShippingRate\Model\Command;

use Comave\SplitOrder\Api\SellerCartDetailsInterface;
use Magento\Quote\Model\Quote\Item;

class ProductDataBuilder
{
    /**
     * @param Item[] $quoteItems
     * @param string $destinationCountry
     * @return array
     */
    public function build(array $quoteItems, string $destinationCountry): array
    {
        $toShipPerSellerArray = [];

        foreach ($quoteItems as $item) {
            $itemSellerData = $item->getOptionByCode('option_' . SellerCartDetailsInterface::SELLER_OPTION);

            if (empty($itemSellerData)) {
                continue;
            }

            $sellerData = json_decode(
                $itemSellerData->getValue() ?? '{}',
                true
            );
            $sellerId = $sellerData['seller_id'];
            $integrationType = $sellerData['integration_type'];
            $sellerKey = sprintf('%s|%s', $integrationType, $sellerId);
            $weight = ($item->getWeight() * $item->getQty());

            if (!isset($toShipPerSellerArray[$sellerKey])) {
                $toShipPerSellerArray[$sellerKey] = [
                    'items' => [],
                    'country' => $destinationCountry,
                    'total_weight' => 0
                ];
            }

            $prodData = [
                'id' => $item->getProduct()->getId(),
                'name' => $item->getName(),
                'qty' => $item->getQty(),
                'qty_weight' => $weight
            ];

            $toShipPerSellerArray[$sellerKey]['items'][] = $prodData;
            $toShipPerSellerArray[$sellerKey]['total_weight'] += $weight;
        }

        return $toShipPerSellerArray;
    }
}
