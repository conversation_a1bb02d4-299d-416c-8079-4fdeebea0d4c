
Seller Shipping Country Restrictions
Updated: 20241031 - 20241104

Solution 1 Discussion
----------------------
Following the standard Magento implementation of this country restriction, would mean to implement this functionality
inside a shipping method implementation.

Since we decided to go with coditron customshippingrate, we need to implement the functionality for this shipping method.


Problems:
1. the custom carrier current implementation would cause the shipping method to become unavailable, when destination country is not in the ship to list
2. we need to query this even before reaching shipping stage in checkout
3. what about other shipping methods ?
4. what about zero shipping price ?
5. how do we store the contry restrictions per seller?
6. what happens when cart contains items from multiple sellers ?
7. what happens with a quote with multiple seller products when is converted to order?
8. quote save inside collectRates causes loosing the set shipping method when no payment method is set
9. bug inside app/code/Coditron/CustomShippingRate/Plugin/UpdateShippingDataOnMethodSelection.php on line 50

Solutions:
1. the custom carrier current implementation would cause the shipping method to become unavailable, when destination country is not in the
   We will adjust coditron customshippingrate carrier code, to return the default rate configured when there is no tablerate.
   Current implementation will calculate retail charge custom for turkishsouq (aka TSQ) and by tablerate for other retail_shops.
   The default configured price is currently not used for retail_shops, so we will adjust this.
   The default configured price is currently used if you add additional different shipping types in admin.

2. We need to query this even before reaching shipping stage in checkout
   20241001-STATUS: We'll find a way
   20241003-STATUS: We'll find a way

3. What about other shipping methods ?
   Why only in coditron customshippingrate?
   We could also consider a way to plug into get config for all methods 'carrier/../specificcountry' settings, but at this moment,
   it does not make sense, as the other shipping method params cannot be configured via the seller dashboard.
   20241003-STATUS: disable all other shipping methods, until we can implement configuring them in seller dashboard

4. what about zero shipping price ?
   Current coditron customshippingrate will allow you to add a 0 rate in tablerates, but will not append the rate when collecting, if it is 0.
   We will adjust this to allow 0, to obtain free shipping.
   20241001-STATUS: rate is not added when it is zero
   20241003-STATUS: rate is added if zero

5. how do we store the contry restrictions per seller?
   We will create an additional table along coditron_customshippingrate_shiptablerates, named coditron_customshippingrate_specificcountry
   OR
   What about we just limit shipping to the defined table rates ? If there is no table rate for a specific country, then we restrict shipping.
   20241001-STATUS: to be fixed: shipping price will be skipped if not defined, which is wrong for orders containing items from multiple sellers
   20241003-STATUS: fixed: shipping price will consider the default price, when no rate defined
   20241003-TODO: decide on What about we just limit shipping to the defined table rates ? If there is no table rate for a specific country, then we restrict shipping. How to restrict? set a flag and prevent adding rate for whole order when flag is set, while providing details in shipping_data, or provide details in a extended \Magento\Quote\Model\Quote\Address\RateResult\Error with details in the error message.
   Otherwise, if we go implement country restrictions separated, how is the seller going to define his table rates? seems unconvenient atm to manually add a rate for each country in the world.
   20241004-HINT: see \Magento\Shipping\Model\Carrier\AbstractCarrier:300 (\Magento\Shipping\Model\Carrier\AbstractCarrier::checkAvailableShipCountries)
   We can define this method checkAvailableShipCountries in our carrier which will fetch data from our storage instead of from config.


6. what happens when cart contains items from multiple sellers ?
   there is some Shipping data set on quote which looks like it had in mind to consider this case:
   `{"252":{"service_name":"DHL","price":"6","items":[{"id":"61025","name":"Scarf Beanie Bear Royal","qty":1}]}}`, where 252 is the seller id,
   but it always sets the last processed item data only, instead of appending.
   So this is not properly supported.
   The shipping data should be adjusted to append instead of replace on quote.
   Remains to be checked how the shipping data is further used, to be checked how it can be used for providing a summary of shipment costs and methods in GQL response.
   20241003-STATUS: Fixed, shipping_data will contain shipping details for each seller (seller, items, total weight, total ship price)
   20241003-TODO: check how shipping_data can be provided in GQL response

7. what happens with a quote with multiple seller products when is converted to order?
Expanding the question 6, is this even supported properly by the rest of the marketplace implementation? what happens with the quote when is converted to order? Is the quote split into multiple orders per each seller ?
Search for 'getShippingData' and there will be hints, needs to be investigated (see CustomShippingRate/SuccessAction, Checkout/SuccessAction, Payment. mpmultishipping).
20241003-STATUS: to be investigated

8. quote save inside collectRates causes loosing the set shipping method when no payment method is set
Calling setShippingMethodsOnCart, will return selected_shipping_method correctly, but after the result for the GQL is built, the collectRates is triggered once more and on $quote->save inside our custom carrier, the shipping rate and selected methods are lost when no payment address is set. This is an edge case, but the problem is that quote modifications should not happen inside collectRates. We need to fix this obsolete code by storing the data that it wants to save on the quote by other means. Identified this bug on 20241104-00:00, reported by MihailK,  
20241004-STATUS: to be fixed, commented quote->save for now, since the data is not properly processed further anyway, see pt. 9.
20241004-STATUS: fixed

9. app/code/Coditron/CustomShippingRate/Plugin/UpdateShippingDataOnMethodSelection.php extension attribute is improperly handled: [2024-11-03T21:15:12.627092+00:00] report.ERROR: Error updating shipping data on method selection: Notice: Indirect modification of overloaded element of Magento\Checkout\Model\PaymentDetails has no effect in /var/www/html/app/code/Coditron/CustomShippingRate/Plugin/UpdateShippingDataOnMethodSelection.php on line 50 [] []



Test data:

SKU, seller
2300140, CrystalPalace (252)
AJAX_HOODED_1606NAVYR_S, ajax ajax (1022)
