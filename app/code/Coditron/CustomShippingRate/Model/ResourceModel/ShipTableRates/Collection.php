<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'shiptablerates_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \Coditron\CustomShippingRate\Model\ShipTableRates::class,
            \Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates::class
        );
    }

    /**
     * Remove records
     */
    public function removeShiprates()
    {
        $ids = [];
        foreach ($this->getItems() as $item) {
            $ids[] = $item->getId();
        }

        if (empty($ids)) {
            return $this;
        }

        $quoted = $this->getConnection()->quoteInto('IN (?)', $ids);
        $this->getConnection()->delete($this->getMainTable(), "shiptablerates_id {$quoted}");
        return $this;
    }
}

