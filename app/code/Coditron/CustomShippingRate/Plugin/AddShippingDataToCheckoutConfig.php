<?php
namespace Coditron\CustomShippingRate\Plugin;

use Magento\Checkout\Model\Session as CheckoutSession;

class AddShippingDataToCheckoutConfig
{
    protected $checkoutSession;

    public function __construct(CheckoutSession $checkoutSession)
    {
        $this->checkoutSession = $checkoutSession;
    }

    public function afterGetConfig(\Magento\Checkout\Model\DefaultConfigProvider $subject, array $result)
    {
        // @TODO: remove all zend loggers from file
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/shipPlugin.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("config data shipping");

        $quote = $this->checkoutSession->getQuote();
        $extensionAttributes = $quote->getExtensionAttributes();

        $logger->info("shipping data");
        $logger->info(print_r($quote->getShippingData(),true));

        if ($extensionAttributes && $quote->getShippingData()) {
            $result['quoteData']['extension_attributes']['shipping_data'] = $quote->getShippingData();
        }

        return $result;
    }
}
