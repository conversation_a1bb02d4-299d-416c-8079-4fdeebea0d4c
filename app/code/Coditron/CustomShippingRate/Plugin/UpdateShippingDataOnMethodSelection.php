<?php
namespace Coditron\CustomShippingRate\Plugin;

use Magento\Checkout\Model\ShippingInformationManagement;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Framework\Serialize\Serializer\Json;
use Psr\Log\LoggerInterface;

class UpdateShippingDataOnMethodSelection
{
    protected $cartRepository;
    protected $json;
    protected $logger;

    public function __construct(
        CartRepositoryInterface $cartRepository,
        Json $json,
        LoggerInterface $logger
    ) {
        $this->cartRepository = $cartRepository;
        $this->json = $json;
        $this->logger = $logger;
    }

    public function afterSaveAddressInformation(
        ShippingInformationManagement $subject,
        $result,
        $cartId,
        \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
    ) {

        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/shipPlugin.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("config data for updated shipping");

        $logger->warn("----------- TODO: fix proper setting extension attributes! -------------------------");

        try {
            $quote = $this->cartRepository->getActive($cartId);
            $shippingMethod = $addressInformation->getShippingCarrierCode() . '_' . $addressInformation->getShippingMethodCode();

            // Check if the selected shipping method is retail_shops
//            if ($addressInformation->getShippingCarrierCode() === 'retail_shops') {
            if ($addressInformation->getShippingMethodCode() === 'retail_shops') {
                if ($quote->getShippingData()) {
                    $shippingData = $quote->getShippingData();

                    if (!isset($result['extension_attributes'])) {
                        $result['extension_attributes'] = [];
                    }
                    $result['extension_attributes']['shipping_data'] = $shippingData;

                    $logger->info(print_r($shippingMethod,true));
                    $logger->info(print_r($shippingData,true));
                }
            } else {
                // Clear shipping data for other methods
                if (isset($result['extension_attributes'])) {
                    unset($result['extension_attributes']['shipping_data']);
                }
                $logger->info('Cleared shipping data for method: ');
                $logger->info(print_r($shippingMethod,true));
            }
        } catch (\Exception $e) {
            $this->logger->error('Error updating shipping data on method selection: ' . $e->getMessage());
        }

        return $result;
    }
}
