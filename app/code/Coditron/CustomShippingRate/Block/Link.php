<?php

namespace Coditron\CustomShippingRate\Block;

use Magento\Framework\View\Element\Html\Link\Current;
use Coditron\CustomShippingRate\Helper\Data as shipRateHelper;

class Link extends \Magento\Framework\View\Element\Html\Link\Current
{
    /**
     * @var \Webkul\Marketplace\Helper\Data
     */
    protected $_mpHelper;

    /**
     * @var \Coditron\CustomShippingRate\Helper\Data
     */
    protected $_helper;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Magento\Framework\App\DefaultPathInterface $defaultPath
     * @param \Webkul\Marketplace\Helper\Data $mpHelper
     * @param \Coditron\CustomShippingRate\Helper\Data $helper
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\App\DefaultPathInterface $defaultPath,
        \Webkul\Marketplace\Helper\Data $mpHelper,
        shipRateHelper $helper,
        array $data = []
    ) {
        parent::__construct($context, $defaultPath);
        $this->_mpHelper = $mpHelper;
        $this->_helper = $helper;
    }

    public function isenable(){
    	return $this->_helper->isAllowedSellerTableRates();
    }

    /**
     * Get Current Url
     *
     * @return string
     */
    public function getCurrentUrl()
    {
        return $this->_urlBuilder->getCurrentUrl();
    }

    /**
     * Get Marketplace helper data
     *
     * @return object
     */
    public function getMpHelper()
    {
        return $this->_mpHelper;
    }
}
