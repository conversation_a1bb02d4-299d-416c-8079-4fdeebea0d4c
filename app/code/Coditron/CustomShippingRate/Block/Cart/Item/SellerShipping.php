<?php
namespace Coditron\CustomShippingRate\Block\Cart\Item;

use LDAP\Result;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Checkout\Model\Session as CheckoutSession;

class SellerShipping extends Template
{
    protected $checkoutSession;

    public function __construct(
        Context $context,
        CheckoutSession $checkoutSession,
        array $data = []
    ) {
        $this->checkoutSession = $checkoutSession;
        parent::__construct($context, $data);
    }

    public function getSellerShippingData()
    {
        $quote = $this->checkoutSession->getQuote();
        $shippingData = $quote->getShippingData();

        if ($shippingData) {
            try {
                return json_decode($shippingData, true) ?: [];
            } catch (\Exception $e) {
                // Log the error
                return [];
            }
        }
    
        return [];
    }

    public function getCurrentShippingMethod()
    {
        $quote = $this->checkoutSession->getQuote();
        $shippingAddress = $quote->getShippingAddress();
        return $shippingAddress ? $shippingAddress->getShippingMethod() : '';
    }
}