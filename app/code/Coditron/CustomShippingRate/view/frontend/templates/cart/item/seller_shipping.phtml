<?php
/** @var \Coditron\CustomShippingRate\Block\Cart\SellerShipping $block */
$sellerShippingData = $block->getSellerShippingData() ?: [];
$currentShippingMethod = $block->getCurrentShippingMethod() ?: '';
?>
<div id="seller-shipping-info" data-bind="scope: 'sellerShippingInfo'">
    <!-- ko template: getTemplate() --><!-- /ko -->
</div>
<script type="text/x-magento-init">
{
    "#seller-shipping-info": {
        "Magento_Ui/js/core/app": {
            "components": {
                "sellerShippingInfo": {
                    "component": "Coditron_CustomShippingRate/js/seller-shipping-info",
                    "config": {
                        "template": "Coditron_CustomShippingRate/seller-shipping-info",
                        "initialData": <?= json_encode($sellerShippingData) ?>,
                        "currentShippingMethod": <?= json_encode($currentShippingMethod) ?>
                    }
                }
            }
        }
    }
}
</script>