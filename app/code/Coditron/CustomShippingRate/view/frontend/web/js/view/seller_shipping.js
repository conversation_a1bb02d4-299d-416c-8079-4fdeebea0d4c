define([
    'jquery',
    'uiComponent',
    'ko',
    'Magento_Checkout/js/model/quote',
    'mage/storage',
    'Magento_Checkout/js/model/url-builder',
    'Magento_Customer/js/model/customer'
], function ($, Component, ko, quote, storage, urlBuilder, customer) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Coditron_CustomShippingRate/seller_shipping'
        },

        sellerShippingData: ko.observable({}),
        isDataAvailable: ko.observable(false),
        isComponentReady: ko.observable(false),

        initialize: function () {
            this._super();
            this.initObservable();

            // Initial load of shipping data from window.checkoutConfig
            this.loadInitialShippingData();

            // Subscribe to shipping method changes
            quote.shippingMethod.subscribe(this.onShippingMethodChange, this);

            this.isComponentReady(true);

            return this;
        },

        initObservable: function () {
            this._super()
                .observe([
                    'sellerShippingData',
                    'isDataAvailable'
                ]);
            return this;
        },

        loadInitialShippingData: function () {
            if (window.checkoutConfig && 
                window.checkoutConfig.quoteData && 
                window.checkoutConfig.quoteData.extension_attributes && 
                window.checkoutConfig.quoteData.extension_attributes.shipping_data) {
                try {
                    var shippingData = JSON.parse(window.checkoutConfig.quoteData.extension_attributes.shipping_data);
                    this.updateShippingData(shippingData);
                    console.log("initial shipping data");
                    console.log(shippingData);
                } catch (e) {
                    console.error('Error parsing initial shipping data:', e);
                    this.isDataAvailable(false);
                }
            }
        },

        onShippingMethodChange: function (shippingMethod) {
            console.log('Shipping method changed:', shippingMethod);
            if (shippingMethod && shippingMethod.method_code === 'retail_shops') {
                console.log('Retail shops method selected, fetching updated data');
                this.fetchUpdatedShippingData();
            } else {
                console.log('Non-retail shops method selected, clearing data');
                this.updateShippingData({});
            }
        },

        fetchUpdatedShippingData: function () {
            var self = this;
            var serviceUrl;
            var quoteId = quote.getQuoteId();
            var payload = {};

            console.log("inside fetchUpdatedShippingData");
            console.log("quote object", quote);

            if (customer.isLoggedIn()) {
                serviceUrl = '/rest/V1/carts/mine/custom-shipping-information';
                payload = {
                    cartId: quoteId
                };
            } else {
                serviceUrl = '/rest/V1/guest-carts/' + quoteId + '/custom-shipping-information';
            }

            console.log("Service URL:", serviceUrl);

            $.ajax({
                url: serviceUrl,
                type: 'GET',
                dataType: 'json',
                data: payload,
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('Content-Type', 'application/json');
                },
                success: function(result) {
                    console.log("API response:", result);
                    if (result.shipping_data) {
                        try {
                            var parsedData = JSON.parse(result.shipping_data);
                            console.log("Parsed shipping data:", parsedData);
                            self.updateShippingData(parsedData);
                        } catch (e) {
                            console.error('Error parsing shipping data:', e);
                            self.isDataAvailable(false);
                        }
                    } else {
                        console.log("No shipping data in response");
                        self.updateShippingData({});
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching shipping information:', status, error);
                    self.isDataAvailable(false);
                }
            });
        },

        updateShippingData: function (data) {
            console.log('Updating shipping data:', data);
            this.sellerShippingData(data);
            var isAvailable = Object.keys(data).length > 0;
            console.log('Setting isDataAvailable to:', isAvailable);
            this.isDataAvailable(isAvailable);
        }
    });
});