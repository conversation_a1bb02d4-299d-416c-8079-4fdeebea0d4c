define([
    'jquery',
    'ko',
    'uiComponent',
    'Magento_Checkout/js/model/quote',
    'Magento_Customer/js/model/customer'
], function ($, ko, Component, quote, customer) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Coditron_CustomShippingRate/seller-shipping-info',
            initialData: {},
            currentShippingMethod: ''
        },
        sellerShippingData: ko.observable({}),
        isDataAvailable: ko.observable(false),

        initialize: function () {
            this._super();
            this.initializeData();

            console.log('Component initialized with config:', this);

            quote.shippingMethod.subscribe(this.onShippingMethodChange, this);

            return this;
        },

        initializeData: function () {
            var initialData = this.initialData || {};
            this.updateShippingData(initialData);
        },

        onShippingMethodChange: function (method) {
            if (method && method.method_code === 'retail_shops') {
                this.fetchUpdatedShippingData();
            } else {
                this.updateShippingData({});
            }
        },

        fetchUpdatedShippingData: function () {
            var self = this;
            var serviceUrl;
            var quoteId = quote.getQuoteId();
            var payload = {};

            console.log("inside fetchUpdatedShippingData");
            console.log("quote object", quote);

            if (customer.isLoggedIn()) {
                serviceUrl = '/rest/V1/carts/mine/custom-shipping-information';
                payload = {
                    cartId: quoteId
                };
            } else {
                serviceUrl = '/rest/V1/guest-carts/' + quoteId + '/custom-shipping-information';
            }

            console.log("Service URL:", serviceUrl);

            $.ajax({
                url: serviceUrl,
                type: 'GET',
                dataType: 'json',
                data: payload,
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('Content-Type', 'application/json');
                },
                success: function(result) {
                    if (result && result.shipping_data) {
                        try {
                            var parsedData = JSON.parse(result.shipping_data);
                            self.updateShippingData(parsedData);
                        } catch (e) {
                            console.error('Error parsing shipping data:', e);
                            self.updateShippingData({});
                        }
                    } else {
                        self.updateShippingData({});
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching shipping data:', status, error);
                    self.updateShippingData({});
                }
            });
        },

        updateShippingData: function (data) {
            this.sellerShippingData(data || {});
            this.isDataAvailable(Object.keys(data || {}).length > 0);
        },

        isCorrectShippingMethod: function () {
            var method = quote.shippingMethod();
            return method && method.method_code === 'retail_shops';
        }
    });
});