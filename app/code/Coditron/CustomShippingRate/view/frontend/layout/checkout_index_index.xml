<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="checkout.root">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="components" xsi:type="array">
                        <item name="checkout" xsi:type="array">
                            <item name="children" xsi:type="array">
                                <item name="sidebar" xsi:type="array">
                                    <item name="children" xsi:type="array">
                                        <item name="seller_shipping" xsi:type="array">
                                            <item name="component" xsi:type="string">Coditron_CustomShippingRate/js/view/seller_shipping</item>
                                            <item name="displayArea" xsi:type="string">summary</item>
                                            <item name="config" xsi:type="array">
                                                <item name="template" xsi:type="string">Coditron_CustomShippingRate/seller_shipping</item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>
    </body>
</page>