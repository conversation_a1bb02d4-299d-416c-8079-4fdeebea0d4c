<?xml version="1.0" ?>
<!--
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
	<acl>
		<resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Coditron_CustomShippingRate::config_coditron_customshippingrate" title="Coditron Custom Shipping Rate"/>
                        </resource>
                    </resource>
                </resource>
                <resource id="Coditron_CustomShippingRate::ShipTableRates" title="ShipTableRates" sortOrder="10">
                    <resource id="Coditron_CustomShippingRate::ShipTableRates_save" title="Save ShipTableRates" sortOrder="10"/>
                    <resource id="Coditron_CustomShippingRate::ShipTableRates_delete" title="Delete ShipTableRates" sortOrder="20"/>
                    <resource id="Coditron_CustomShippingRate::ShipTableRates_update" title="Update ShipTableRates" sortOrder="30"/>
                    <resource id="Coditron_CustomShippingRate::ShipTableRates_view" title="View ShipTableRates" sortOrder="40"/>
                </resource>
            </resource>
        </resources>
	</acl>
</config>
