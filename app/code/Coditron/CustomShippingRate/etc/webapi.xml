<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
	<route url="/V1/coditron-customshippingrate/shiptablerates" method="POST">
		<service class="Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Coditron_CustomShippingRate::ShipTableRates_save"/>
		</resources>
	</route>
	<route url="/V1/coditron-customshippingrate/shiptablerates/search" method="GET">
		<service class="Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface" method="getList"/>
		<resources>
			<resource ref="Coditron_CustomShippingRate::ShipTableRates_view"/>
		</resources>
	</route>
	<route url="/V1/coditron-customshippingrate/shiptablerates/:shiptableratesId" method="GET">
		<service class="Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface" method="get"/>
		<resources>
			<resource ref="Coditron_CustomShippingRate::ShipTableRates_view"/>
		</resources>
	</route>
	<route url="/V1/coditron-customshippingrate/shiptablerates/:shiptableratesId" method="PUT">
		<service class="Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Coditron_CustomShippingRate::ShipTableRates_update"/>
		</resources>
	</route>
	<route url="/V1/coditron-customshippingrate/shiptablerates/:shiptableratesId" method="DELETE">
		<service class="Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface" method="deleteById"/>
		<resources>
			<resource ref="Coditron_CustomShippingRate::ShipTableRates_delete"/>
		</resources>
	</route>
	<route url="/V1/carts/mine/custom-shipping-information" method="GET">
        <service class="Coditron\CustomShippingRate\Api\CustomShippingInformationManagementInterface" method="getShippingInformation"/>
        <resources>
            <resource ref="self"/>
        </resources>
    </route>
    <route url="/V1/guest-carts/:cartId/custom-shipping-information" method="GET">
        <service class="Coditron\CustomShippingRate\Api\CustomShippingInformationManagementInterface" method="getShippingInformation"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
</routes>
