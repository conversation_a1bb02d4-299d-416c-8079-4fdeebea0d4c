<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Controller\Adminhtml\ShipTableRates;

class Edit extends \Coditron\CustomShippingRate\Controller\Adminhtml\ShipTableRates
{

    protected $resultPageFactory;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        parent::__construct($context, $coreRegistry);
    }

    /**
     * Edit action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        // 1. Get ID and create model
        $id = $this->getRequest()->getParam('shiptablerates_id');
        $model = $this->_objectManager->create(\Coditron\CustomShippingRate\Model\ShipTableRates::class);
        
        // 2. Initial checking
        if ($id) {
            $model->load($id);
            if (!$model->getId()) {
                $this->messageManager->addErrorMessage(__('This table rate no longer exists.'));
                /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
                $resultRedirect = $this->resultRedirectFactory->create();
                return $resultRedirect->setPath('*/*/');
            }
        }
        $this->_coreRegistry->register('coditron_customshippingrate_shiptablerates', $model);
        
        // 3. Build edit form
        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $resultPage = $this->resultPageFactory->create();
        $this->initPage($resultPage)->addBreadcrumb(
            $id ? __('Edit Table rates') : __('New Table rates'),
            $id ? __('Edit Table rates') : __('New Table rates')
        );
        $resultPage->getConfig()->getTitle()->prepend(__('Custom Table Rates'));
        $resultPage->getConfig()->getTitle()->prepend($model->getId() ? __('Edit table rate %1', $model->getId()) : __('New table rate'));
        return $resultPage;
    }
}

