<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;

class Authorize implements ArgumentInterface
{

    /**
     * @var \Magento\Framework\Json\Helper\Data
     */
    protected $jsonHelper;

    /**
     * __constructor.
     * @param Magento\Framework\Json\Helper\Data $jsonHelper
     */
    public function __construct(
        \Magento\Framework\Json\Helper\Data $jsonHelper
    ) {
        $this->jsonHelper = $jsonHelper;
    }

    /**
     * Json Encode function
     *
     * @param array $releventData
     * @return string
     */
    public function jsonEncode($releventData)
    {
        return $this->jsonHelper->jsonEncode($releventData);
    }
}
