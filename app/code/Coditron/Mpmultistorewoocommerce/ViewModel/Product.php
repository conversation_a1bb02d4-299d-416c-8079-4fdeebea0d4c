<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */ 
namespace Coditron\Mpmultistorewoocommerce\ViewModel;
 
use Magento\Framework\View\Element\Block\ArgumentInterface;
 
class Product implements ArgumentInterface
{
    /**
     * @var \Magento\Framework\Json\Helper\Data
     */
    protected $jsonHelper;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Helper\Data
     */
    protected $helper;
    
    /**
     * Constructor.
     *
     * @param Magento\Framework\Json\Helper\Data $jsonHelper
     * @param \Coditron\Mpmultistorewoocommerce\Helper\Data $helper
     */
    public function __construct(
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $helper
    ) {
        $this->jsonHelper = $jsonHelper;
        $this->helper = $helper;
    }

    /**
     * Json Encode function
     *
     * @param array $releventData
     * @return string
     */
    public function jsonEncode($releventData)
    {
        return $this->jsonHelper->jsonEncode($releventData);
    }

    /**
     * Get Header function
     *
     * @return object
     */
    public function getHelper()
    {
        return $this->helper;
    }
}
