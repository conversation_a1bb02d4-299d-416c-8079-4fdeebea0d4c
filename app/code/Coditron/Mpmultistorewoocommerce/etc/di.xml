<?xml version="1.0"?>
<!-- /**
 * @category  Coditron
 * @package   Coditron_Languagepack
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license    https://www.coditron.com/LICENSE.txt
 */ -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
   
    <preference for="Coditron\Mpmultistorewoocommerce\Api\Data\WoocommerceaccountsInterface" type="Coditron\Mpmultistorewoocommerce\Model\Woocommerceaccounts" />
    <preference for="Coditron\Mpmultistorewoocommerce\Api\WoocommerceaccountsRepositoryInterface" type="Coditron\Mpmultistorewoocommerce\Model\WoocommerceaccountsRepository" />
    
    <preference for="Coditron\Mpmultistorewoocommerce\Api\Data\WoocommercecategoryInterface" type="Coditron\Mpmultistorewoocommerce\Model\Woocommercecategory" />
    <preference for="Coditron\Mpmultistorewoocommerce\Api\WoocommercecategoryRepositoryInterface" type="Coditron\Mpmultistorewoocommerce\Model\WoocommercecategoryRepository" />
    
    <preference for="Coditron\Mpmultistorewoocommerce\Api\Data\WoocommercecategorymapInterface" type="Coditron\Mpmultistorewoocommerce\Model\Woocommercecategorymap" />
    <preference for="Coditron\Mpmultistorewoocommerce\Api\WoocommercecategorymapRepositoryInterface" type="Coditron\Mpmultistorewoocommerce\Model\WoocommercecategorymapRepository" />

    <preference for="Coditron\Mpmultistorewoocommerce\Api\Data\ProductmapInterface" type="Coditron\Mpmultistorewoocommerce\Model\Productmap" />
    <preference for="Coditron\Mpmultistorewoocommerce\Api\ProductmapRepositoryInterface" type="Coditron\Mpmultistorewoocommerce\Model\ProductmapRepository" />

    <preference for="Coditron\Mpmultistorewoocommerce\Api\Data\ImportedtmpproductInterface" type="Coditron\Mpmultistorewoocommerce\Model\Importedtmpproduct" />
    <preference for="Coditron\Mpmultistorewoocommerce\Api\ImportedtmpproductRepositoryInterface" type="Coditron\Mpmultistorewoocommerce\Model\ImportedtmpproductRepository" />

    <preference for="Coditron\Mpmultistorewoocommerce\Api\Data\CategoriesspecificationInterface" type="Coditron\Mpmultistorewoocommerce\Model\Categoriesspecification" />
    <preference for="Coditron\Mpmultistorewoocommerce\Api\CategoriesspecificationRepositoryInterface" type="Coditron\Mpmultistorewoocommerce\Model\CategoriesspecificationRepository" />

    <preference for="Magento\Eav\Api\Data\EavAttributeInterfaceFactory" type="Magento\Eav\Model\Entity\AttributeFactory" />
    <virtualType name="Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommerceaccounts\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">mpmultiwoocommerce_seller_details</argument>
            <argument name="resourceModel" xsi:type="string">Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommerceaccounts</argument>
        </arguments>
    </virtualType>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="mpmultistorewoocommerce_woocommerceaccount_list_data_source" xsi:type="string">Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommerceaccounts\Grid\Collection</item>
            </argument>
        </arguments>
    </type>


    <type name="Magento\Framework\Data\Form\FormKey\Validator">
        <plugin name="afterValidateWc" type="Coditron\Mpmultistorewoocommerce\Plugin\Validator" sortOrder="1" />
    </type>

    <type name="Magento\Cms\Model\Wysiwyg\CompositeConfigProvider">
        <arguments>
            <argument name="variablePluginConfigProvider" xsi:type="array">
                <item name="default" xsi:type="string">Magento\Variable\Model\Variable\ConfigProvider</item>
            </argument>
            <argument name="widgetPluginConfigProvider" xsi:type="array">
                <item name="default" xsi:type="string">Magento\Widget\Model\Widget\Config</item>
            </argument>
            <argument name="wysiwygConfigPostProcessor" xsi:type="array">
                <item name="default" xsi:type="string">Magento\Cms\Model\Wysiwyg\DefaultConfigProvider</item>
            </argument>
            <argument name="galleryConfigProvider" xsi:type="array">
                <item name="default" xsi:type="string">Magento\Cms\Model\Wysiwyg\Gallery\DefaultConfigProvider</item>
            </argument>
        </arguments>
    </type>

    <type name="Coditron\Mpmultistorewoocommerce\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="Coditron\Mpmultistorewoocommerce\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">customLogHandler</argument>
            <argument name="handlers"  xsi:type="array">
                <item name="system" xsi:type="object">Coditron\Mpmultistorewoocommerce\Logger\Handler</item>
            </argument>
        </arguments>
    </type>
    
</config>