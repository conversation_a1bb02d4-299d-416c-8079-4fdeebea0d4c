<?xml version="1.0"?>
<!-- 
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */  -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../lib/internal/Magento/Framework/Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Coditron_Mpmultistorewoocommerce::manager" title="Multi Store Woo Commerce Magento Connect" sortOrder="10" >
                    <resource id="Coditron_Mpmultistorewoocommerce::woocommerce_account_connect" title="Connect Woocommerce Account" sortOrder="10" >
                        <resource id="Coditron_Mpmultistorewoocommerce::Woocommerceaccount" title="Connect Woocommerce Account" sortOrder="10"/>
                        <resource id="Coditron_Mpmultistorewoocommerce::Categories" title="Map Categories" sortOrder="20"/>
                    </resource>
                </resource>
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Coditron_Mpmultistorewoocommerce::config_mpmultistorewoocommerce" title="Multi Store Woo Commerce Connect" sortOrder="50" >
                            </resource>
                        </resource>
                    </resource>
                </resource>
            </resource>   
        </resources>
    </acl>
</config>