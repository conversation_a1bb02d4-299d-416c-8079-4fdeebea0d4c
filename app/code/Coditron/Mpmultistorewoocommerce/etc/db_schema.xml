<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="mpmultiwoocommerce_seller_details" resource="default" engine="innodb" comment="Woo Commerce Sellers Details">
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false" padding="10" identity="true" comment="Entity Id"/>
        <column xsi:type="int" name="seller_id" unsigned="true" nullable="false" padding="10" comment="Seller Id"/>
        <column xsi:type="int" name="attribute_set_id" unsigned="true" nullable="false" comment="attribute set id"/>
        <column xsi:type="text" name="store_name" comment="woo commerce store name"/>
        <column xsi:type="text" name="woocommerce_user_id" comment="woocommerce_user_id"/>
        <column xsi:type="text" name="woocommerce_url" comment="woocommerce_url"/>
        <column xsi:type="text" name="woocommerce_consumer_key" comment="woo commerce consumer key"/>
        <column xsi:type="text" name="woocommerce_consumer_secret_key" comment="woo commerce consumer secret key"/>
        <column xsi:type="text" name="magento_base_currency" nullable="false" comment="Base Currency Of Magento."/>
        <column xsi:type="text" name="woocommerce_base_currency" nullable="false" comment="Base Currency Of Woo coomerce account."/>
        <column xsi:type="int" name="default_cate" default="0" unsigned="true" nullable="false" comment="default category"/>
        <column xsi:type="int" name="default_store_view" default="0" unsigned="true" nullable="false" comment="Default Store View"/>
        <column xsi:type="int" name="default_website" default="0" unsigned="true" nullable="false" comment="Default Website"/>
        <column xsi:type="text" name="order_status" comment="Order Status"/>
        <column xsi:type="text" name="default_quantity" comment="Default quantity"/>
        <column xsi:type="text" name="default_weight" comment="Default Weight"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        <index referenceId="MPMULTIWOOCOMMERCE_SELLER_DETAILS" indexType="btree">
            <column name="entity_id"/>
        </index>
    </table>

    <table name="mpmultiwoocommercesynchronize_category_specification" resource="default" engine="innodb" comment="woocommerce Synchronize Category Specifications Table">
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false" padding="10" identity="true" comment="Entity Id"/>
        <column xsi:type="int" name="seller_id" default="0" padding="11" comment="Seller Id"/>
        <column xsi:type="int" name="woocommerce_category_id" unsigned="true" nullable="false" comment="Woo commerce Category Id"/>
        <column xsi:type="text" name="woocommerce_specification_name" comment="Woo commerce Specification Name"/>
        <column xsi:type="text" name="mage_product_attribute_code" comment="Magento Product Attribute Code"/>
        <column xsi:type="timestamp" name="created" comment="Specification Sync Time"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        <index referenceId="MP_MULTIWWOOCOMMERCESYNC_CATEGORY_SPEC" indexType="btree">
            <column name="entity_id"/>
        </index>
    </table>

    <table name="mpmultiwoocommercesynchronize_category" resource="default" engine="innodb" comment="Mp Woocommerce Synchronize Product">
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false" padding="10" identity="true" comment="Entity Id"/>
        <column xsi:type="int" name="seller_id" default="0" padding="11" comment="Seller Id"/>
        <column xsi:type="int" name="mage_cat_id" unsigned="true" nullable="false" default="0" identity="false" padding="10" comment="Magento Category Id"/>
        <column xsi:type="bigint" name="woocommerce_cat_id" unsigned="true" nullable="false" comment="Woo Commerce Category Id"/>
        <column xsi:type="text" name="woocommerce_cat_name" comment="Woo commerce Category Id"/>
        <column xsi:type="text" name="body_html" comment="Body Html"/>
        <column xsi:type="timestamp" name="published_at_woocommerce" nullable="false" on_update="false" comment="Published At woocommerce" default="CURRENT_TIMESTAMP"/>
        <column xsi:type="int" name="rule_id" unsigned="true" nullable="false" default="0" identity="false" padding="10" comment="Customer Rule Id"/>
        <constraint xsi:type="foreign" referenceId="MPMULTIWOOCOMMERCE_CATEGORY_MAGE_CAT_ID_CATALOG_ENTITY_ID" table="mpmultiwoocommercesynchronize_category" column="mage_cat_id" referenceTable="catalog_category_entity" referenceColumn="entity_id" onDelete="CASCADE"/>
        <constraint xsi:type="foreign" referenceId="MPMULTIWOOCOMMERCE_CATEGORY_RULE_ID_SELLER_DETAIL_ENTITY_ID" table="mpmultiwoocommercesynchronize_category" column="rule_id" referenceTable="mpmultiwoocommerce_seller_details" referenceColumn="entity_id" onDelete="CASCADE"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        <index referenceId="MP_MULTIWOOCOMMERCESYNC_CATEGORY" indexType="btree">
            <column name="entity_id"/>
        </index>
    </table>

    <table name="mpmultiwoocommerce_categories" resource="default" engine="innodb" comment="Mp Woo Commerce Synchronize Product">
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false" padding="10" identity="true" comment="Entity Id"/>
        <column xsi:type="bigint" name="woocommerce_cat_id" unsigned="true" nullable="false" comment="Woo commerce Category Id"/>
        <column xsi:type="int" name="seller_id" default="0" padding="11" comment="Seller Id"/>
        <column xsi:type="bigint" name="woocommerce_cat_parentid" unsigned="true" nullable="false" comment="Woo commerce parent Category Id"/>
        <column xsi:type="text" name="woocommerce_cat_name" comment="Woo commerce Category Name"/>
        <column xsi:type="timestamp" name="created" comment="Category Mapped Time"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        <index referenceId="MP_MULTIWOOCOMMERCESYNC_CATEGORIES" indexType="btree">
            <column name="entity_id"/>
        </index>
    </table>

    <table name="mpmultiwoocommercesynchronize_product" resource="default" engine="innodb" comment="Mp Woo Commerce Synchronize Product">
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false" padding="10" identity="true" comment="Entity Id"/>
        <column xsi:type="int" name="magento_pro_id" unsigned="true" nullable="false" identity="false" padding="10" default="0" comment="Magento Product Id"/>
        <column xsi:type="text" name="woocommerce_pro_id" nullable="true" comment="Woo Commerce Product Id"/>
        <column xsi:type="text" name="name" nullable="true" comment="Product Name"/>
        <column xsi:type="int" name="seller_id" default="0" padding="11" comment="Seller Id"/>
        <column xsi:type="text" name="product_type" nullable="false" comment="Product Type"/>
        <column xsi:type="decimal" name="price" scale="4" precision="12" default="0.0000" unsigned="false" nullable="false" comment="Product Price"/>
        <column xsi:type="decimal" name="regular_price" scale="4" precision="12" default="0.0000" unsigned="false" nullable="false" comment="Product Regular Price"/>
        <column xsi:type="decimal" name="sale_price" scale="4" precision="12" default="0.0000" unsigned="false" nullable="false" comment="Product Sale Price"/>
        <column xsi:type="text" name="mage_cat_id" nullable="true" comment="Magento Category Id"/>
        <column xsi:type="int" name="change_status" unsigned="true" nullable="false" padding="10" comment="Change Status"/>
        <column xsi:type="timestamp" name="sync_date" nullable="false" on_update="false" comment="Sync Date" default="CURRENT_TIMESTAMP"/>
        <column xsi:type="int" name="rule_id" unsigned="true" nullable="false" padding="10" comment="shopify account id"/>
        <column xsi:type="text" name="woocommerce_variant_map" comment="woocommerce_variant_map"/>
        <constraint xsi:type="foreign" referenceId="MPMULTIWOOCOMMERCE_MAGEPROID_CATALOG_PRODUCT_ENTITY_ID" table="mpmultiwoocommercesynchronize_product" column="magento_pro_id" referenceTable="catalog_product_entity" referenceColumn="entity_id" onDelete="CASCADE"/>
        <constraint xsi:type="foreign" referenceId="MPMULTIWOOCOMMERCE_CATEGORY_RULE_ID_SELLER_DETAIL_ENTITY_ID" table="mpmultiwoocommercesynchronize_product" column="rule_id" referenceTable="mpmultiwoocommerce_seller_details" referenceColumn="entity_id" onDelete="CASCADE"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        <index referenceId="MP_MULTIWOOCOMMERCESYNC_PRODUCT" indexType="btree">
            <column name="entity_id"/>
        </index>
    </table>

    <table name="mpwoocommerce_product_image" resource="default" engine="innodb" comment="woo commerce Synchronize Product image">
        <column xsi:type="int" name="entity_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
        <column xsi:type="int" name="seller_id" default="0" padding="11" comment="Seller Id"/>
        <column xsi:type="int" name="magento_pro_id" padding="10" unsigned="true" nullable="false" identity="false" comment="Magento Product Id"/>
        <column xsi:type="varchar" name="image_url" nullable="true" length="255" comment="Image Url"/>
        <column xsi:type="int" name="is_default" padding="10" unsigned="true" nullable="false" identity="false" comment="Is Default Image"/>
        <column xsi:type="int" name="rule_id" padding="10" unsigned="true" nullable="false" identity="false" comment="Rule Id"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        <constraint xsi:type="foreign" referenceId="MP_WOOCOMMERCE_PRD_IMAGE_MAGENTO_PRO_ID_CAT_PRD_ENTT_ENTT_ID" table="mpwoocommerce_product_image" column="magento_pro_id" referenceTable="catalog_product_entity" referenceColumn="entity_id" onDelete="CASCADE"/>
        <index referenceId="MP_WOOCOMMERCE_PRODUCT_IMAGE_ENTITY_ID" indexType="btree">
            <column name="entity_id"/>
        </index>
    </table>

    <table name="mpmultiwoocommerce_tempwoocommerce" resource="default" engine="innodb" comment="woo commerce imported products temp table">
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false" padding="10" identity="true" comment="Entity Id"/>
        <column xsi:type="text" name="item_type" comment="Idenityfy that order or product"/>
        <column xsi:type="int" name="seller_id" default="0" padding="11" comment="Seller Id"/>
        <column xsi:type="text" name="item_id" comment="woo commerce Item Id"/>
        <column xsi:type="text" name="product_data" comment="woo commerce item data in json format"/>
        <column xsi:type="text" name="associate_products" nullable="true" comment="Configurable Associates Products"/>
        <column xsi:type="boolean" name="virtual" nullable="false" default="0" comment="Virtual product flag"/>
        <column xsi:type="boolean" name="downloadable" nullable="false" default="0" comment="Downloadable product flag"/>
        <column xsi:type="timestamp" name="created_at" comment="Import Time"/>
        <column xsi:type="int" name="rule_id" unsigned="true" nullable="false" comment="customer rule id"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        <index referenceId="MP_MULTIWOOCOMMERCESYNC_CATEGORY_SPEC" indexType="btree">
            <column name="entity_id"/>
        </index>
    </table>
</schema>
