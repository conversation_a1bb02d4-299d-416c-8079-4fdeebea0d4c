<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="webkul" translate="label" sortOrder="10">
            <label>Webkul</label>
        </tab>
        <section id="mpmultistorewoocommerce" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Multi-WooCommerce Magento Connect</label>
            <tab>webkul</tab>
            <resource>Coditron_Mpmultistorewoocommerce::config_mpmultistorewoocommerce</resource>
            <group id="general_settings" translate="label comment" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Configuration</label>
                <field id="enable" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Enable/Disable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable or disable the feature</comment>
                </field>
                <field id="order_sync_enable" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Enable order sycn on woocommerce</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable or disable the feature</comment>
                </field>
            </group>
        </section>
    </system>
</config>
