<?xml version="1.0"?>
<!--
/**
 * Coditron
 * 
 * NOTICE OF LICENSE
 * 
 * This source file is subject to the Coditron.com license that is
 * available through the world-wide-web at this URL:
 * https://www.coditron.com/LICENSE.txt
 * 
 * DISCLAIMER
 * 
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 * 
 * @category   Coditron
 * @package    Coditron_Mpmultistorewoocommerce
 * @copyright  Copyright (c) 2023 Coditron (https://www.coditron.com/)
 * @license    https://www.coditron.com/LICENSE.txt
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="Coditron_Mpmultistorewoocommerce::maintab" title="Multi Store Woo Commerce Connect" translate="title" module="Coditron_Mpmultistorewoocommerce" sortOrder="10" resource="Coditron_Mpmultistorewoocommerce::maintab" parent="Webkul_Marketplace::marketplace"/>
        <add id="Coditron_Mpmultistorewoocommerce::woocommerce_account_connect" title="Woo Commerce Account Connect" translate="title" module="Coditron_Mpmultistorewoocommerce" sortOrder="20" parent="Coditron_Mpmultistorewoocommerce::maintab" action="mpmultistorewoocommerce/woocommerceaccount" resource="Coditron_Mpmultistorewoocommerce::woocommerce_account_connect"/>
    </menu>
</config>