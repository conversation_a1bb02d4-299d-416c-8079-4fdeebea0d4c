<?php
namespace Co<PERSON>ron\Mpmultistorewoocommerce\Observer\Sales;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Exception\LocalizedException;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable as ConfigurableProTypeModel;
use Coditron\Mpmultistorewoocommerce\Helper\Data as wchelper;
use Magento\CatalogInventory\Model\Stock\StockItemRepository;
use Magento\Catalog\Model\ProductFactory;
use Webkul\Marketplace\Model\OrdersFactory as MpOrderModel;

class WCOrderPlaceAfter implements ObserverInterface
{
    /**
     * @var StockItemRepository
     */
    private $stockItemRepository;

    /**
     * @var ProductFactory
     */
    private $productFactory;

    /**
     * @var wchelper
     */
    private $wchelper;

    protected $mpOrderModel;

    /**
     * @var ConfigurableProTypeModel
     */
    private $configurableProTypeModel;

    public function __construct(
        StockItemRepository $stockItemRepository,
        ProductFactory $productFactory,
        wchelper $wchelper,
        MpOrderModel $mpOrderModel,
        ConfigurableProTypeModel $configurableProTypeModel
    ) {
        $this->stockItemRepository = $stockItemRepository;
        $this->productFactory = $productFactory;
        $this->wchelper = $wchelper;
        $this->mpOrderModel = $mpOrderModel;
        $this->configurableProTypeModel = $configurableProTypeModel;
    }

    public function execute(Observer $observer)
    {
        $logger = $this->createLogger();

        $isEnabled = $this->wchelper->isEnabled();
        $isOrderSyncEnable = $this->wchelper->isOrderSyncEnable();

        if ($isEnabled && $isOrderSyncEnable) {
            try {
                $productQtyUpdateStatus =  $this->helper->getStatusOfProQtyUpdate();
                if ($productQtyUpdateStatus) {
                    $ShopifyOrderId = null;
                    $order = $observer->getOrder();
                    $lastOrderId = $order->getId();
                    $orderIncrementedId = $order->getIncrementId();
                    $orderItems = $order->getAllVisibleItems();
                    foreach ($orderItems as $item) {
                        if (in_array($item->getProductType(), ['bundle', 'downloadable', 'virtual'])) {
                            throw new LocalizedException(__($item->getProductType().' not allowed'));
                        }
                        if ($item->getProductType() == 'configurable') {
                            $product = $this->productloader->create()->load($item->getProductId());
                            $productId = $this->configurableProTypeModel->getProductByAttributes(
                                $item->getProductOptions()['info_buyRequest']['super_attribute'],
                                $product
                            )->getEntityId();
                            $productIds[] = [
                                'product_id' => $item->getProductId(),
                                'variant_id'=> $productId,
                                'ordered_qty'=> $item->getQtyOrdered()
                            ];
                        } else {
                            $productIds[] = [
                                'product_id' => $item->getProductId(),
                                'variant_id'=> $item->getProductId(),
                                'ordered_qty'=> $item->getQtyOrdered()
                            ];
                        }
                    }
                    $this->manipulationOnProductIds($productIds,$order);
                }
            } catch (\Exception $e) {
                $logger->info('Observer SalesOrderPlaceAfterObserver : '.$e->getMessage());
            }    
        }
    }

    public function manipulationOnProductIds($productIds)
    {
        $logger = $this->createLogger();
        foreach ($productIds as $key => $data) {
            $Woocommerce = $this->productMapRepository
                            ->getRecordByMageProductId($data['product_id'])->getFirstItem();
            if ($Woocommerce->getEntityId()) {
                $product = $this->productloader->create()->load($data['product_id']);
                $variantMapping = $Woocommerce->getShopifyVariantMap() ?
                $this->helper->jsonDecoder($Woocommerce->getShopifyVariantMap()) : [];
                $variantId = $this->getTheVariantIdToUpdateQty($data['product_sku'], $variantMapping);
                $helper = $this->helper;
                $apiCredentials = $helper->getTheShopApiCredentials($Woocommerce->getRuleId(), true);
                $isValidApiCred = $helper->authorizeShopifyShop($apiCredentials);
                if ($isValidApiCred['status'] && $variantId!='') {
                    $logger->info(' updated product quantity in Shopify');
                    $shopifyProductUpdateData = $this->dataToUpdateShpProductVariant(
                        $Woocommerce->getRuleId(),
                        $variantId,
                        $data['ordered_qty']
                    );
                    $updateQtyStatus = $this->helper->updateProductQty(
                        $data['ordered_qty'],
                        $Woocommerce->getRuleId(),
                        $getInventoryItemId['data']['variant']['inventory_item_id'],
                        'adjust'
                    );
                   
                } else {
                    $logger
                    ->info('Shopify details not correct '.$Woocommerce->getRuleId());
                }
            } else {
                $logger->info('this product id '.$data['product_id'].' is not an Shopify product');
            }
        }
    }

    /**
     * Create logger instance
     *
     * @return \Zend_Log
     */
    private function createLogger()
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/WCsyncOrders.log');
        return new \Zend_Log($writer);
    }
}
