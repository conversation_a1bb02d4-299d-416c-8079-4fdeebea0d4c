<?php

namespace Coditron\Mpmultistorewoocommerce\Helper;

use Coditron\Mpmultistorewoocommerce\Api\ImportedtmpproductRepositoryInterface;
use Coditron\Mpmultistorewoocommerce\Model\Importedtmpproduct;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\App\Filesystem\DirectoryList;

class ManageRawData
{

    private $registry;
    private $productMapRecord;
    private $helper;
    private $attributeModel;
    private $importedTmpProduct;
    private $date;
    protected $_directory;
    protected $contextController;
    protected $importedTmpProductRepository;
    protected $io;
    public $ruleId;
    public $attributeSetId;
    private $categoryFactory;
    private $_categoryTree;

    public function __construct(
        \Coditron\Mpmultistorewoocommerce\Helper\Data $helper,
        \Coditron\Mpmultistorewoocommerce\Model\Productmap $productMapRecord,
        \Magento\Framework\Registry $registry,
        ImportedtmpproductRepositoryInterface $importedTmpProductRepository,
        \Magento\Catalog\Model\ResourceModel\Eav\AttributeFactory $attributeModel,
        Importedtmpproduct $importedTmpProduct,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $date,
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\Filesystem\Io\File $io,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Catalog\Model\CategoryFactory $categoryFactory,
        \Magento\Catalog\Model\ResourceModel\Category\Tree $categoryTree
    ) {
        $this->contextController = $context;
        $this->productMapRecord = $productMapRecord;
        $this->registry = $registry;
        $this->helper = $helper;
        $this->importedTmpProductRepository = $importedTmpProductRepository;
        $this->attributeModel = $attributeModel;
        $this->importedTmpProduct = $importedTmpProduct;
        $this->date =  $date;
        $this->io = $io;
        $this->_directory = $filesystem->getDirectoryWrite(DirectoryList::MEDIA);
        $this->categoryFactory = $categoryFactory;
        $this->_categoryTree = $categoryTree;
    }


    public function createlogger(){
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/WooCommerce.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        return $logger;
    }

    /**
     * ImportShopifyProductByShopifyItemId
     *
     * @param int $itemId
     * @param int $ruleId
     * @param boolean $viaCrone
     * @return void
     */
    public function importShopifyProductByShopifyItemId($itemId, $ruleId, $viaCrone = false)
    {
        try {
            $apiCredentials = $this->helper->getTheShopApiCredentials($ruleId, true);
            $isValidApiCred = $this->helper->authorizeShopifyShop($apiCredentials);
            if ($isValidApiCred['status']) {
                $data = $this->helper->getShopifyProductListFromShopifyProductId($apiCredentials, $itemId);
                if ($data['status'] == true) {
                    if (isset($data['data']['product'])) {
                        $data['data']['products'][] = $data['data']['product'];
                        unset($data['data']['product']);
                    }
                    $this->manageProductRawData($data, $ruleId, $viaCrone);
                } else {
                    $this->createlogger()->err('importShopifyProductByShopifyItemId :- '. $data['error_msg']);
                }
            } else {
                $this->helper->logCriticalMessage(
                    __('Helper ManageRawData importShopifyProductByShopifyItemId :- '.$isValidApiCred['error_msg'])
                );
            }
        } catch (\Exception $e) {
            $this->helper->logCriticalMessage(
                __('Helper ManageRawData importShopifyProductByShopifyItemId :- '.$e->getMessage())
            );
        }
    }

    /**
     * GetMagentoProductIdsAccordingtoShopifyItem
     *
     * @param object $syncProMap
     * @param array $transaction
     * @param int $ruleId
     * @return void
     */
    private function getMagentoProductIdsAccordingtoShopifyItem($syncProMap, $transaction, $ruleId)
    {
        try {
            $productId = $syncProMap->getMagentoProId();
            $productType = $syncProMap->getProductType();
            $bundalProductItems = false;
            switch ($productType) {
                case 'configurable':
                    $productId = $this->helper->getConfAssoProductId($productId, $transaction, $ruleId);
                    break;
                case 'grouped':
                    $productId = $this->helper->getProductRepository()
                                        ->get($transaction['Variation']['SKU'])->getEntityId();
                    break;
                case 'bundle':
                    $sku = $transaction['Variation']['SKU'];
                    $nameValueList = $transaction['Variation']['VariationSpecifics']['NameValueList'];
                    $bundalProductItems = $this->getBundleAssoProductIds($productId, $nameValueList, $sku);
                    break;
            }
            
            if ($productId == false) {
                $this->helper->logCriticalMessage(
                    __('Helper ManageRawData getMagentoProductIdsAccordingtoShopifyItem :- productId not defined')
                );
                return false;
            }
            
            if (isset($productId['status']) && $productId['status'] == false) {
                throw new LocalizedException(__($productId['error_msg']));
            }

            return ['product_id' => $productId, 'bundle_items' => $bundalProductItems];
        } catch (\Exception $e) {
            $this->helper->logCriticalMessage(
                __('Helper ManageRawData getMagentoProductIdsAccordingtoShopifyItem :- '.$e->getMessage())
            );
            return false;
        }
    }

    /**
     * Manage Product Raw Data
     *
     * @param object $resultsItems
     * @param int $ruleId
     * @param boolean $viaCron
     * @param boolean $viaListener
     * @return array
     */
    public function manageProductRawData(
        $resultsItems,
        $ruleId,
        $viaCron = false,
        $viaListener = false,
        $viaCsv = false
    ) {
        try {
            if (isset($resultsItems['errors'])) {
                return $resultsItems['errors'];
            }
            $items = [];
            $this->ruleId = $ruleId;
            $this->helper->ruleId = $ruleId;
            $this->attributeSetId = $this->helper->getAttributeSetId($ruleId);

            $alreadyMapped = [];
            if ($viaListener) {
                $tempAvlImported = [];
            } else {
                $tempAvlImported = $this->importedTmpProduct->getCollection()
                                ->addFieldToFilter('item_type', 'product')
                                ->addFieldToFilter('rule_id', $ruleId)
                                ->getColumnValues('item_id');
                $alreadyMapped = $this->productMapRecord->getCollection()
                                ->addFieldToFilter('rule_id', $ruleId)
                                ->getColumnValues('woocommerce_pro_id');

                $tempAvlImported =  array_merge($tempAvlImported, $alreadyMapped);
            }
            $helper = $this->helper;
            $notImportedProduct  = [];
            foreach ($resultsItems['data']['products'] as $key => $data) {
                $this->helper->logInfoMessage(
                    __('Helper manageProductRawData data:- ').
                        $this->helper->jsonEncoder($data)
                );
                try {
                    if (isset($data['requires_shipping'])
                        && ($data['requires_shipping'] == true || $data['requires_shipping'] == 1)
                    ) {
                        continue;
                    }
                    if (in_array($data['id'], $tempAvlImported)) {
                        continue;
                    }
                    if (!$helper->isDesWithHtml($ruleId)) {
                        $data['body_html'] = preg_replace(
                            '#<script(.*?)>(.*?)</script>#is',
                            '',
                            (string)$data['body_html']
                        );
                        $data['body_html'] = preg_replace(
                            '#<link(.*?)>(.*?)</link>#is',
                            '',
                            (string)$data['body_html']
                        );
                        $data['body_html'] = preg_replace(
                            '#<style(.*?)>(.*?)</style>#is',
                            '',
                            (string)$data['body_html']
                        );
                        $data['body_html'] = strip_tags($data['body_html']);
                    }
                    $wholedata = $this->prepareWholeData($data, $ruleId, $viaCsv);
                    if (isset($wholedata['category']) && is_array($wholedata['category'])) {
                        $wholedata['category'] = implode(',', $wholedata['category']);
                    }
                    if (isset($data['images'])) {
                        // $wholedata['image_data'] = ['default' => $data['image']['src'], 'images' => [$data['image']['src']]];
                        $wholedata = $this->setImageData($wholedata, $data['images']);
                        $this->helper->logInfoMessage(
                            __('Helper ManageRawData manageProductRawData image data:- ').
                                $this->helper->jsonEncoder($data['images'])
                        );
                    }
                    /* Save imported product in temp table***/
                    if ($viaListener) {
                        return $wholedata;
                    } elseif (!$viaCron) {
                        $associatedProducts = $wholedata['associate_products'] ?? null;
                        unset($wholedata['associate_products']);
                        $currentDate = $this->date->date()->format('Y-m-d\TH:i:s');
                        $items[] =  [
                            'item_type' => 'product',
                            'item_id' => $data['id'],
                            'product_data' => $this->helper->jsonEncoder($wholedata),
                            'associate_products' => $associatedProducts ?
                                                $this->helper->jsonEncoder($associatedProducts) : null,
                            'created_at' => $currentDate,
                            'rule_id'   => $ruleId
                        ];
                    } else {
                        $productId = $this->cronActionOnProductData($wholedata);
                        return $productId;
                    }
                } catch (\Exception $e) {
                    $displayMessage = 'Shopify product of id - '.
                        '"'.$data['id'].'"'.' and name - '.'"'.$data['title'].'"'
                        .' not imported. Please check the log.';
                    $notImportedProduct[] = $displayMessage;
                    $this->helper->logCriticalMessage(__(
                        "errorMessage: - ".$displayMessage."\r\n"."Actual error :-".$e->getMessage()
                    ));
                }
            }
            $itemsCount = 0;
            if (!empty($items)) {
                $itemsCount = $this->helper->insertDataInBulk('wk_mpmultishopify_tempshopify', $items);
            }
            return ['item_count' => $itemsCount, 'notImportedProduct' => $notImportedProduct];
        } catch (\Exception $e) {
            $this->helper->createlogger()->err(__('Helper ManageRawData manageProductRawData :- ').$e->getMessage());
            throw new LocalizedException(__("There is some error in mapping product. Please check the log"));
        }
    }

    /**
     * Cron Action On Product Data
     *
     * @param array $tempProData
     * @return int
     */

    private function cronActionOnProductData($tempProData)
    {
        try {
            $productId = 0;
            $request = $this->contextController->getRequest();
            if (($tempProData['type_id'] == 'simple') || (isset($tempProData['supperattr'])
                && empty($tempProData['supperattr']))) {
                if (isset($tempProData['assocate_pro'][0])) {
                    $tempProData['price'] = $tempProData['assocate_pro'][0]['price'];
                    $tempProData['stock'] = $tempProData['assocate_pro'][0]['qty'];
                    $tempProData['type_id'] = 'simple';
                    foreach ($tempProData['assocate_pro'][0] as $key => $value) {
                        if (strpos($key, 'conf_') !== false) {
                            $tempProData[$key] = $value;
                        }
                    }
                    unset($tempProData['assocate_pro']);
                    unset($tempProData['supperattr']);
                }
                foreach ($tempProData as $key => $value) {
                    $request->setParam($key, $value);
                }

                $result = $this->helper->saveSimpleProduct($request, $this->ruleId);
            } else {
                foreach ($tempProData as $key => $value) {
                    $request->setParam($key, $value);
                }
                $result = $this->helper->saveConfigProduct($request);
            }
            $data = [
                'shopify_pro_id' => $tempProData['shopify_item_id'],
                'name' => $tempProData['name'],
                'price' => $tempProData['price'],
                'product_type' => $tempProData['type_id'],
                'rule_id'   => $this->helper->ruleId
            ];

            if (isset($result['product_id']) && $result['product_id']) {
                $productId = $data['magento_pro_id'] = $result['product_id'];
                $data['mage_cat_id'] = $tempProData['category'][0];
                $record = $this->productMapRecord;
                $record->setData($data);
                $record->save();
            }
            $this->registry->unregister('product');
            $this->registry->unregister('current_product');
            $this->registry->unregister('current_store');
            return $productId;
        } catch (\Exception $e) {
            $this->createlogger()->info('product raw data -'.json_encode($tempProData));
            $this->createlogger()->err('Error :- '. $e->getMessage());
            return $productId;
        }
    }

    /**
     * Prepare Whole Data
     *
     * @param array $data
     * @return array
     */
    public function prepareWholeData($data, $ruleId, $viaCsv = false)
    {
        try {
            $this->ruleId = $ruleId;
            $this->helper->ruleId = $ruleId;
            $proCatSpec = [];
            $managedProData = $this->getManageProductData($data);
            if (isset($managedProData['status']) && $managedProData['status'] == false) {
                return $managedProData;
            }
            $shopifyMageId = $managedProData['shopify_mage_id'];
            $proCatSpec = $managedProData['pro_cat_spec'];
            $weight = $managedProData['weight'];
            if ($weight == "" || $weight == 0) {
                $weight = 1;
            }
            $attributeSetId = $managedProData['attribute_set_id'];

            /** if description is blank then product url is not generated at shopify end so give title.**/
            if (isset($data['body_html']) && $data['body_html'] == '') {
                $data['body_html'] == $data['title'];
            }

            /* get product variation**/
            if (isset($data['variants']) &&
                !empty($data['variants']) &&
                $data['variants'][0]['title'] != "Default Title"
            ) {
                $itemSku = isset($data['sku']) ? $data['sku'].'-'.$data['id'] : $data['id'];
                $superAttrAndAssociatePro = $this->getSuperAttrAndAssociatePro(
                    ['options'=>$data['options'], 'variants'=> $data['variants']],
                    $itemSku,
                    $weight
                );
                $productCost = $this->helper->getPriceAfterAppliedRule(
                    $data['variants'][0]['price'],
                    'import'
                );
                $productCost = $this->helper->convertPrice($productCost, 'import');
                $wholedata = [
                    'shopify_item_id' => $data['id'],
                    'type_id' => 'configurable',
                    'supperattr' => $superAttrAndAssociatePro['super_att_ids'],
                    'status' => 1,
                    'attribute_set_id' => $attributeSetId,
                    'category' => [$shopifyMageId],
                    'name' => $data['title'],
                    'description' => $data['body_html'],
                    'short_description' => ' ',
                    'sku' => $data['variants'][0]['sku']."-".$data['id'],
                    'price' => $productCost,
                    'is_in_stock' => 1,
                    'tax_class_id' => 0,
                    'weight' => $weight
                ];
                if ($wholedata['sku'] == '') {
                    $wholedata['sku'] = $data['title']."-".$data['id'];
                }
                /* Assigne values to store product according to shopify product**/
                foreach ($proCatSpec as $key => $value) {
                    $wholedata[$key] = $value;
                }

                $wholedata['assocate_pro'] = $superAttrAndAssociatePro['associate_pro'];
                // $wholedata['shopify_variant_map'] = $superAttrAndAssociatePro['shopify_variant_map'];
            } else {
                $productCost = $this->helper->getPriceAfterAppliedRule(
                    $data['variants'][0]['price'],
                    'import'
                );
                $productCost = $this->helper->convertPrice($productCost, 'import');
                $lowestIdLocationData = $this->helper->getShopifyProductQtyFromLowestIdLocation(
                    $this->ruleId,
                    $data['variants'][0]['inventory_item_id']
                );
                /**For without variation product**/
                $wholedata = [
                    'shopify_item_id' => $data['id'],
                    'type_id' => 'simple',
                    'status' => 1,
                    'attribute_set_id' => $attributeSetId,
                    'producttypecustom' => 'customproductsimple',
                    'category' => [$shopifyMageId],
                    'name' => $data['title'],
                    'description' => $data['body_html'],
                    'short_description' => ' ',
                    'sku' => $data['variants'][0]['sku'],
                    'price' => $productCost,
                    'stock' => $lowestIdLocationData['qty'] ?? 0,
                    'is_in_stock' => 1,
                    'tax_class_id' => 0,
                    'weight' => $weight,
                    'shopify_variant_id' => $data['variants'][0]['id']
                ];
                if ($wholedata['sku'] == '') {
                    $wholedata['sku'] = $data['title'];
                }
                
                foreach ($proCatSpec as $key => $value) {
                    $wholedata[$key] = $value;
                }
            }
            if ($viaCsv) {
                $wholedata = $this->csvPrepareData($wholedata, $managedProData, $data);
            }
            return $wholedata;
            
        } catch (\Exception $e) {
            $this->helper->logCriticalMessage('Helper Data prepareWholeData :- '. $e->getMessage());
            throw new LocalizedException(__($e->getMessage()));
            // return $wholedata;
        }
    }

    public function csvPrepareData($wholedata, $managedProData, $data)
    {
        $prepareData = $mappedMageCatIds = [];
        $shopifyCatId = $managedProData['shopify_mage_id'];
        $shopifyCategoriesIds = [$shopifyCatId];
        $mappedMageCatPath = '';
        foreach ($shopifyCategoriesIds as $key => $value) {
            $catMapData = $this->helper->getStorShopifyCatMapData($value, $this->ruleId);
            if ($shopifyCatId) {
                $mageCatId = $catMapData ? $catMapData->getMageCatId() : $value;
                $category = $this->categoryFactory->create()->load($mageCatId);
                $categoryTree = $this->getCategoryTree($category);
                $mappedMageCatPath = implode('/', $categoryTree);
            }
        }
        $mappedMageCatIds = array_unique($mappedMageCatIds);
        $attributeSetCode = $this->helper->getAttributeSet($wholedata['attribute_set_id'])
        ->getData('attribute_set_name');
        $qty = $wholedata['stock'] ?? 0;
        $isInStock = $qty > 0 ? 1 : 0;
        if ($wholedata['type_id'] == 'configurable') {
            $isInStock = 1;
        }
        $prepareData = [
            'sku' => $wholedata['sku'],
            'store_view_code' => '',
            'product_online' => 1,
            'status' => $wholedata['status'],
            'attribute_set_code' => $attributeSetCode,
            'product_type' => $wholedata['type_id'],
            'category' => $shopifyCatId ?? '',
            'categories' => $mappedMageCatPath,
            'product_websites' => $this->helper->getDefaultWebsiteCode($this->ruleId),
            'name' => $wholedata['name'],
            'description' => $wholedata['description'],
            'short_description' => '',
            'weight' => $wholedata['weight'],
            'price' => $wholedata['price'],
            'special_price' => $wholedata['special_price'] ?? '',
            'url_key' => $wholedata['sku'],
            'page_layout' => 'Product -- Full Width',
            'tax_class_name' => 'None',
            'visibility' => 'Catalog, Search',
            'qty' => $qty,
            'is_in_stock' => $isInStock,
            'rule_id' => $this->ruleId,
            'additional_attributes' => '',
            'configurable_variations' => '',
            'configurable_variation_labels' => '',
            'image_data' => []
        ];
        if ($wholedata['type_id'] == 'configurable') {
            $itemSku = isset($data['sku']) ? $data['sku'].'-'.$data['id'] : $data['id'];
            $weight = $managedProData['weight'];
                if ($weight == "" || $weight == 0) {
                    $weight = 1;
                }
            $superAttrAndAssociatePro = $this->getSuperAttrAndAssociatePro(
                ['options'=>$data['options'], 'variants'=> $data['variants']],
                $itemSku,
                $weight,
                $prepareData,
                true
            );
            // $wholedata['shopify_variant_map'] = $superAttrAndAssociatePro['shopify_variant_map'];
            $prepareData['configurable_variations'] = $superAttrAndAssociatePro['configurable_variations'];
            $prepareData['configurable_variation_labels'] =
                $superAttrAndAssociatePro['configurable_variation_labels'];
            $prepareData['associate_products'] = $superAttrAndAssociatePro['associate_pro'];
        } else {
            if ($wholedata['shopify_variant_id']) {
                $prepareData['shopify_variant_id'] = $wholedata['shopify_variant_id'];
            }
        }
        return $prepareData;
    }

    /**
     * GetSuperAttrAndAssociatePro
     *
     * @param array $variationsList
     * @param int $itemId
     * @param string $weight
     * @return array
     */
    private function getSuperAttrAndAssociatePro($variationsList, $itemId, $weight, $wholedata=[], $viaCsv=false)
    {
        $associatePro = [];
        $superAttIds = [];
        $configurable_attr = [];
        $count = 1;
        $variations = [];
        $shopifyVariantMap = [];
        foreach ($variationsList['variants'] as $variation) {
            $productOptions = [];
            if ($variation['requires_shipping'] == false || $variation['requires_shipping'] == 0) {
                continue;
            }
            $attributeData = [];
            foreach ($variationsList['options'] as $option) {
                $attributeCode = str_replace(' ', '_', $option['name']);
                $attributeCode = preg_replace('/[^A-Za-z0-9\_]/', '', $attributeCode);
                $mageAttrCode = substr('conf_'.strtolower($attributeCode), 0, 30);
                if ($variation['option'.$option['position']] != '' &&
                    $variation['option'.$option['position']] != 'Non applicabile'
                ) {
                    $attributeData[$mageAttrCode] = $variation['option'.$option['position']];
                    array_push($superAttIds, $mageAttrCode);
                    if ($viaCsv) {
                        $value = $variation['option' . $option['position']];
                        $configurable_attr[] = "{$mageAttrCode}={$option['name']}";
                        $productOptions[] = "{$mageAttrCode}={$value}";
                    }
                }
            }
            if (!empty($attributeData)) {
                $quictProData = $this->getQuickProductData($variation, $attributeData, $itemId, $count, $weight, $wholedata);
                if ($viaCsv) {
                    $quictProData['additional_attributes'] = implode(',', $productOptions);
                    $sku = $quictProData['sku'];
                    array_unshift($productOptions, "sku={$sku}");
                    $variations[] = implode(',', $productOptions);
    
                }
                array_push($associatePro, $quictProData);
                $count++;
            }
        }
        $response = ['super_att_ids' => array_unique($superAttIds), 'associate_pro' => $associatePro];
        if ($viaCsv) {
            $configurable_attr = array_unique($configurable_attr);
            $variations = array_unique($variations);
            $confLable = implode(',', $configurable_attr);
            $confVariations = implode('|', $variations);
            // $response['shopify_variant_map'] = $shopifyVariantMap;
            $response['configurable_variations'] = $confVariations;
            $response['configurable_variation_labels'] = $confLable;
        }
        return $response;
    }

   /**
    * GetQuickProductData

    * @param array $variation
    * @param array $attributeData
    * @param varchar $itemId
    * @param int $count
    * @param float $weight
    * @return array
    */
    private function getQuickProductData($variation, $attributeData, $itemId, $count, $weight, $wholedata = [])
    {
        $productCost = $this->helper->getPriceAfterAppliedRule(
            $variation['price'],
            'import'
        );
        $productCost = $this->helper->convertPrice($productCost, 'import');
        $productSpecialPrice = isset($variation['compare_at_price'])
            && $variation['compare_at_price'] >= 0 ? $variation['price'] : null;

        $lowestIdLocationData = $this->helper->getShopifyProductQtyFromLowestIdLocation(
            $this->ruleId,
            $variation['inventory_item_id']
        );
        if ($variation['sku'] == '' && $variation['sku'] == null) {
            $variation['sku'] = $variation['id'];
        }

        if (!isset($wholedata['shopify_item_id'])) {
            if (isset($variation['images'])) {
                foreach ($variation['images'] as $imageData) {
                    $images[$imageData['id']] = $imageData['src'];
                }
            }    
            $imageUrl = $images[$variation['image_id']] ?? "";
            $quictProData = $wholedata;
            $quictProData['sku'] = $variation['sku'];
            $quictProData['product_type'] = 'simple';
            $quictProData['name'] = $variation['title'];
            $quictProData['weight'] = $weight;
            $quictProData['price'] = $productCost;
            $quictProData['special_price'] = $productSpecialPrice;
            $quictProData['url_key'] = $variation['id'];
            $quictProData['visibility'] = 'Not Visible Individually';
            $quictProData['qty'] = (int) $variation['inventory_quantity'];
            $quictProData['shopify_variant_id'] = $variation['id'];
            $quictProData['image_data'] = ['default' => $imageUrl, 'images' => [$imageUrl]];
        }else {
            $qty = !empty($lowestIdLocationData) ? $lowestIdLocationData['qty']: 0;
            $quictProData = [
                'status' => 1,
                'sku' => $variation['sku'],
                'price' => $productCost,
                'qty' => (int) $qty,
                'is_in_stock' => 1,
                'tax_class_id' => 0,
                'weight' => $weight,
                'visibility' => 1,
                'shopify_variant_id'=> $variation['id']
            ];
            foreach ($attributeData as $mageAttrCode => $value) {
                $quictProData[$mageAttrCode] = $this->getAttributeOptionId($mageAttrCode, $value);
            }
        }
        return $quictProData;
    }

    /**
     * Get Attribute
     *
     * @param string $mageAttrCode
     * @param string $value
     * @return int
     */
    private function getAttributeOptionId($mageAttrCode, $value)
    {
        try {
            $attributeInfo = $this->attributeModel->create()->getCollection()
                                        ->addFieldToFilter('attribute_code', $mageAttrCode)
                                        ->setPageSize(1)->getFirstItem();

            $attribute = $this->attributeModel->create()->load($attributeInfo->getAttributeId());
            return $attribute->getSource()->getOptionId($value);
        } catch (\Exception $e) {
            $this->helper->logCriticalMessage('Helper ManageRawData getSuperAttrIds :- '.$e->getMessage());
        }
    }

    /**
     * Manage ProductData
     *
     * @param array $data
     */
    private function getManageProductData($data)
    {
        try {
            $this->helper->logCriticalMessage('Helper data :- '.json_encode($data));
            $proCatSpec = [];
            $shopifyMageId = [];
            $shopifyCategoryListAssociatedWithProduct = $this->helper->getAllShopifyCategoryAssociatedWithProduct(
                $this->ruleId,
                $data['id']
            );
            $weight = isset($data['variants'][0]['weight']) ? $data['variants'][0]['weight'] : 0 ;
            if (array_key_exists('data', $shopifyCategoryListAssociatedWithProduct)) {
                $shopifyCategoriesIds = $this->helper->getSpecificKeyValueFromArray(
                    $shopifyCategoryListAssociatedWithProduct['data']['collects'],
                    'collection_id'
                );
                foreach ($shopifyCategoriesIds as $key => $value) {
                    $catMapData = $this->helper->getStorShopifyCatMapData($value, $this->ruleId);
                    if ($catMapData) {
                        $shopifyMageId[] = $catMapData->getMageCatId();
                    }
                }
            }
            
            if (empty($shopifyMageId)) {
                $shopifyMageId[] = $this->helper->getDefaultCate($this->ruleId);
            }

            if (isset($data['variants']) &&
                !empty($data['variants']) &&
                $data['variants'][0]['title'] != "Default Title"
            ) {
                $attr = $this->helper->createSuperAttrMagento($data['options'], $this->attributeSetId);
            }
            $response = [
                'shopify_mage_id' => implode(',', $shopifyMageId),
                'pro_cat_spec' => $proCatSpec,
                'weight' => $weight,
                'attribute_set_id' => $this->attributeSetId
            ];
            return $response;
        } catch (\Exception $e) {
            $this->helper->logCriticalMessage('Helper ManageRawData getManageProductData :- '.$e->getMessage());
        }
    }

    /**
     * Set ImageData
     *
     * @param array $wholedata
     * @param array $pictureDetails
     * @return array
     */
    private function setImageData($wholedata, $pictureDetails)
    {
        $imageArr = [];
        $defaultImage = '';
        $i = 0;
        $path = BP.'/pub/media/import/mpmultishopifystoremageconnect/';
        if (!($this->_directory->isDirectory($path))) {
            $this->io->mkdir($path, 0755, true);
        }
        if (!empty($pictureDetails)) {
            foreach ($pictureDetails as $value) {
                if ($i == 0) {
                    $defaultImage = $value['src'];
                    $i++;
                }
                array_push($imageArr, $value['src']);
            }
            $wholedata['image_data'] = ['default' => $defaultImage, 'images' => $imageArr];
        }
        return $wholedata;
    }


    /**
     * Get category tree
     *
     * @param mixed $category
     * @return array
     */
    public function getCategoryTree($category)
    {
        if (!$category) {
            return [];
        } elseif ($category && !$category instanceof \Magento\Catalog\Model\Category) {
            $category = $this->categoryFactory->create()->load($mageCatId);
        }
        
        $tree = $this->_categoryTree->loadBreadcrumbsArray($category->getPath());

        return array_column($tree, 'name');
    }
}
