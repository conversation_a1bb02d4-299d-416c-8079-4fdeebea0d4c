<?php

namespace Coditron\Mpmultistorewoocommerce\Helper;

use Magento\ConfigurableProduct\Model\Product\Type\Configurable as ConfigurableProduct;
use Magento\Catalog\Controller\Adminhtml\Product\Initialization\Helper as InitializationHelper;
use Magento\Catalog\Controller\Adminhtml\Product\Builder as ProductBuilder;
use Magento\ConfigurableProduct\Model\Product\VariationHandler;

/**
 * SaveProduct Class for saving products
 */
class SaveProduct
{
    /**
     * @var \Magento\Catalog\Model\Product
     */
    private $product;

    /**
     * @var Initialization\Helper
     */
    private $initializationHelper;

    /**
     * @var \Magento\Catalog\Model\Product\TypeTransitionManager
     */
    private $productTypeManager;

    /** @var \Magento\ConfigurableProduct\Model\Product\VariationHandler */
    private $variationHandler;

    /** @var \Magento\Catalog\Api\ProductRepositoryInterface  */
    private $productRepository;
    
    /** @var ProductBuilder */
    private $productBuilder;
    
    /** @var \Magento\InventoryCatalogAdminUi\Model\GetSourceItemsDataBySku */
    private $sourceDataBySku;
    
    /** @var \Magento\Framework\App\Config\ScopeConfigInterface */
    private $scopeConfig;
    
    /** @var \Webkul\MpMultiShopifyStoreMageConnect\Logger\Logger */
    private $logger;

    /**
     * Construct function
     *
     * @param \Magento\Catalog\Model\Product $product
     * @param \Magento\Catalog\Model\Product\TypeTransitionManager $productTypeManager
     * @param VariationHandler $variationHandler
     * @param \Magento\Catalog\Api\ProductRepositoryInterface $productRepository
     * @param \Magento\InventoryCatalogAdminUi\Model\GetSourceItemsDataBySku $sourceDataBySku
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param InitializationHelper $initializationHelper
     * @param ProductBuilder $productBuilder
     * @param \Webkul\MpMultiShopifyStoreMageConnect\Logger\Logger $logger
     */
    public function __construct(
        \Magento\Catalog\Model\Product $product,
        \Magento\Catalog\Model\Product\TypeTransitionManager $productTypeManager,
        VariationHandler $variationHandler,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        // \Magento\InventoryCatalogAdminUi\Observer\SourceItemsProcessor $sourceItemsProcessor,
        \Magento\InventoryCatalogAdminUi\Model\GetSourceItemsDataBySku $sourceDataBySku,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        InitializationHelper $initializationHelper,
        ProductBuilder $productBuilder,
        \Webkul\MpMultiShopifyStoreMageConnect\Logger\Logger $logger
    ) {
        $this->product = $product;
        $this->initializationHelper = $initializationHelper;
        $this->productBuilder = $productBuilder;
        $this->productTypeManager = $productTypeManager;
        $this->variationHandler = $variationHandler;
        $this->productRepository = $productRepository;
        // $this->sourceItemsProcessor = $sourceItemsProcessor;
        $this->sourceDataBySku = $sourceDataBySku;
        $this->scopeConfig = $scopeConfig;
        $this->logger = $logger;
    }

    /**
     * Default customer account page.
     *
     * @param object $proDataReq
     * @param int $storeId
     * @return \Magento\Framework\View\Result\Page
     */
    public function saveProductData($proDataReq, $storeId = 0)
    {
        try {
            $wholedata = $proDataReq->getParams();
            $product = $this->initializationHelper
                                    ->initialize(
                                        $this->productBuilder->build(
                                            $proDataReq,
                                            $storeId
                                        )
                                    );
            $this->productTypeManager->processProduct($product);
            $product->setUrlKey($product->getName().rand(1, 100));
            $originalSku = $product->getSku();
            try {
                $product->save();
            } catch (\Excetpion $e) {
                return 0;
            }
            $productId = $product->getId();

            $configurations = [];
            if (!empty($wholedata['supperattr'])) {
                $configurations = $wholedata['supperattr'];
            }
            /** for configurable associated product */
            if ($product->getTypeId() == ConfigurableProduct::TYPE_CODE
                && !empty($configurations)) {
                $configurations = $this->variationHandler
                                        ->duplicateImagesForVariations(
                                            $configurations
                                        );
                foreach ($configurations as $associtedProductId => $productData) {
                    $associtedProduct = $this->productRepository->getById(
                        $associtedProductId,
                        true,
                        $storeId
                    );
                    $productData = $this->variationHandler->processMediaGallery(
                        $associtedProduct,
                        $productData
                    );
                    $associtedProduct->addData($productData);
                    if ($associtedProduct->hasDataChanges()) {
                        $this->_saveAssocitedProduct($associtedProduct);
                    }
                }
            }

            /*for configurable associated products save end*/
            $this->product->load($productId)
                            ->setStatus($wholedata['product']['status'])->save();
                            $stockData = [
                            'qty' => $wholedata['product']['quantity_and_stock_status']['qty'],
                            'is_in_stock' => $wholedata['product']['quantity_and_stock_status']['is_in_stock']
                            ];
                            $inventorySourceList = $this->sourceDataBySku->execute($product->getSku());
                
                            $inventorySourceConfig = $wholedata['product']['stock_data']['inventory_source'] ??
                            $inventorySourceConfig   =   $this->scopeConfig->getValue(
                                'mpmultishopifystoremageconnect/general_settings/default_source',
                                \Magento\Store\Model\ScopeInterface::SCOPE_STORE
                            );
            if (is_array($inventorySourceList) && !empty($inventorySourceList)) {
                $addSource = true;
                foreach ($inventorySourceList as $key => $inventorySource) {
                    if ($inventorySourceConfig && $inventorySource['source_code'] == $inventorySourceConfig) {
                        $inventorySourceList[$key]['quantity'] = $stockData['qty'];
                        $addSource = false;
                    }
                }
                if ($addSource) {
                    array_push(
                        $inventorySourceList,
                        [
                            'source_code' => $inventorySourceConfig,
                            'status' => 1,
                            'quantity' => $stockData['qty']
                        ]
                    );
                }
            } else {
                $inventorySourceList = [
                ['source_code' => $inventorySourceConfig, 'status' => 1, 'quantity' => $stockData['qty']]
                ];
            }
            // if (is_array($inventorySourceList)) {
            //     $this->sourceItemsProcessor->process($product->getSku(), $inventorySourceList);
            // }
        } catch (\Exception $e) {
            $this->logger->error('saveProductData helper :'.$e->getMessage());
            $productId =  0;
        }
        return $productId;
    }

    /**
     * Save AssocitedProduct
     *
     * @param Magento\Catalog\Api\Data\ProductInterface $associtedProduct
     * @return void
     */
    private function _saveAssocitedProduct($associtedProduct)
    {
        $this->productRepository->save($associtedProduct, true);
    }
}
