<?php

namespace Coditron\Mpmultistorewoocommerce\Helper;

use Magento\Framework\Data\Form\FormKey;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\Json\Helper\Data as JsonHelper;
use Webkul\Marketplace\Helper\Data as MpHelper;
use Coditron\Mpmultistorewoocommerce\Model\WoocommerceaccountsFactory;
use Magento\Framework\Encryption\EncryptorInterface;

/**
 * Custom MpMultiShopifyStoreMageConnect Data helper.
 */
class FData extends AbstractHelper
{
    private $_storeManager;
    private $_scopeConfig;
    private $curl;
    private $jsonHelper;
    private $woocommerceAccountsFactory;
    private $encryptor;
    private $mphelper;

    public function __construct(
        Context $context,
        StoreManagerInterface $storeManager,
        Curl $curl,
        JsonHelper $jsonHelper,
        WoocommerceaccountsFactory $woocommerceAccountsFactory,
        EncryptorInterface $encryptor,
        MpHelper $mphelper
    ) {
        $this->_storeManager = $storeManager;
        $this->_scopeConfig = $context->getScopeConfig();
        $this->curl = $curl;
        $this->jsonHelper = $jsonHelper;
        $this->woocommerceAccountsFactory = $woocommerceAccountsFactory;
        $this->encryptor = $encryptor;
        $this->mphelper = $mphelper;
        parent::__construct($context);
    }

    /**
     * Get default website ID for a given shop ID.
     *
     * @param int $shopId
     * @return int
     */
    public function getDefaultWebsite($shopId = '')
    {
        $defaultStoreView = $this->getDefaultStoreView($shopId);
        $store = $this->_storeManager->getStore($defaultStoreView);
        return $store->getWebsiteId();
    }

    /**
     * Get default store view for a given shop ID.
     *
     * @param int $shopId
     * @return int
     */
    public function getDefaultStoreView($shopId = '')
    {
        return $this->getShopifyConfiguration($shopId)['default_store_view'];
    }

    /**
     * Get current currency conversion rate for a given shop ID.
     *
     * @param int $shopId
     * @return string
     */
    public function getCurrConRate($shopId = "")
    {
        return $this->getShopifyConfiguration($shopId)['currency_conv_rate'];
    }

    /**
     * Get store base currency code.
     *
     * @return string
     */
    public function getBaseCurrencyCode()
    {
        return $this->_storeManager->getStore()->getBaseCurrencyCode();
    }

    /**
     * Convert price based on process type.
     *
     * @param float $price
     * @param string $process
     * @return float
     */
    public function convertPrice($price, $process)
    {
        $convRate = $this->getCurrConRate($this->ruleId);
        if ($convRate == 0 || $convRate == 0.0) {
            return $price;
        }
        return ($process == 'import') ? $price / $convRate : $price * $convRate;
    }

    /**
     * Decode JSON string.
     *
     * @param string $string
     * @return array
     */
    public function jsonDecoder($string = '')
    {
        return $this->jsonHelper->jsonDecode($string);
    }

    /**
     * Encode array to JSON string.
     *
     * @param array $arr
     * @return string
     */
    public function jsonEncoder($arr = [])
    {
        return $this->jsonHelper->jsonEncode($arr);
    }

    /**
     * Get WooCommerce account details by seller ID.
     *
     * @return array
     */
    public function getAccountDetailsBySellerId()
    {
        $sellerId = $this->mphelper->getSellerData()->getFirstItem()->getSellerId();
        return $this->woocommerceAccountsFactory->create()
            ->getCollection()
            ->addFieldToFilter("seller_id", ["eq" => $sellerId]);
    }

    /**
     * Get WooCommerce account details.
     *
     * @return array
     */
    public function getAccountDetails()
    {
        $data = [
            'store_name' => '',
            'attribute_set_id' => '',
            'woocommerce_consumer_key' => '',
            'woocommerce_consumer_secret_key' => '',
            'woocommerce_url' => '',
            'default_qty' => '',
            'default_cate' => '',
            'default_store_view' => '',
            'item_with_html' => '0',
            'order_status' => '',

        ];

        $sellerData = $this->getAccountDetailsBySellerId();
        if ($sellerData->getSize() > 0) {
            $data = $sellerData->getFirstItem()->getData();
            $decryptedVal = $this->decryptTheApiCredential($data["woocommerce_consumer_key"], $data["woocommerce_consumer_secret_key"]);
            $data["woocommerce_consumer_key"] = $decryptedVal["woocommerce_consumer_key"];
            $data["woocommerce_consumer_secret_key"] = $decryptedVal["woocommerce_consumer_secret_key"];
        }
        return $data;
    }

    /**
     * Decrypt WooCommerce API credentials.
     *
     * @param string $consumerKey
     * @param string $consumerSecret
     * @return array
     */
    private function decryptTheApiCredential($consumerKey, $consumerSecret)
    {
        return [
            'woocommerce_consumer_key' => $this->encryptor->decrypt($consumerKey),
            'woocommerce_consumer_secret_key' => $this->encryptor->decrypt($consumerSecret)
        ];
    }

    /**
     * Return Seller ID.
     *
     * @return int
     */
    public function getSellerId()
    {
        return $this->mphelper->getSellerData()->getFirstItem()->getSellerId();
    }
}
