<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Helper;

use Magento\Framework\Data\Form\FormKey;
use Magento\Catalog\Model\ResourceModel\Eav\AttributeFactory;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\Group\CollectionFactory as AttrGroupCollection;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\Option\CollectionFactory as AttrOptionCollectionFactory;
use Magento\Eav\Api\AttributeManagementInterface;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable\Attribute as ConfigurableAttributeModel;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable as ConfigurableProTypeModel;
use Magento\Framework\Filter\FilterManager;
use Magento\Framework\Exception\LocalizedException;
use Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategorymap\Source\RootCat;
use Coditron\Mpmultistorewoocommerce\Api\WoocommercecategorymapRepositoryInterface;
use Coditron\Mpmultistorewoocommerce\Api\WoocommerceaccountsRepositoryInterface;
use Coditron\Mpmultistorewoocommerce\Api\ImportedtmpproductRepositoryInterface;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
      /**
     * @var array $operation
     */
    private static $operation = [
        '' => '--Select--',
        'Increase' => 'Increase',
        'Decrease' => 'Decrease',
    ];

    /**
     * @var array $operationType
     */
    private static $operationType = [
            '' => '--Select--',
            'Fixed' => 'Fixed',
            'Percent' => 'Percent',
    ];

    /**
     * @var array $status
     */
    private static $status = [
        '1' => 'Enable',
        '0' => 'Disable',
    ];

     /**
     * @var const CATALOGSEARCH_FULLTEXT
     */
    public const CATALOGSEARCH_FULLTEXT = 'catalogsearch_fulltext';

    public const IMG_STORE_DEFAULT = 0;

    public const IMG_STORE_PROFILER = 1;

    public const IMG_STORE_MQ = 2;

    public const IMG_IMPORT_CONSUMER_NAME = "woocommerce.image.import";


     /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    private $_storeManager;

    /**
     * @var object $_scopeConfig
     */
    protected $_scopeConfig;

    /**
     * @var object $importedTmpProductRepository
     */
    private $importedTmpProductRepository;

    /**
     * @var RootCat's object
     */
    private $rootCat;

    /**
     * @var \Magento\Framework\Data\Form\FormKey $formkey
     */
    private $formkey;

    /**
     * @var \Magento\Framework\HTTP\Client\Curl
     */
    private $curl;

    /**
     * @var \Magento\Framework\Json\Helper\Data
     */
    public $jsonHelper;
    
    /**
     * @var FilterManager
     */
    public $filterManager;
    
    /**
     * @var WoocommercecategorymapRepositoryInterface
     */
    public $woocommerceCategoryMapRepository;
    
    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\WoocommerceaccountsFactory 
     */
    public $_woocommerceaccountsFactory;
    
    /**
     * @var WoocommerceaccountsRepositoryInterface
     */
    public $woocommerceAccountsRepository;
    
    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\Storage\DbStorage
     */
    public $dbStorage;
    
    /**
     * @var \Magento\Catalog\Model\ProductFactory
     */
    public $product;
    
    /**
     * @var \Magento\Catalog\Api\ProductRepositoryInterface
     */
    public $productRepository;
    
    /**
     * @var SaveProduct
     */
   // public $saveProduct;
    
    /**
     * @var AttributeFactory
     */
    public $attributeFactory;
    
    /**
     * @var AttrGroupCollection
     */
    public $attrGroupCollection;
    
    /**
     * @var AttrOptionCollectionFactory
     */
    public $attrOptionCollectionFactory;
    
    /**
     * @var AttributeManagementInterface
     */
    public $attributeManagement;
    
    /**
     * @var ProductAttributeRepositoryInterface
     */
    public $productAttribute;
    
    /**
     * @var \Magento\Framework\Registry
     */
    public $registry;
    
    /**
     * @var \Magento\Framework\Filesystem
     */
    public $filesystem;
    
    /**
     * @var \Magento\Framework\Filesystem\Driver\File
     */
    public $file;
    
    /**
     * @var ConfigurableAttributeModel
     */
    public $configurableAttributeModel;
    
    /**
     * @var ConfigurableProTypeModel
     */
    public $configurableProTypeModel;
    
    /**
     * @var \Magento\Widget\Model\Template\Filter
     */
    public $templateProcessor;
    
    /**
     * @var \Magento\Framework\Indexer\IndexerInterfaceFactory
     */
    public $indexerFactory;
    
    /**
     * @var \Magento\Framework\Encryption\EncryptorInterface
     */
    public $encryptor;
    
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    public $cartRepositoryInterface;
    
    /**
     * @var \Magento\Quote\Api\CartManagementInterface
     */
    public $cartManagementInterface;
    
    /**
     * @var \Magento\Quote\Model\Quote\Address\Rate
     */
    public $shippingRate;
    
    /**
     * @var \Magento\Sales\Model\Order
     */
    public $order;
    
    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    public $customerRepository;
    
    /**
     * @var \Magento\Customer\Model\CustomerFactory
     */
    public $customerFactory;
    
    /**
     * @var \Magento\Backend\Model\Session
     */
    public $backendSession;
    
    /**
     * @var \Magento\Directory\Model\Config\Source\Country
     */
    public $countryHelper;
    
    /**
     * @var \Magento\Directory\Model\CountryFactory
     */
    public $countryFactory;
    
    /**
     * @var ListingTemplateFactory
     */
    public $listingTemplate;
    
    /**
     * @var \Coditron\Mpmultistorewoocommerce\Logger\Logger
     */
    public $logger;
    
    /**
     * @var \Webkul\Marketplace\Helper\Data
     */
    public $mphelper;
    
    /**
     * @var \Webkul\Marketplace\Model\ProductFactory
     */
    public $mpProductFactory;
    
    /**
     * @var \Magento\Framework\Stdlib\DateTime\DateTime
     */
    public $_date;


    /**
     * @var \Laminas\Uri\Uri
     */
    public $laminasUri;

    /**
     * @var AttributeSetInterface
     */
    protected $attributeSetRepository;


    
    /**
     * Construct function
     *
     * @param FilterManager                                                        $filterManager
     * @param \Magento\Framework\App\Helper\Context                                $context
     * @param \Magento\Store\Model\StoreManagerInterface                           $storeManager
     * @param \Magento\Framework\Mail\Template\TransportBuilder                    $transportBuilder
     * @param \Coditron\Mpmultistorewoocommerce\Model\WoocommerceaccountsFactory   $woocommerceAccountsFactory
     * @param \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategorymap\Source\RootCatFactory $rootCat,
     * @param WoocommercecategorymapRepositoryInterface                            $woocommerceCategoryMapRepository
     * @param WoocommerceaccountsRepositoryInterface                               $woocommerceAccountsRepository
     * @param ImportedtmpproductRepositoryInterface                                $importedTmpProductRepository
     * @param \Magento\Catalog\Model\ProductFactory                                $product
     * @param \Magento\Catalog\Api\ProductRepositoryInterface                      $productRepository
     * @param FormKey                                                              $formkey
     * @param AttributeFactory                                                     $attributeFactory
     * @param AttrGroupCollection                                                  $attrGroupCollection
     * @param AttrOptionCollectionFactory                                          $attrOptionCollectionFactory
     * @param AttributeManagementInterface                                         $attributeManagement
     * @param ProductAttributeRepositoryInterface                                  $productAttribute
     * @param \Magento\Framework\Registry                                          $registry
     * @param \Magento\Framework\Filesystem                                        $filesystem
     * @param ConfigurableAttributeModel                                           $configurableAttributeModel
     * @param ConfigurableProTypeModel                                             $configurableProTypeModel
     * @param \Magento\Widget\Model\Template\Filter                                $templateProcessor
     * @param \Magento\Framework\Indexer\IndexerInterfaceFactory                   $indexerFactory
     * @param \Magento\Framework\Encryption\EncryptorInterface                     $encryptor
     * @param \Magento\Quote\Api\CartRepositoryInterface                           $cartRepositoryInterface
     * @param \Magento\Quote\Api\CartManagementInterface                           $cartManagementInterface
     * @param \Magento\Quote\Model\Quote\Address\Rate                              $shippingRate
     * @param \Magento\Sales\Model\Order                                           $order
     * @param \Magento\Customer\Api\CustomerRepositoryInterface                    $customerRepository
     * @param \Magento\Customer\Model\CustomerFactory                              $customerFactory
     * @param \Magento\Backend\Model\Session                                       $backendSession
     * @param \Magento\Directory\Model\Config\Source\Country                       $countryHelper
     * @param \Magento\Directory\Model\CountryFactory                              $countryFactory
     * @param \Magento\Framework\HTTP\Client\Curl                                  $curl
     * @param \Magento\Framework\Json\Helper\Data                                  $jsonHelper
     * @param \Magento\Framework\Filesystem\Driver\File                            $file
     * @param \Webkul\Marketplace\Helper\Data                                      $mphelper
     * @param \Webkul\Marketplace\Model\ProductFactory                             $mpProductFactory
     * @param \Magento\Framework\Stdlib\DateTime\DateTime                          $date
     * @param \Laminas\Uri\Uri                                                     $laminasUri
     * @param \Magento\Catalog\Api\AttributeSetRepositoryInterface                 $attributeSetRepository
     * @param \Coditron\Mpmultistorewoocommerce\Logger\Logger                 $logger
     */
    public function __construct(
        FilterManager $filterManager,
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Coditron\Mpmultistorewoocommerce\Model\WoocommerceaccountsFactory   $woocommerceAccountsFactory,
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategorymap\Source\RootCatFactory $rootCat,
        WoocommercecategorymapRepositoryInterface $woocommerceCategoryMapRepository,
        WoocommerceaccountsRepositoryInterface $woocommerceAccountsRepository,
        ImportedtmpproductRepositoryInterface $importedTmpProductRepository,
        \Magento\Catalog\Model\ProductFactory $product,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        FormKey $formkey,
        AttributeFactory $attributeFactory,
        AttrGroupCollection $attrGroupCollection,
        AttrOptionCollectionFactory $attrOptionCollectionFactory,
        AttributeManagementInterface $attributeManagement,
        ProductAttributeRepositoryInterface $productAttribute,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Filesystem $filesystem,
        ConfigurableAttributeModel $configurableAttributeModel,
        ConfigurableProTypeModel $configurableProTypeModel,
        \Magento\Widget\Model\Template\Filter $templateProcessor,
        \Magento\Framework\Indexer\IndexerInterfaceFactory $indexerFactory,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        \Magento\Quote\Api\CartRepositoryInterface $cartRepositoryInterface,
        \Magento\Quote\Api\CartManagementInterface $cartManagementInterface,
        \Magento\Quote\Model\Quote\Address\Rate $shippingRate,
        \Magento\Sales\Model\Order $order,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Magento\Backend\Model\Session $backendSession,
        \Magento\Directory\Model\Config\Source\Country $countryHelper,
        \Magento\Directory\Model\CountryFactory $countryFactory,
        \Magento\Framework\HTTP\Client\Curl $curl,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Magento\Framework\Filesystem\Driver\File $file,
        \Webkul\Marketplace\Helper\Data $mphelper,
        \Webkul\Marketplace\Model\ProductFactory $mpProductFactory,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        \Laminas\Uri\Uri $laminasUri,
        \Magento\Catalog\Api\AttributeSetRepositoryInterface $attributeSetRepository,
        \Coditron\Mpmultistorewoocommerce\Logger\Logger                 $logger
    ) {
        $this->filterManager = $filterManager;
        $this->_storeManager = $storeManager;
        $this->_scopeConfig = $context->getScopeConfig();
        $this->_woocommerceAccountsFactory = $woocommerceAccountsFactory;
        $this->rootCat = $rootCat;
        $this->woocommerceCategoryMapRepository = $woocommerceCategoryMapRepository;
        $this->woocommerceAccountsRepository = $woocommerceAccountsRepository;
        $this->importedTmpProductRepository = $importedTmpProductRepository;
        $this->product = $product;
        $this->productRepository = $productRepository;
        $this->formkey = $formkey;
        $this->attributeFactory = $attributeFactory;
        $this->attrGroupCollection = $attrGroupCollection;
        $this->attrOptionCollectionFactory = $attrOptionCollectionFactory;
        $this->attributeManagement = $attributeManagement;
        $this->productAttribute = $productAttribute;
        $this->registry = $registry;
        $this->filesystem = $filesystem;
        $this->file = $file;
        $this->configurableAttributeModel = $configurableAttributeModel;
        $this->configurableProTypeModel = $configurableProTypeModel;
        $this->indexerFactory = $indexerFactory;
        $this->encryptor = $encryptor;
        $this->cartRepositoryInterface = $cartRepositoryInterface;
        $this->cartManagementInterface = $cartManagementInterface;
        $this->shippingRate = $shippingRate;
        $this->order = $order;
        $this->customerRepository = $customerRepository;
        $this->customerFactory = $customerFactory;
        $this->backendSession = $backendSession;
        $this->countryHelper = $countryHelper;
        $this->countryFactory = $countryFactory;
        $this->curl = $curl;
        $this->jsonHelper = $jsonHelper;
        $this->templateProcessor = $templateProcessor;
        $this->mphelper = $mphelper;
        $this->mpProductFactory = $mpProductFactory;
        $this->_date = $date;
        $this->laminasUri = $laminasUri;
        $this->attributeSetRepository = $attributeSetRepository;
        $this->logger = $logger;
        parent::__construct($context);
    }

    /**
     * Check if the extension is enabled
     */
     public function isEnabled()
     { 
        return $this->scopeConfig->getValue(
            'mpmultistorewoocommerce/general_settings/enable',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        ); 
     }
  
    public function isOrderSyncEnable(){
        return $this->scopeConfig->getValue( 
            'mpmultistorewoocommerce/general_settings/order_sync_enable', 
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE 
        );  
    }

    /**
     * Get store base currency code
     *
     * @return string
     */
    public function getBaseCurrencyCode()
    {
        return $this->_storeManager->getStore()->getBaseCurrencyCode();
    }

    /**
     * Get Logger for get log
     *
     * @return \Coditron\Mpmultistorewoocommerce\Logger\Logger
     */
    public function getLogger()
    {
        return $this->logger;
    }

    /**
     * LogInfoMessage function log the info message
     *
     * @param string $msg
     * @return void
     */
    public function logInfoMessage($msg = '')
    {
        $this->getLogger()->info($msg);
    }

    /**
     * LogCriticalMessage function log the critical message
     *
     * @param string $msg
     * @return void
     */
    public function logCriticalMessage($msg = '')
    {
        $this->getLogger()->critical($msg);
    }

    /**
     * AuthorizeWooCommerceShop function authorizes the WooCommerce shop credentials and returns the shop details
     *
     * @param array $data
     * @return array
     */
    public function authorizeWooCommerceShop($data = [])
    {
        try {
            $url = rtrim($data['woocommerce_url'], '/') . "/wp-json/wc/v3/system_status";
            $consumerKey = $data['woocommerce_consumer_key'];
            $consumerSecret = $data['woocommerce_consumer_secret_key'];
            $oauthSignatureMethod = 'HMAC-SHA1';
            $oauthTimestamp = time();
            $oauthNonce = bin2hex(random_bytes(8));
            $oauthVersion = '1.0';
            $baseString = "GET&" . rawurlencode($url) . "&" . rawurlencode("oauth_consumer_key=$consumerKey&oauth_nonce=$oauthNonce&oauth_signature_method=$oauthSignatureMethod&oauth_timestamp=$oauthTimestamp&oauth_version=$oauthVersion");
            $signingKey = rawurlencode($consumerSecret) . "&";
            $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));
            $finalUrl = $url . "?oauth_consumer_key=$consumerKey&oauth_signature_method=$oauthSignatureMethod&oauth_timestamp=$oauthTimestamp&oauth_nonce=$oauthNonce&oauth_version=$oauthVersion&oauth_signature=" . rawurlencode($oauthSignature);

            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $finalUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPGET, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8'
            ]);

            $responseBody = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if (curl_errno($ch)) {
                $this->logCriticalMessage("Curl error: " . curl_error($ch));
            }

            curl_close($ch);
            if ($status != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }

            $response = json_decode($responseBody, true);
            if (!isset($response['environment']['version']) || !isset($response['settings']['currency'])) {
                return [
                    'status' => false,
                    'error_msg' => 'Invalid response from WooCommerce API'
                ];
            }
            $data = array();
            $data['status'] = true;
            $data['woocommerce_version'] = $response['environment']['version'];
            $data['woocommerce_base_currency'] = $response['settings']['currency'];
            $data['api_request_http_code'] = $status;
            return $data;

        } catch (\Exception $e) {
            $this->logCriticalMessage("authorizeWooCommerceShop :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Woo commerce user didn\'t authorize successfully, Please try again and check the log.')
            ];
        }
    }
   
    /**
     * Decodes the given $string string which is
     *
     * Encoded in the JSON format
     *
     * @param string $string
     * @return array
     */
    public function jsonDecoder($string = '')
    {
        $decodedData = $this->jsonHelper->jsonDecode($string);
        return $decodedData;
    }

    /**
     * GetTopLevelMageCategory function
     *
     * @return array
     */
    public function getTopLevelMageCategory()
    {
        return $this->rootCat->create()->tomageCatArray();
    }

     /**
     * getTopLevelWoocommerceCategory function
     *
     * @return array
     */
    public function getTopLevelWoocommerceCategory()
    {
        return $this->rootCat->create()->towoocommerceCatArray();
    }

     /**
     * GetTheShopApiCredentials function get the shopify shop private app credentials
     *
     * @param int $id
     * @param boolean $isDecrypt
     * @return array
     */
    public function getTheShopApiCredentials($id = "", $isDecrypt = false)
    {
        try {
            $woocommerceAccountsModel = $this->_woocommerceAccountsFactory->create();
            $woocommerceAccountsModel->load($id);
            $consumerkey = $woocommerceAccountsModel->getWoocommerceConsumerKey();
            $conumersecreatkey = $woocommerceAccountsModel->getWoocommerceConsumerSecretKey();
            $domainName = $woocommerceAccountsModel->getWoocommerceUrl();

            if (!$isDecrypt) {
                return [
                    'woocommerce_consumer_key'=> $consumerkey,
                    'woocommerce_consumer_secret_key'=> $conumersecreatkey,
                    'woocommerce_url'=> $domainName,
                ];
            }

            $cred = $this->decryptTheApiCredential($consumerkey, $conumersecreatkey);
            return [
                'woocommerce_consumer_key'=> $cred['woocommerce_consumer_key'],
                'woocommerce_consumer_secret_key'=> $cred['woocommerce_consumer_secret_key'],
                'woocommerce_url'=> $domainName,
            ];
        }catch (\Exception $e) {
           $this->logCriticalMessage("Error retrieving Woocommerce account data for ID $id: " . $e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Unable to retrieve the Woocommerce account data.')
            ];
        }
    }

    /**
     * GecryptTheApiCredential function decrypt the shop credentials
     *
     * @param string $apiKey
     * @param string $pwd
     * @return array
     */
    public function decryptTheApiCredential($consumerkey = '', $conumersecreatkey = '')
    {
        try {
            $decryptedConsumerKey = $this->encryptor->decrypt($consumerkey);
            $decryptedConsumerSecretKey = $this->encryptor->decrypt($conumersecreatkey);
            return [
                'woocommerce_consumer_key' => $decryptedConsumerKey,
                'woocommerce_consumer_secret_key' => $decryptedConsumerSecretKey
            ];
        } catch (\Exception $e) {
            $this->logCriticalMessage('Error decrypting API credentials: ' . $e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Unable to decrypt the API credentials.')
            ];
        }
    }

     /**
     * getTheCustomCollectionFromWoocommerce function get the categoreis (custom collections) from woo commerce
     *
     * @param string $id
     * @return array
     */
    public function getTheCustomCollectionFromWoocommerce($id = '')
    {
        try {
            $credentials = $this->getTheShopApiCredentials($id, true);
            $errorMsg = '';
            $status = false;
            if($credentials != null){
                $url = rtrim($credentials['woocommerce_url'], '/') . "/wp-json/wc/v3/products/categories"; 

                $consumerKey = $credentials['woocommerce_consumer_key'];
                $consumerSecret = $credentials['woocommerce_consumer_secret_key'];
                $oauthSignatureMethod = 'HMAC-SHA1';
                $oauthTimestamp = time();
                $oauthNonce = bin2hex(random_bytes(8));
                $oauthVersion = '1.0';

                $baseString = "GET&" . rawurlencode($url) . "&" . rawurlencode("oauth_consumer_key=$consumerKey&oauth_nonce=$oauthNonce&oauth_signature_method=$oauthSignatureMethod&oauth_timestamp=$oauthTimestamp&oauth_version=$oauthVersion");
                $signingKey = rawurlencode($consumerSecret) . "&";
                $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));

                $finalUrl = $url . "?oauth_consumer_key=$consumerKey&oauth_signature_method=$oauthSignatureMethod&oauth_timestamp=$oauthTimestamp&oauth_nonce=$oauthNonce&oauth_version=$oauthVersion&oauth_signature=" . rawurlencode($oauthSignature);
                $ch = curl_init();

                curl_setopt($ch, CURLOPT_URL, $finalUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPGET, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Accept: application/json',
                    'Content-Type: application/json; charset=utf-8'
                ]);

                $responseBody = curl_exec($ch);
                $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

                if (curl_errno($ch)) {
                    $this->logCriticalMessage("Get the all woo commerce categories error: " . curl_error($ch));
                }

                curl_close($ch);
                if ($status != 200) {
                    return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
                }
                
                $categories = json_decode($responseBody, true);
                $parentIds = array_column($categories, 'parent');
                $filteredCategories = [];
                foreach ($categories as $category) {
                    if (!in_array($category['id'], $parentIds)) {
                        $filteredCategories[] = [
                            'id' => $category['id'],
                            'name' => $category['name'],
                            'slug' => $category['slug'],
                            'parent' => $category['parent'],
                            'self_url' => $category['_links']['self'][0]['href']
                        ];
                    }
                }
                return [
                    'status' => true,
                    'data' => $filteredCategories
                ];
            }
        }catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getTheCustomCollectionFromWoocommerce :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while mapping Woo commerce categories, Please check the log.')
            ];
        }
    }

    /**
     * checkTheWoocommerceMappingCategoryExist function check that mapped category exixt or not at woo commerce end
     *
     * @param string $id
     * @param int $collectionId
     * @return array
     */
    public function checkTheWoocommerceMappingCategoryExist($id = '', $collectionId = '')
    {
        try {
            $credentials = $this->getTheShopApiCredentials($id, true);
            $errorMsg = '';
            $status = false;
            if($credentials != null){
                $url = rtrim($credentials['woocommerce_url'], '/') . "/wp-json/wc/v3/products/categories/$collectionId"; 
                $consumerKey = $credentials['woocommerce_consumer_key'];
                $consumerSecret = $credentials['woocommerce_consumer_secret_key'];
                $oauthSignatureMethod = 'HMAC-SHA1';
                $oauthTimestamp = time();
                $oauthNonce = bin2hex(random_bytes(8));
                $oauthVersion = '1.0';
                $baseString = "GET&" . rawurlencode($url) . "&" . rawurlencode("oauth_consumer_key=$consumerKey&oauth_nonce=$oauthNonce&oauth_signature_method=$oauthSignatureMethod&oauth_timestamp=$oauthTimestamp&oauth_version=$oauthVersion");
                $signingKey = rawurlencode($consumerSecret) . "&";
                $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));
                $finalUrl = $url . "?oauth_consumer_key=$consumerKey&oauth_signature_method=$oauthSignatureMethod&oauth_timestamp=$oauthTimestamp&oauth_nonce=$oauthNonce&oauth_version=$oauthVersion&oauth_signature=" . rawurlencode($oauthSignature);

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $finalUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPGET, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Accept: application/json',
                    'Content-Type: application/json; charset=utf-8'
                ]);

                $responseBody = curl_exec($ch);
                $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

                if (curl_errno($ch)) {
                    $this->logCriticalMessage("checking maped category exist error: " . curl_error($ch));
                }

                curl_close($ch);

                if ($status != 200) {
                    return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
                }
                
                $categories = json_decode($responseBody, true);

                return [
                    'status' => true,
                    'data' => $categories
                ];
            }
        }catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data checkTheWoocommerceMappingCategoryExist :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while mapping woocommerce categories, Please check the log.')
            ];
        }
    }

    /**
     * Is Mage Category Mapped
     *
     * @param int $leafMageCategory
     * @param int $ruleId
     * @return bool
     */
    public function isMageCategoryMapped($leafMageCategory, $ruleId)
    {
        try {
            $woocommerceExistCatColl = $this->woocommerceCategoryMapRepository
                ->getCollectionByMageCateIdnRuleId($leafMageCategory, $ruleId);

            if ($woocommerceExistCatColl->getSize()) {
                foreach ($woocommerceExistCatColl as $woocommerceExistCat) {
                    return $woocommerceExistCat->getEntityId();
                }
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("Error checking if Mage category $leafMageCategory is mapped with rule ID $ruleId: " . $e->getMessage());
            return false;
        }
        
    }

    /**
     * Get mapped category data
     *
     * @param int $ruleId
     * @return void
     */
    public function getMappedCategoryData($ruleId)
    {
        try {
            $woocommerceExistCatColl = $this->woocommerceCategoryMapRepository->getCollectionByRuleId($ruleId);

            if ($woocommerceExistCatColl->getSize()) {
                return $woocommerceExistCatColl->toArray();
            }
        }catch (\Exception $e) {
            $this->logCriticalMessage("Error getting if mapped category data" . $e->getMessage());
            return false;
        }
    }

    /**
     * GetShopifyConfiguration function get the woo commerce account configuration
     *
     * @param string $id
     * @return void
     */
    public function getWoocommerceConfiguration($id = "")
    {
        return $this->woocommerceAccountsRepository->getConfigurationById($id)->toArray();
    }

    /**
     * GetwoocommerceProductListFromShopCategoryId function get the shopify product list from shopify
     *
     * @param array $shopifyShopConfiguration
     * @param int $shopifyProductId
     * @param string $field
     * @return array
     */
    public function getwoocommerceProductListFromShopCategryId(
        $shopConfiguration = [],
        $categoryId = ''
    ){
        try {
            if($shopConfiguration != null){
                $url = rtrim($shopConfiguration['woocommerce_url'], '/') . "/wp-json/wc/v3/products";
                $category = $categoryId; 
                $consumerKey = $shopConfiguration['woocommerce_consumer_key'];
                $consumerSecret = $shopConfiguration['woocommerce_consumer_secret_key'];
                $oauthSignatureMethod = 'HMAC-SHA1';
                $oauthTimestamp = time();
                $oauthNonce = bin2hex(random_bytes(8));
                $oauthVersion = '1.0';
                $queryParams = [
                    'category' => $category,
                    'oauth_consumer_key' => $consumerKey,
                    'oauth_nonce' => $oauthNonce,
                    'oauth_signature_method' => $oauthSignatureMethod,
                    'oauth_timestamp' => $oauthTimestamp,
                    'oauth_version' => $oauthVersion
                ];

                $baseParams = http_build_query($queryParams, '', '&', PHP_QUERY_RFC3986);
                $baseString = "GET&" . rawurlencode($url) . "&" . rawurlencode($baseParams);
                $signingKey = rawurlencode($consumerSecret) . "&";
                $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));
                $queryParams['oauth_signature'] = $oauthSignature;
                $finalUrl = $url . '?' . http_build_query($queryParams, '', '&', PHP_QUERY_RFC3986);
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $finalUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPGET, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Accept: application/json',
                    'Content-Type: application/json; charset=utf-8'
                ]);

                $responseBody = curl_exec($ch);
                $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                if (curl_errno($ch)) {
                    $this->logCriticalMessage("Getting all product from woo commerce error: " . curl_error($ch));
                }
                curl_close($ch);
                if ($status != 200) {
                    return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
                }
                $productIds=array();
                $product = json_decode($responseBody, true);
                foreach ($product as $products) {
                    if (isset($products['id'])) {
                        $productIds[] = $products['id'];
                    }
                }

                return [
                    'status' => true,
                    'data' => $product,
                    'count'=> count($product),
                    'product_id' =>$productIds
                ];
            }
        }catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getwoocommerceProductListFromShopCategryId :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while Getting all product from woo commerce, Please check the log.')
            ];
        }
    }

    /**
     * AuthorizeWooCommerceShop function authorizes the WooCommerce shop credentials and returns the shop details
     *
     * @param array $data
     * @return array
     */
    public function getWooCommerceProductAttributes($data = [])
    {
        try {
            $url = rtrim($data['woocommerce_url'], '/') . "/wp-json/wc/v3/products/attributes";
            $consumerKey = $data['woocommerce_consumer_key'];
            $consumerSecret = $data['woocommerce_consumer_secret_key'];
            $oauthSignatureMethod = 'HMAC-SHA1';
            $oauthTimestamp = time();
            $oauthNonce = bin2hex(random_bytes(8));
            $oauthVersion = '1.0';
            $baseString = "GET&" . rawurlencode($url) . "&" . rawurlencode("oauth_consumer_key=$consumerKey&oauth_nonce=$oauthNonce&oauth_signature_method=$oauthSignatureMethod&oauth_timestamp=$oauthTimestamp&oauth_version=$oauthVersion");
            $signingKey = rawurlencode($consumerSecret) . "&";
            $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));
            $finalUrl = $url . "?oauth_consumer_key=$consumerKey&oauth_signature_method=$oauthSignatureMethod&oauth_timestamp=$oauthTimestamp&oauth_nonce=$oauthNonce&oauth_version=$oauthVersion&oauth_signature=" . rawurlencode($oauthSignature);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $finalUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPGET, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8'
            ]);

            $responseBody = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if (curl_errno($ch)) {
                $this->logCriticalMessage("Getting product attribute from woo commerce error: " . curl_error($ch));
            }

            curl_close($ch);

            if ($status != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }

            $response = json_decode($responseBody, true);
            $data = array();
            $attributesArray = [];
            foreach ($response as $attribute) {
                $attributesArray[] = [
                    'id' => $attribute['id'],
                    'name' => $attribute['name'],
                    'slug' => $attribute['slug'],
                    'type' => $attribute['type']
                ];
            }
            $data['status'] = true;
            $data['attribute'] = $attributesArray;
            return $data;

        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getWooCommerceProductAttributes :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while Getting  product attribute data from woo commerce, Please check the log.')
            ];
        }
    }
    /**
     * Fetches WooCommerce product attribute terms based on the provided attribute ID.
     *
     * @param array $data
     * @param string $id
     * @return array
     */
    public function getWooCommerceProductAttributesTerms($data = [], $id = "0")
    {
        try {
            $url = rtrim($data['woocommerce_url'], '/') . "/wp-json/wc/v3/products/attributes/{$id}/terms";
            $consumerKey = $data['woocommerce_consumer_key'];
            $consumerSecret = $data['woocommerce_consumer_secret_key'];
            $oauthSignatureMethod = 'HMAC-SHA1';
            $oauthTimestamp = time();
            $oauthNonce = bin2hex(random_bytes(8));
            $oauthVersion = '1.0';
            $baseString = "GET&" . rawurlencode($url) . "&" . rawurlencode("oauth_consumer_key=$consumerKey&oauth_nonce=$oauthNonce&oauth_signature_method=$oauthSignatureMethod&oauth_timestamp=$oauthTimestamp&oauth_version=$oauthVersion");
            $signingKey = rawurlencode($consumerSecret) . "&";
            $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));
            $finalUrl = $url . "?oauth_consumer_key=$consumerKey&oauth_signature_method=$oauthSignatureMethod&oauth_timestamp=$oauthTimestamp&oauth_nonce=$oauthNonce&oauth_version=$oauthVersion&oauth_signature=" . rawurlencode($oauthSignature);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $finalUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPGET, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8'
            ]);

            $responseBody = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if (curl_errno($ch)) {
                $this->logCriticalMessage("Getting product attribute terms from woo commerce error: " . curl_error($ch));
            }

            curl_close($ch);

            if ($status != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            
            $response = json_decode($responseBody, true);
            $data = array();
            $attributesArray = [];
            foreach ($response as $attribute) {
                $attributesArray[] = [
                    'id' => $attribute['id'],
                    'name' => $attribute['name'],
                    'slug' => $attribute['slug']
                ];
            }
            $data['status'] = true;
            $data['attribute'] = $attributesArray;
            return $data;

        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getWooCommerceProductAttributesTerms :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while Getting  product attribute terms data from woo commerce, Please check the log.')
            ];
        } 
    }

    /**
     * Fetches WooCommerce product attribute terms based on the provided attribute ID.
     *
     * @param array $data
     * @param string $id
     * @return array
     */
    public function getWooCommerceVariationProduct($data = [], $id = "0")
    {
        try {
            $url = rtrim($data['woocommerce_url'], '/') . "/wp-json/wc/v3/products/{$id}";
            $consumerKey = $data['woocommerce_consumer_key'];
            $consumerSecret = $data['woocommerce_consumer_secret_key'];
            $oauthSignatureMethod = 'HMAC-SHA1';
            $oauthTimestamp = time();
            $oauthNonce = bin2hex(random_bytes(8));
            $oauthVersion = '1.0';
            $baseString = "GET&" . rawurlencode($url) . "&" . rawurlencode("oauth_consumer_key=$consumerKey&oauth_nonce=$oauthNonce&oauth_signature_method=$oauthSignatureMethod&oauth_timestamp=$oauthTimestamp&oauth_version=$oauthVersion");
            $signingKey = rawurlencode($consumerSecret) . "&";
            $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));
            $finalUrl = $url . "?oauth_consumer_key=$consumerKey&oauth_signature_method=$oauthSignatureMethod&oauth_timestamp=$oauthTimestamp&oauth_nonce=$oauthNonce&oauth_version=$oauthVersion&oauth_signature=" . rawurlencode($oauthSignature);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $finalUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPGET, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8'
            ]);

            $responseBody = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if (curl_errno($ch)) {
                $this->logCriticalMessage("Getting variation product from woo commerce error: " . curl_error($ch));
            }

            curl_close($ch);

            if ($status != 200) {
                return ['status'=>false, 'error_msg'=> $this->jsonDecoder($this->curl->getBody())['errors']];
            }
            
            $response = json_decode($responseBody, true);
            $data = array();
            $data['status'] = true;
            $data['variationproduct'] = $response;
            return $data;

        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getWooCommerceVariationProduct :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while Getting variation product data from woo commerce, Please check the log.')
            ];
        }  
    }

    /**
     *  Get default attribute set
     *
     * @param int $shopId
     * @return int
     */
    public function getAttributeSetId($shopId = "")
    { 
        return $this->getWoocommerceConfiguration($shopId)['attribute_set_id']; 
    }

    /**
     * getWooCommerceProductList function
     *
     * @param array $WooCommerceShopConfiguration
     * @return array
     */
    public function getWooCommerceProductList($WooCommerceShopConfiguration = [])
    {
        try {
            $url = $WooCommerceShopConfiguration['woocommerce_url'] . "wp-json/wc/v3/products";
            $consumerKey = $WooCommerceShopConfiguration['woocommerce_consumer_key'];
            $consumerSecret = $WooCommerceShopConfiguration['woocommerce_consumer_secret_key'];
            $cred = $this->decryptTheApiCredential($consumerKey, $consumerSecret);
            $params = [
                'consumer_key' => $cred['woocommerce_consumer_key'],
                'consumer_secret' => $cred['woocommerce_consumer_secret_key'],
                'per_page' => 100
            ];
            $queryString = http_build_query($params);
            $url .= '?' . $queryString;
            $arr = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
            ];

            $this->curl->setOptions($arr);
            $this->curl->get($url);

            if ($this->curl->getStatus() != 200) {
                return ['status' => false, 'error_msg' => $this->jsonDecoder($this->curl->getBody())['message']];
            }

            if ($this->curl->getStatus() == 200) {
                $products = $this->jsonDecoder($this->curl->getBody());
                return [
                    'status' => true,
                    'total_imported' => count($products) == 0 ? 1 : 0,
                    'data' => $products,
                ];
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getWooCommerceProductList :".$e->getMessage());
            return [
                'status'=>false,
                'error_msg'=>__('Something wrong while Getting all product list data from woo commerce, Please check the log.')
            ];
        }
    }

    public function getCategData($id)
    {
        $categoriesArr = [];
        try {
            $wooCommerCat = $this->getTheCustomCollectionFromWoocommerce($id);
            if (isset($wooCommerCat['data'])) {
                foreach ($wooCommerCat['data'] as $category) {
                    $categoriesArr[] = ['value' => $category['id'],'label' => $category['name']];
                }
            }
            return $categoriesArr;
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getCategData :".$e->getMessage());
            return $categoriesArr;
        }
    }

    /**
     * Get count of imported items.
     *
     * @param string $itemType
     * @param int $ruleId
     * @return int
     */
    public function getTotalImportedCount($itemType, $ruleId)
    {
        try {
            $tempProCollection = $this->importedTmpProductRepository 
                ->getCollectionByProductTypeAndRuleId($itemType, $ruleId);
            return $tempProCollection->getSize();
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getTotalImportedCount :".$e->getMessage());
            return 0;
        }
    }

    public function getruleid($sellerId){
        try {
            $woocommerceAccountsModel = $this->_woocommerceAccountsFactory->create();
            $woocommerceAccountsModel->addFieldToFilter('seller_id', $sellerId);
            $woocommerceAccountsModel->load();
            return $woocommerceAccountsModel->getId();
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getruleid :".$e->getMessage());
            return 0;
        }
    }

    public function SyncOrder($order, $sellerId, $orderItems){
        try {
            $id = $this->getruleid($sellerId);
            $details = $this->getTheShopApiCredentials($id);
            if (!$details || !isset($details['woocommerce_consumer_key'], $details['woocommerce_consumer_secret_key'], $details['woocommerce_url'])) {
                throw new \Exception("WooCommerce API credentials not found for seller ID: $sellerId");
            }
            $consumerKey = $details['woocommerce_consumer_key'];
            $consumerSecret = $details['woocommerce_consumer_secret_key'];
            $cred = $this->decryptTheApiCredential($consumerKey, $consumerSecret);
            $orderData = $this->prepareOrderData($order, $sellerId, $orderItems);
            if(!empty($orderData)){
                $url = rtrim($details['woocommerce_url'], '/') . "/wp-json/wc/v3/orders";
                if (!empty($orderData)) {
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 30,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => json_encode($orderData),
                    CURLOPT_HTTPHEADER => array(
                        'Content-Type: application/json',
                        'Authorization: Basic ' . base64_encode($consumerKey . ':' . $consumerSecret),
                    ),
                    ));
                    $response = curl_exec($curl);
                    if (curl_errno($curl)) {
                        $error_msg = curl_error($curl);
                        curl_close($curl);
                        throw new \Exception("Curl error: $error_msg");
                    }
                    $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                    curl_close($curl);
                    if ($http_status != 201) {
                        throw new \Exception("Failed to create order. HTTP status code: $http_status");
                    }
                    return true;
                }
                throw new \Exception("Order data is empty or invalid for seller ID: $sellerId");
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getruleid :".$e->getMessage());
            return false;
        }
    }

    public function prepareOrderData($order, $sellerId, $orderItems) {
        try{
            $paymentMethod = $order->getPaymentMethod();
            $paymentMethodTitle = $order->getPaymentMethodTitle();
            $shippingMethod = $order->getShippingMethod();
            $shippingMethodTitle = $order->getShippingMethodTitle();
            $shippingTotal = $order->getShippingTotal();
            
            $orderData = [
                "payment_method" => $paymentMethod,
                "payment_method_title" => $paymentMethodTitle,
                "set_paid" => true,
                "billing" => [
                    "first_name" => $order->getBillingFirstName(),
                    "last_name" => $order->getBillingLastName(),
                    "address_1" => $order->getBillingAddress1(),
                    "address_2" => $order->getBillingAddress2(),
                    "city" => $order->getBillingCity(),
                    "state" => $order->getBillingState(),
                    "postcode" => $order->getBillingPostcode(),
                    "country" => $order->getBillingCountry(),
                    "email" => $order->getBillingEmail(),
                    "phone" => $order->getBillingPhone()
                ],
            
                "shipping" => [
                    "first_name" => $order->getShippingFirstName(),
                    "last_name" => $order->getShippingLastName(),
                    "address_1" => $order->getShippingAddress1(),
                    "address_2" => $order->getShippingAddress2(),
                    "city" => $order->getShippingCity(),
                    "state" => $order->getShippingState(),
                    "postcode" => $order->getShippingPostcode(),
                    "country" => $order->getShippingCountry()
                ],
            
                "line_items" => array_map(function($item) {
                    return [
                        "product_id" => $item->getProductId(),
                        "variation_id" => $item->getVariationId(),
                        "quantity" => $item->getQuantity()
                    ];
                }, $order->getItems()),
            
                "shipping_lines" => [
                    [
                        "method_id" => $shippingMethod,
                        "method_title" => $shippingMethodTitle,
                        "total" => $shippingTotal
                    ]
                ]
            ];
            
            if(!empty($orderData['line_items'])){
                return $orderData;
            }else{
                return [];
            }
        } catch (\Exception $e) {
            $this->logCriticalMessage("Helper Data getruleid :".$e->getMessage());
            return false;
        }
    }       
}