<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Product;

class Profiler extends \Magento\Framework\View\Element\Template
{
    /**
     * @var \Webkul\Shopifymagentoconnect\Helper\Data
     */
    private $helperData;

    /**
     * Construct function
     *
     * @param \Magento\Backend\Block\Widget\Context $context
     * @param \Coditron\Mpmultistorewoocommerce\Helper\Data $helperData
     * @param array $data
     */
    public function __construct(
        \Magento\Backend\Block\Widget\Context $context,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $helperData,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->helperData = $helperData;
    }

    /**
     * For get total imported product count.
     *
     * @return int
     */
    public function getImportedProduct()
    {
        $ruleId = $this->getRequest()->getParam('id');
        return $this->helperData->getTotalImportedCount('product', $ruleId);
    }

    /**
     * Get MappedCategories
     *
     * @return array
     */
    public function getMappedCategories()
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/getMappedCategories.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("In savemapping function");
        $cates = [];
        $ruleId = $this->getRequest()->getParam('id');
        $logger->info("Input data: " . print_r($ruleId, true));
        $mappedCatesArray =  $this->helperData->getMappedCategoryData($ruleId);
        $logger->info("Input data: " . print_r($mappedCatesArray, true));
        if (isset($mappedCatesArray['items'])) {
            foreach ($mappedCatesArray['items'] as $record) {
                $cates[] = $record['woocommerce_cat_id'];
            }
        }
        $configs = $this->helperData->getWoocommerceConfiguration($ruleId);
        $logger->info("Input data: " . print_r($configs, true));
        return [
            'isAllProImport' => $configs,
            'cates'          => implode(',', $cates)
        ];
    }

    /**
     * Get ProductImportScriptData
     *
     * @return json
     */
    public function getProductImportScriptData()
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/profilerproduct.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("In savemapping function");
        $id = $this->getRequest()->getParam('id');
        $logger->info("Input data: " . print_r($id, true));
        $mappedCates = $this->getMappedCategories();
        $logger->info("Input data: " . print_r($mappedCates, true));
        // $releventData = [
        //     'imageImportUrl' => $this->getUrl('*/products/imgprofiler', ['id' => $id]),
        //     'importProductSelector' => '#wc-import-product',
        //     'importAjaxUrl' => $this->getUrl('*/products/import'),
        //     'profilerSelector' => '#wc-run-profiler',
        //     'profilerAjaxUrl' => $this->getUrl('*/products/profiler', ['id'=>$id]),
        //     'isAllProImport' => $mappedCates['isAllProImport'],
        //     'cates' => $mappedCates['cates']
        // ];

        $releventData = [
            'isAllProImport' => $mappedCates['isAllProImport'],
            'cates' => $mappedCates['cates']
        ];
        return $this->helperData->jsonHelper->jsonEncode($releventData);
    }
}
