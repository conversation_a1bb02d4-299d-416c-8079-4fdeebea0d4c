<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Block\Adminhtml\ShopifyAccount\Edit\Tab;

use Magento\Backend\Block\Widget\Grid;
use Magento\Backend\Block\Widget\Grid\Column;
use Magento\Backend\Block\Widget\Grid\Extended;
use Coditron\Mpmultistorewoocommerce\Api\ProductmapRepositoryInterface;
use Coditron\Mpmultistorewoocommerce\Model\Config\Source\CategoriesList;
use Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tab\Renderer\MageCategoryName;

class MapProductGrid extends \Magento\Backend\Block\Widget\Grid\Extended
{

    /**
     * @var ProductmapRepository
     */
    private $_productmapRepository;

    /**
     * @var CategoriesList
     */
    private $categoriesList;

    /**
     * Construct function
     *
     * @param \Magento\Backend\Block\Template\Context  $context
     * @param \Magento\Backend\Helper\Data             $backendHelper
     * @param ProductmapRepositoryInterface            $productmapRepository
     * @param CategoriesList                           $categoriesList
     * @param array                                    $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Backend\Helper\Data $backendHelper,
        ProductmapRepositoryInterface $productmapRepository,
        CategoriesList $categoriesList,
        array $data = []
    ) {
        $this->_productmapRepository = $productmapRepository;
        $this->categoriesList = $categoriesList;
        parent::__construct($context, $backendHelper, $data);
    }

    /**
     * @inheritdoc
     */
    protected function _construct()
    {
        parent::_construct();
        $this->setId('woocommerce_map_product');
        $this->setDefaultSort('id');
        $this->setUseAjax(true);
    }

    /**
     * @inheritdoc
     */
    protected function _prepareCollection()
    {
        $id =  $this->getRequest()->getParam('id');
        if ($id) {
            $collection = $this->_productmapRepository
                    ->getCollectionByRuleId($id);
        } else {
            $collection = $this->_mapProductCollection->create();
        }
        $this->setCollection($collection);
        return parent::_prepareCollection();
    }

    /**
     * @inheritdoc
     */
    protected function _prepareColumns()
    {
        $this->addColumn(
            'woocommerce_pro_id',
            [
                'header' => __('Woo Commerce Product Id'),
                'sortable' => true,
                'index' => 'woocommerce_pro_id',
                'header_css_class' => 'col-id',
                'column_css_class' => 'col-id'
            ]
        );
        $this->addColumn(
            'name',
            [
                'header' => __('Product Name'),
                'sortable' => true,
                'index' => 'name'
            ]
        );
        $this->addColumn(
            'product_type',
            [
                'header' => __('Type'),
                'sortable' => false,
                'index' => 'product_type'
            ]
        );
        $this->addColumn(
            'magento_pro_id',
            [
                'header' => __('Store Product Id'),
                'sortable' => false,
                'index' => 'magento_pro_id'
            ]
        );
        $this->addColumn(
            'mage_cat_id',
            [
                'header' => __('Store Category'),
                'sortable' => false,
                'index' => 'mage_cat_id',
                'filter' => false,
                'renderer'  => MageCategoryName::class
            ]
        );
        $this->addColumn(
            'sync_date',
            [
                'header' => __('Sync Date'),
                'sortable' => true,
                'filter' => false,
                'index' => 'sync_date'
            ]
        );
        return parent::_prepareColumns();
    }

    /**
     * Get massaction
     *
     * @return object
     */
    protected function _prepareMassaction()
    {
        $asinCatOptions = $this->categoriesList->toOptionArray();
        $ruleId = $this->getRequest()->getParam('id');
        $this->setMassactionIdField('entity_id');
        $this->setChild('massaction', $this->getLayout()->createBlock($this->getMassactionBlockName()));
        $this->getMassactionBlock()->setFormFieldName('productEntityIds');
        $this->getMassactionBlock()->addItem(
            'delete',
            [
                'label' => __('Delete'),
                'url' => $this->getUrl(
                    '*/*/MassDelete',
                    [
                        'rule_id'=>$ruleId
                    ]
                ),
                'confirm' => __('Are you sure want to delete?')
            ]
        )->addItem(
            'massassigncate',
            [
                'label'=> __('Assign to category'),
                'url'=> $this->getUrl(
                    '*/*/massassigntocategory',
                    [
                        'rule_id'=>$ruleId
                    ]
                ),
                'additional'=> [
                    'visibility'=> [
                    'name'=> 'magecate',
                    'type'=> 'select',
                    'label'=> __('Assign to category'),
                    'values'=> $asinCatOptions
                    ]
                ]
            ]
        );
        $this->getMassactionBlock()->setTemplate(
            'Coditron_Mpmultistorewoocommerce::widget/grid/massaction_extended.phtml'
        );
        return $this;
    }

    /**
     * Get Grid Url
     *
     * @return string
     */
    public function getGridUrl()
    {
        return $this->getUrl('mpmultistorewoocommerce/*/resetgrid', ['_current' => true]);
    }
}
