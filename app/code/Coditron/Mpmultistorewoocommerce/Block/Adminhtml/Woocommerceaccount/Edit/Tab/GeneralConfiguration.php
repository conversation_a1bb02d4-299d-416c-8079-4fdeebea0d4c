<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tab;

use \Coditron\Mpmultistorewoocommerce\Model\Config\Source;

class GeneralConfiguration extends \Magento\Backend\Block\Widget\Form\Generic
{
    /** @var \Coditron\Mpmultistorewoocommerce\Model\Config\Source\AllStoreList */
    protected $allStoreList;
    
    /** @var \Coditron\Mpmultistorewoocommerce\Model\Config\Source\CategoriesList */
    protected $categoriesList;
     
    /** @var \Coditron\Mpmultistorewoocommerce\Model\Config\Source\AllWebsiteList */
    protected $allwebsiteList;
    
    /** @var \Magento\Sales\Model\Config\Source\Order\Status */
    protected $orderStatus;
    
    /** @var \Coditron\Mpmultistorewoocommerce\Helper\Data */
    protected $helper;

    /**
     * Construct function
     *
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Data\FormFactory $formFactory
     * @param \Coditron\Mpmultistorewoocommerce\Model\Config\Source\AllStoreList $allStoreList
     * @param \Coditron\Mpmultistorewoocommerce\Model\Config\Source\CategoriesList $categoriesList
     * @param \Coditron\Mpmultistorewoocommerce\Model\Config\Source\AllWebsiteList $allwebsiteList
     * @param \Magento\Sales\Model\Config\Source\Order\Status $orderStatus
     * @param \Coditron\Mpmultistorewoocommerce\Helper\Data $helper
     * @param array $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\FormFactory $formFactory,
        \Coditron\Mpmultistorewoocommerce\Model\Config\Source\AllStoreList $allStoreList,
        \Coditron\Mpmultistorewoocommerce\Model\Config\Source\CategoriesList $categoriesList,
        \Coditron\Mpmultistorewoocommerce\Model\Config\Source\AllWebsiteList $allwebsiteList,
        \Magento\Sales\Model\Config\Source\Order\Status $orderStatus,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $helper,
        array $data = []
    ) {
        $this->allStoreList = $allStoreList;
        $this->categoriesList = $categoriesList;
        $this->orderStatus = $orderStatus;
        $this->allwebsiteList = $allwebsiteList;
        $this->helper = $helper;
        parent::__construct($context, $registry, $formFactory, $data);
    }

    /**
     * Prepare form fields
     *
     * @return \Magento\Backend\Block\Widget\Form
     */
    protected function _prepareForm()
    {
        /** @var $model \Magento\User\Model\User */
        $model = $this->_coreRegistry->registry('woocommerceaccount_info');

        /** @var \Magento\Framework\Data\Form $form */
        $form = $this->_formFactory->create();
        $form->setHtmlIdPrefix('woocommerce_generalconfiguration_user_');

        $baseFieldset = $form->addFieldset(
            'generalConfiguration_fieldset',
            ['legend' => __('WooCommerce General Configuration')]
        );

        if ($model->getId()) {
            $baseFieldset->addField('entity_id', 'hidden', ['name' => 'entity_id']);
        } else {
            if (!$model->hasData('is_active')) {
                $model->setIsActive(1);
            }
        }

        $baseFieldset->addField(
            'default_cate',
            'select',
            [
                'name' => 'default_cate',
                'label' => __('Default Category'),
                'id' => 'default_cate',
                'title' => __('Default Category'),
                'values' => $this->categoriesList->toOptionArray(),
                'class' => 'required-entry',
                'required' => true
            ]
        );

        $baseFieldset->addField(
            'default_store_view',
            'select',
            [
                'name' => 'default_store_view',
                'label' => __('Default Store View'),
                'id' => 'default_store_view',
                'title' => __('Default Store View'),
                'values' => $this->allStoreList->toOptionArray(),
                'class' => 'required-entry',
                'required' => true
            ]
        );

        $baseFieldset->addField(
            'default_website',
            'select',
            [
                'name' => 'default_store_view',
                'label' => __('Default Website View'),
                'id' => 'default_store_view',
                'title' => __('Default Website View'),
                'values' => $this->allwebsiteList->toOptionArray(),
                'class' => 'required-entry',
                'required' => true
            ]
        );

        $baseFieldset->addField(
            'order_status',
            'select',
            [
                'name' => 'order_status',
                'label' => __('Order Status'),
                'id' => 'order_status',
                'title' => __('Order Status'),
                'values' => $this->orderStatus->toOptionArray(),
                'class' => 'required-entry',
                'required' => true
            ]
        );

       // Add Quantity Field
        $baseFieldset->addField(
            'default_quantity',
            'text',
            [
                'name' => 'default_quantity',
                'label' => __('Default Quantity'),
                'id' => 'default_quantity',
                'title' => __('Default Quantity'),
                'class' => 'required-entry',
                'required' => true
            ]
        );

        // Add Weight Field
        $baseFieldset->addField(
            'default_weight',
            'text',
            [
                'name' => 'default_weight',
                'label' => __('Default Weight'),
                'id' => 'default_weight',
                'title' => __('Default Weight'),
                'class' => 'required-entry',
                'required' => true
            ]
        );
        $form->setValues($model->getData());
        $this->setForm($form);
        return parent::_prepareForm();
    }
}
