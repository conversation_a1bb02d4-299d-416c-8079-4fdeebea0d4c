<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tab;

use Magento\Backend\Block\Widget\Grid;
use Magento\Backend\Block\Widget\Grid\Column;
use Magento\Backend\Block\Widget\Grid\Extended;
use Coditron\Mpmultistorewoocommerce\Api\WoocommercecategorymapRepositoryInterface;
use Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tab\Renderer\MageCategoryName;

class MapCategoryGrid extends \Magento\Backend\Block\Widget\Grid\Extended
{
    /**
     * @var WoocommercecategorymapRepositoryInterface
     */
    private $WoocommerceCategoryMapRepository;

    /**
     * Construct function
     *
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Backend\Helper\Data            $backendHelper
     * @param WoocommercecategorymapRepositoryInterface   $WoocommerceCategoryMapRepository
     * @param array                                   $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Backend\Helper\Data $backendHelper,
        WoocommercecategorymapRepositoryInterface $WoocommerceCategoryMapRepository,
        array $data = []
    ) {
        $this->WoocommerceCategoryMapRepository = $WoocommerceCategoryMapRepository;
        parent::__construct($context, $backendHelper, $data);
    }

    /**
     * Construct function
     *
     * @return void
     */
    protected function _construct()
    {
        parent::_construct();
        $this->setId('woocommerce_map_category');
        $this->setDefaultSort('id');
        $this->setUseAjax(true);
    }

    /**
     * @inheritdoc
     */
    protected function _prepareCollection()
    {
        $id = $this->getRequest()->getParam('id');
        $collection = null;
        try {
            if ($id) {
                $collection = $this->WoocommerceCategoryMapRepository->getCollectionByRuleId($id);
            } else {
                $collection = $this->WoocommerceCategoryMapRepository->create();
            }
            $this->setCollection($collection);

        } catch (\Exception $e) {
            $this->setCollection($this->WoocommerceCategoryMapRepository->create());
        }

        return parent::_prepareCollection();
    }

    /**
     * @inheritdoc
     */
    protected function _prepareColumns()
    {
        $this->addColumn(
            'entity_id',
            [
                'header' => __('Entity Id'),
                'sortable' => true,
                'index' => 'entity_id',
                'header_css_class' => 'col-id',
                'column_css_class' => 'col-id'
            ]
        );
        $this->addColumn(
            'mage_cat_id',
            [
                'header' => __('Store Category'),
                'sortable' => true,
                'index' => 'mage_cat_id',
                'filter' => false,
                'renderer'  => MageCategoryName::class
            ]
        );
        $this->addColumn(
            'woocommerce_cat_id',
            [
                'header' => __('Woo Commerce Category'),
                'sortable' => true,
                'filter' => false,
                'index' => 'woocommerce_cat_id'
            ]
        );
        $this->addColumn(
            'woocommerce_cat_name',
            [
                'header' => __('Woo commerce Category Name'),
                'sortable' => true,
                'filter' => false,
                'index' => 'woocommerce_cat_name'
            ]
        );
        $this->addColumn(
            'body_html',
            [
                'header' => __('Description'),
                'sortable' => true,
                'filter' => false,
                'index' => 'body_html'
            ]
        );
        $this->addColumn(
            'published_at_woocommerce',
            [
                'header' => __('Sync Date'),
                'sortable' => true,
                'filter' => false,
                'index' => 'published_at_woocommerce'
            ]
        );
        return parent::_prepareColumns();
    }

    /**
     * Get massaction
     *
     * @return object
     */
    protected function _prepareMassaction()
    {
        $ruleId = $this->getRequest()->getParam('id');
        $this->setMassactionIdField('entity_id');
        $this->setChild('massaction', $this->getLayout()->createBlock($this->getMassactionBlockName()));
        $this->getMassactionBlock()->setFormFieldName('cateEntityIds');
        $this->getMassactionBlock()->addItem(
            'delete',
            [
                'label' => __('Delete'),
                'url' => $this->getUrl('mpmultistorewoocommerce/*/massdelete', ['rule_id' => $ruleId]),
                'confirm' => __('Are you sure want to delete?')
            ]
        );
        $this->getMassactionBlock()->setTemplate('Coditron_Mpmultistorewoocommerce::widget/grid/massaction_extended.phtml');
        return $this;
    }

    /**
     * @inheritdoc
     */
    public function getGridUrl()
    {
        return $this->getUrl('mpmultistorewoocommerce/*/resetgrid', ['_current' => true]);
    }
}
