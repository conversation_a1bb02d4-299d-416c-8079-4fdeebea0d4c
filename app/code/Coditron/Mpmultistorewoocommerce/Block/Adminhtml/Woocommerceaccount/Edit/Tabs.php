<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit;

use \Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tab\Woocommerceaccount;
use \Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tab\MapCategoryGrid;
use \Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tab\GeneralConfiguration;
//use \Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tab\ListingConfiguration;

class Tabs extends \Magento\Backend\Block\Widget\Tabs
{

    /**
     * @inheritdoc
     */
    protected function _construct()
    {
        parent::_construct();
        $this->setId('woocommerceaccount_tab');
        $this->setDestElementId('edit_form');
        $this->setTitle(__('Woo Commerce Account Information'));
    }

    /**
     * Prepare Layout
     *
     * @return object $this
     */
    protected function _prepareLayout()
    {
        $id = $this->getRequest()->getParam('id');
        $block = Woocommerceaccount::class;
        $mapCategory = MapCategoryGrid::class;
        $this->addTab(
            'woocommerceaccount',
            [
                'label' => __('WooCommerce Account Information'),
                'content' => $this->getLayout()->createBlock($block, 'woocommerce_account_info')->toHtml()
            ]
        );
     
        if ($id) {
            $this->addTab(
                'general_configuration',
                [
                    'label' => __('WooCommerce General Configuration'),
                    'title' => __('WooCommerce General Configuration'),
                    'content' => $this->getLayout()->createBlock(
                        GeneralConfiguration::class
                    )->toHtml(),
                    'active' => false
                ]
            );
            $this->addTab(
                'mapcategory',
                [
                    'label' => __('Category Mapping'),
                    'url'       => $this->getUrl('*/categories/mapcategory', ['_current' => true]),
                    'class'     => 'ajax',
                    'title'     => __('Category Mapping'),
                ]
            );
            $this->addTab(
                'mapproduct',
                [
                    'label' => __('Import Product From WooCommerce'),
                    'url'       => $this->getUrl('*/products/map', ['_current' => true]),
                    'class'     => 'ajax',
                    'title'     => __('Import Product From WooCommerce'),
                ]
            );
            // $this->addTab(
            //     'maporder',
            //     [
            //         'label' => __('Import Order From WooCommerce'),
            //         //'url'       => $this->getUrl('*/shopifyorder/index', ['_current' => true]),
            //         'class'     => 'ajax',
            //         'title'     => __('Import Order From WooCommerce'),
            //     ]
            // );
        }
        return parent::_prepareLayout();
    }
}
