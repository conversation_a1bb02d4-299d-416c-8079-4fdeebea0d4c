<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tab;

use Magento\Backend\Block\Widget\Form\Generic;
use Magento\Backend\Block\Widget\Tab\TabInterface;
//use Coditron\Mpmultistorewoocommerce\Model\Config\Source;

class Woocommerceaccount extends Generic implements TabInterface
{
    /** @var \Magento\Catalog\Model\Product\AttributeSet\Options */
    protected $_attributeSet;

     /**
     * @var \Magento\Framework\Encryption\EncryptorInterface
     */
    private $encryptor;
    
    /**
     * Construct function
     *
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Data\FormFactory $formFactory
     * @param \Magento\Catalog\Model\Product\AttributeSet\Options $attributeSet
     * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
     * @param array $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\FormFactory $formFactory,
        \Magento\Catalog\Model\Product\AttributeSet\Options $attributeSet,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        array $data = []
    ) {
        $this->_attributeSet = $attributeSet;
        $this->encryptor = $encryptor;
        parent::__construct($context, $registry, $formFactory, $data);
    }

    /**
     * Prepare form
     *
     * @return $this
     */
    protected function _prepareForm()
    {
        $model = $this->_coreRegistry->registry('woocommerceaccount_info');
        $form = $this->_formFactory->create();
        $fieldset = $form->addFieldset(
            'base_fieldset',
            ['legend' => __('Woo Commerce Account'), 'class' => 'fieldset-wide']
        );
        $afterElementHtml = '<p class="nm"><small>' . __('Set unique store name for your account') . '</small></p>';
       if ($model->getId()) {
            $fieldset->addField('entity_id', 'hidden', ['name' => 'id']);
            $fieldset->addField(
                'store_name',
                'text',
                [
                    'name' => 'store_name',
                    'label' => __('Store Name'),
                    'title' => __('Store Name'),
                    'readonly' => true,
                    'required' => true
                ]
            );

            $model->setData('woocommerce_consumer_key', $this->_escaper->escapeHtml($this->encryptor->decrypt($model->getWoocommerceConsumerKey())));
            $model->setData('woocommerce_consumer_secret_key', $this->_escaper->escapeHtml($this->encryptor->decrypt($model->getWoocommerceConsumerSecretKey())));

        } else {
            $fieldset->addField(
                'store_name',
                'text',
                [
                    'name' => 'store_name',
                    'label' => __('Store Name'),
                    'title' => __('Store Name'),
                    'required' => true,
                    'after_element_html' => $afterElementHtml
                ]
            );
        }

        $fieldset->addField(
            'woocommerce_url',
            'text',
            [
            'label' => __('Store url'),
            'title' => __('Store url'),
            'name' => 'woocommerce_url',
            'required' => true
            ]
        );

        $fieldset->addField(
            'attribute_set_id',
            'select',
            [
                'name' => 'attribute_set_id',
                'label' => __('Attribute set Id'),
                'title' => __('Attribute set Id'),
                'required' => true,
                'options' => $this->getAttributeSets()
            ]
        );

    /* $mainSelect = $fieldset->addField(
            'shopify_app_select',
            'select',
            [
                'label' => __('Use Custom App'),
                'title' => __('Use Custom App'),
                'name' => 'shopify_app_select',
                'required' => true,
                'options' => ['1'=>__('Yes'),'0'=>__('No')]
            ]
        ); */

        // $access_token = $fieldset->addField(
        //     'access_token',
        //     'obscure',
        //     [
        //         'label' => __('Access Token'),
        //         'title' => __('Access Token'),
        //         'name' => 'access_token',
        //         'required' => true
        //     ]
        // );

        $woocommerce_consumer_key = $fieldset->addField(
            'woocommerce_consumer_key',
            'text',
            [
                'label' => __('Woo Commrece Consumer Key'),
                'title' => __('Woo Commrece Consumer Key'),
                'name' => 'woocommerce_consumer_key',
                'required' => true
            ]
        );
        $woocommerce_consumer_secret_key = $fieldset->addField(
            'woocommerce_consumer_secret_key',
            'text',
            [
                'label' => __('Woo Commerce Consumer Secret Key'),
                'title' => __('Woo Commerce Consumer Secret Key'),
                'name' => 'woocommerce_consumer_secret_key',
                'required' => true
            ]
        );

       
        
      /*  $this->setChild(
            'form_after',
            $this->getLayout()->createBlock(\Magento\Backend\Block\Widget\Form\Element\Dependence::class)
            ->addFieldMap($mainSelect->getHtmlId(), $mainSelect->getName())
            ->addFieldMap($access_token->getHtmlId(), $access_token->getName())
            ->addFieldMap($woocommerce_consumer_key->getHtmlId(), $woocommerce_consumer_key->getName())
            ->addFieldDependence($access_token->getName(), $mainSelect->getName(), 1)
            ->addFieldDependence($woocommerce_consumer_key->getName(), $mainSelect->getName(), 0)
        );*/
        
        $form->setValues($model->getData());
        $this->setForm($form);
        return parent::_prepareForm();
    }

    /**
     * Prepare label for tab
     *
     * @return \Magento\Framework\Phrase
     */
    public function getTabLabel()
    {
        return __('Woo Commerce Account');
    }

    /**
     * Prepare title for tab
     *
     * @return \Magento\Framework\Phrase
     */
    public function getTabTitle()
    {
        return __('Woo Commerce Account');
    }

    /**
     * @inheritdoc
     */
    public function canShowTab()
    {
        return true;
    }

    /**
     * @inheritdoc
     */
    public function isHidden()
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function getAttributeSets()
    {
        $attributSetArray = [];
        $attributeSet =  $this->_attributeSet->toOptionArray();
        foreach ($attributeSet as $key => $value) {
            $attributSetArray[$value['value']] = $value['label'];
        }
        return $attributSetArray;
    }

    /**
     * Check permission for passed action
     *
     * @param string $resourceId
     *
     * @return bool
     */
    protected function _isAllowedAction($resourceId)
    {
        return $this->_authorization->isAllowed($resourceId);
    }
}
