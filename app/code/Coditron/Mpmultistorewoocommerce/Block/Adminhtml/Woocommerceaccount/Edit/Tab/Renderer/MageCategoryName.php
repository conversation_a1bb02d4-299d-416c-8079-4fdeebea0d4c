<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tab\Renderer;

use Magento\Framework\DataObject;

class MageCategoryName extends \Magento\Backend\Block\Widget\Grid\Column\Renderer\AbstractRenderer
{
    /** @var \Magento\Catalog\Model\CategoryFactory */
    protected $categoryFactory;

    /**
     * Construct function
     *
     * @param \Magento\Catalog\Model\CategoryFactory $categoryFactory
     */
    public function __construct(
        \Magento\Catalog\Model\CategoryFactory $categoryFactory
    ) {
        $this->categoryFactory = $categoryFactory;
    }

    /**
     * Get category name
     *
     * @param  DataObject $row
     * @return string
     */
    public function render(DataObject $row)
    {
        $mageCateId = $row->getMageCatId();
        $string = '';
        $getSynchProCat = $mageCateId ? explode(',', trim($mageCateId, ',')) : [];
        foreach ($getSynchProCat as $categoryId) {
            $catName = $this->categoryFactory->create()->load($categoryId)->getName();
            if ($catName == '') {
                continue;
            }
            $string .= $catName;
            $string .= ',';
        }
        $string = trim($string, ',');
        return $string;
    }
}
