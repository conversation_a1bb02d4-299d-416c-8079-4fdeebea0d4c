<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount;

class Edit extends \Magento\Backend\Block\Widget\Form\Container
{
    /**
     * Initialize Mpmultistorewoocommerce Woo commerce Account edit block
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_objectId = 'entity_id';
        $this->_blockGroup = 'Coditron_Mpmultistorewoocommerce';
        $this->_controller = 'adminhtml_Woocommerceaccount';
        parent::_construct();
        if ($this->_isAllowedAction('Coditron_Mpmultistorewoocommerce::woocommerce_account_connect')) {
            $this->buttonList->update('save', 'label', __('Save Woo Commerce Account'));
            $this->buttonList->add(
                'saveandcontinue',
                [
                    'label' => __('Save and Continue Edit'),
                    'class' => 'save',
                    'data_attribute' => [
                        'mage-init' => [
                            'button' => [
                                'event' => 'saveAndContinueEdit',
                                'target' => '#edit_form',
                                '_current' => true,
                                'active_tab' => ''
                            ],
                        ],
                    ]
                ],
                -100
            );
        } else {
            $this->buttonList->remove('save');
        }
        $this->buttonList->remove('reset');
    }

    /**
     * Retrieve text for header element depending on loaded Group
     *
     * @return \Magento\Framework\Phrase
     */
    public function getHeaderText()
    {
        if ($this->_coreRegistry->registry('woocommerceaccount_info')->getId()) {
            return __("Edit Woo Commerce Account ");
        } else {
            return __('New Woo Commerce Account');
        }
    }

    /**
     * Check permission for passed action
     *
     * @param string $resourceId
     *
     * @return bool
     */
    protected function _isAllowedAction($resourceId)
    {
        return $this->_authorization->isAllowed($resourceId);
    }
}
