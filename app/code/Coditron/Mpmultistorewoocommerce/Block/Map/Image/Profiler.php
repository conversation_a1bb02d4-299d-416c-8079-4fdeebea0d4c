<?php
namespace Co<PERSON>ron\Mpmultistorewoocommerce\Block\Map\Image;

class Profiler extends \Magento\Framework\View\Element\Template
{
    public $helperData;
    public $importedImages;
    public $woocommerceHelper;

    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $helperData,
        \Coditron\Mpmultistorewoocommerce\Helper\FData $woocommerceHelper,
        \Coditron\Mpmultistorewoocommerce\Model\ImportedImagesFactory $importedImages,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->helperData = $helperData;
        $this->importedImages = $importedImages;
        $this->$woocommerceHelper = $woocommerceHelper;
    }

    /**
     * For get total imported product image count.
     *
     * @param int $ruleId
     * @return int
     */
    public function getImportedProductImages($ruleId)
    {
        return $this->importedImages->create()->getCollection()->addFieldToFilter('rule_id', $ruleId)->getSize();
    }

    /**
     * For get total imported product image count.
     *
     * @return int
     */
    public function getImageImportScriptData()
    {
        $ruleId = $this->woocommerceHelper->getAccountDetails()["entity_id"];
        $productImageCount = $this->getImportedProductImages($ruleId);
        $releventData = [
            'imageImportUrl' => $this->getUrl('*/products/addimage'),
            'imageCount' => $productImageCount,
            'ruleId' => $ruleId
        ];
        return [
            'totalCount' => $productImageCount,
            'script_json_data' => $this->helperData->jsonHelper->jsonEncode($releventData)
        ];
    }
}
