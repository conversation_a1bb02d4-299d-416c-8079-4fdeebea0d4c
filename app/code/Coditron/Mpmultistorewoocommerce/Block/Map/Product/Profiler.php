<?php

namespace Coditron\Mpmultistorewoocommerce\Block\Map\Product;

class Profiler extends \Magento\Framework\View\Element\Template
{

    public $helperData;
    public $FhelperData;
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $helperData,
        \Coditron\Mpmultistorewoocommerce\Helper\FData $FhelperData,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->helperData = $helperData;
        $this->FhelperData = $FhelperData;
    }

    public function getImportedProduct()
    {
        $ruleId = $this->FhelperData->getAccountDetails()["entity_id"];
        return $this->helperData->getTotalImportedCount('product', $ruleId);
    }

    public function getMappedCategories()
    {
        $cates = [];
        $ruleId = $this->FhelperData->getAccountDetails()["entity_id"];
        $mappedCatesArray =  $this->helperData->getMappedCategoryData($ruleId);
        if (isset($mappedCatesArray['items'])) {
            foreach ($mappedCatesArray['items'] as $record) {
                $cates[] = $record['woocommerce_cat_id'];
            }
        }
        $configs = $this->helperData->getWoocommerceConfiguration($ruleId);
        return [
            'isAllProImport' => $configs,
            'cates'          => implode(',', $cates)
        ];
    }

    /**
     * For get total imported order count.
     *
     * @param int $ruleId
     * @return int
     */
    public function getImportedOrder($ruleId)
    {
        return $this->FhelperData->getTotalImportedCount('order', $ruleId);
    }
}
