<?php
namespace Coditron\Mpmultistorewoocommerce\Block\Account;

use Exception;
use Magento\Eav\Api\Data\AttributeSetInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Catalog\Api\AttributeSetRepositoryInterface;

class Connect extends \Magento\Framework\View\Element\Template
{
    /**
     * @var object AttributeSetRepositoryInterface
     */
    private $attributeSetRepository;

    /**
     * @var object SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;
    
    /**
     * @var \Magento\Catalog\Model\CategoryFactory
     */
    private $_categoryFactory;
    
    /**
     * @var \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory
     */
    private $_categoryCollectionFactory;
    
    /**
     * @var \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory
     */
    private $storeManager;
    
    /**
     * @var \Magento\Sales\Model\ResourceModel\Order\Status\CollectionFactory
     */
    private $statusCollectionFactory;
    
    /**
     * @var \Webkul\Marketplace\Helper\Data
     */
    public $helper;
    
    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Model\ListingTemplateFactory
     */
    private $listingTemplate;
    
    /**
     * @var \Webkul\Marketplace\Helper\Data
     */
    private $mphelper;

    /**
     * Construct function
     *
     * @param \Magento\Backend\Block\Template\Context $context
     * @param AttributeSetRepositoryInterface $attributeSetRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param \Magento\Catalog\Model\CategoryFactory $categoryFactory
     * @param \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $categoryCollectionFactory
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Sales\Model\ResourceModel\Order\Status\CollectionFactory $statusCollectionFactory
     * @param \Webkul\MpMultiShopifyStoreMageConnect\Helper\Data $helper
     * @param \Webkul\MpMultiShopifyStoreMageConnect\Model\ListingTemplateFactory $listingTemplate
     * @param \Webkul\Marketplace\Helper\Data $mphelper
     * @param array $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        AttributeSetRepositoryInterface $attributeSetRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        \Magento\Catalog\Model\CategoryFactory $categoryFactory,
        \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $categoryCollectionFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Sales\Model\ResourceModel\Order\Status\CollectionFactory $statusCollectionFactory,
        \Coditron\Mpmultistorewoocommerce\Helper\FData $helper,
        \Webkul\MpMultiShopifyStoreMageConnect\Model\ListingTemplateFactory $listingTemplate,
        \Webkul\Marketplace\Helper\Data $mphelper,
        array $data = []
    ) {
        $this->attributeSetRepository = $attributeSetRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->_categoryFactory = $categoryFactory;
        $this->_categoryCollectionFactory = $categoryCollectionFactory;
        $this->storeManager = $storeManager;
        $this->statusCollectionFactory = $statusCollectionFactory;
        $this->helper = $helper;
        $this->listingTemplate = $listingTemplate;
        $this->mphelper = $mphelper;
        parent::__construct($context, $data);
    }

    /**
     * List attribute set
     *
     * @return AttributeSetInterface|null
     */
    public function listAttributeSet()
    {
        $attributeSetList = null;
        try {
            $searchCriteria = $this->searchCriteriaBuilder->create();
            $attributeSet = $this->attributeSetRepository->getList($searchCriteria);
        } catch (\Exception $exception) {
            $exception->getMessage();
        }

        if ($attributeSet->getTotalCount()) {
            $attributeSetList = $attributeSet;
        }

        return $attributeSetList;
    }

    /**
     * @inheritdoc
     */
    public function getCategoryCollection($isActive = true, $level = false, $sortBy = false, $pageSize = false)
    {
        $collection = $this->_categoryCollectionFactory->create();
        $collection->addAttributeToSelect('*');

        // select only active categories
        if ($isActive) {
            $collection->addIsActiveFilter();
        }

        // select categories of certain level
        if ($level) {
            $collection->addLevelFilter($level);
        }

        // sort categories by some value
        if ($sortBy) {
            $collection->addOrderField($sortBy);
        }

        // select certain number of categories
        if ($pageSize) {
            $collection->setPageSize($pageSize);
        }

        return $collection;
    }

    /**
     * @inheritdoc
     */
    public function listCategories()
    {
        $arr = $this->_toArray();
        $ret = [];

        foreach ($arr as $key => $value) {
            $ret[] = [
                'value' => $key,
                'label' => $value
            ];
        }
        
        return $ret;
    }

    /**
     * @inheritdoc
     */
    private function _toArray()
    {
        $categories = $this->getCategoryCollection(true, false, false, false);

        $catagoryList = [];
        foreach ($categories as $category) {
            $catagoryList[$category->getEntityId()] = $this
            ->_getParentName($category->getPath()) . $category->getName();
        }

        return $catagoryList;
    }

    /**
     * @inheritdoc
     */
    private function _getParentName($path = '')
    {
        $parentName = '';
        $rootCats = [1,2];

        $catTree = explode("/", $path);
        // Deleting category itself
        array_pop($catTree);

        if ($catTree && (count($catTree) > count($rootCats))) {
            foreach ($catTree as $catId) {
                if (!in_array($catId, $rootCats)) {
                    $category = $this->_categoryFactory->create()->load($catId);
                    $categoryName = $category->getName();
                    $parentName .= $categoryName . ' -> ';
                }
            }
        }

        return $parentName;
    }

    /**
     * @inheritdoc
     */
    public function getStores()
    {
        return $this->storeManager->getStores(true, false);
    }

    /**
     * @inheritdoc
     */
    public function optionTypes($store = null)
    {

            $importType =[
                 [
                    'value' => 1,
                    'label' => __('All products')
                 ],
                 [
                    'value' => 0,
                    'label' => __('Only mapped categories\'s product')
                 ]
            ];
            return $importType;
    }

    /**
     * @inheritdoc
     */
    public function priceRuleOptions($store = null)
    {
        $importProductType = [
            ['value' => 'none','label' => __('None')],
            ['value' => 'import','label' => __('Import Product')],
            ['value' =>'export','label' => __('Export Product')]
        ];
        return $importProductType;
    }

    /**
     * @inheritdoc
     */
    public function orderStatus()
    {
        return $this->statusCollectionFactory->create()->toOptionArray();
    }

    /**
     * @inheritdoc
     */
    public function baseCurrCode()
    {
        return $this->helper->getBaseCurrencyCode();
    }

    /**
     * @inheritdoc
     */
    public function getProductOptions()
    {
        return [
            ['value' => 'simple', 'label' => __('Simple')],
            ['value' => 'configurable', 'label' => __('Configurable')]
        ];
    }

    public function getTemplates()
    {
        $listingTemplates = [];
        $sellrId = $this->mphelper->getSellerData()->getFirstItem()->getSellerId();
        $accDetails = $this->helper->getAccountDetails();
            $templates = $this->listingTemplate
            ->create()
            ->getCollection()
            ->addFieldToFilter("seller_id", ["eq"=>$sellrId]);
        foreach ($templates as $temp) {
            $eachData['value'] = $temp->getEntityId();
            $eachData['label'] = $temp->getTemplateTitle();
            $listingTemplates[] = $eachData;
        }
        return $listingTemplates;
    }

        /**
     * @inheritdoc
     */
    public function getImageImportOptions() {
        $imageImportOptions = [
            ['value' => $this->helper::IMG_STORE_DEFAULT, 'label' => __('With Products')],
            ['value' => $this->helper::IMG_STORE_PROFILER, 'label' => __('Profiler')],
            ['value' => $this->helper::IMG_STORE_MQ, 'label' => __('Message Queue')]
        ];
        return $imageImportOptions;
    }
}
