<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Api;

/**
 * @api
 */
interface WoocommercecategorymapRepositoryInterface
{

    /**
     * Get shopify mapped category collection by rule id
     *
     * @param int $ruleId
     * @return object
     */
    public function getCollectionByRuleId($ruleId);

    /**
     * Get collection by mage category id
     *
     * @param  int $mageCateId
     * @param  int $ruleId
     * @return object
     */
    public function getCollectionByMageCateIdnRuleId($mageCateId, $ruleId);

    /**
     * Get collection by entity ids
     *
     * @param  array $entityIds
     * @return object
     */
    public function getCollectionByIds(array $entityIds);

    /**
     * Get record by magento category id
     *
     * @param  array $mageCateIds
     * @param  int $ruleId
     * @return object
     */
    public function getCollectionByMageCateIdsnRuleId($mageCateIds, $ruleId);

    /**
     * Get collection by rule id and shopify cate id
     *
     * @param  int $wcCateId
     * @param  int $ruleId
     * @return object
     */
    public function getCollectionByWoocommerceCateIdnRuleId($wcCateId, $ruleId);
}
