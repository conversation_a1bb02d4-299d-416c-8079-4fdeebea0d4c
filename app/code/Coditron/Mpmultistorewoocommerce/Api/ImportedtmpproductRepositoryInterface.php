<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Api;

/**
 * @api
 */
interface ImportedtmpproductRepositoryInterface
{

    /**
     * Get a record by item id and product type
     *
     * @param  string $productType
     * @param  int $itemId
     * @return object
     */
    public function getRecordByItemIdnProductType($productType, $itemId);

    /**
     * Get tempdate collection by rule id and product type
     *
     * @param  string $productType
     * @param  int $ruleId
     * @return object
     */
    public function getCollectionByProductTypeAndRuleId($productType, $ruleId);
}
