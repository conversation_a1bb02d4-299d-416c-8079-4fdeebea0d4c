<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Api;

/**
 * @api
 */
interface CategoriesspecificationRepositoryInterface
{
    /**
     * Get collection by wwoocommerce category id
     *
     * @param  int $woocommerceCateId
     * @return object
     */
    public function getCollectionByWoocommerceCatId($woocommerceCateId);
}
