<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Api;

/**
 * @api
 */
interface WoocommercecategoryRepositoryInterface
{

    /**
     * Get Woo commerce category collectionby woo commerce category id
     *
     * @param  int $woocommerceCateId
     * @return object
     */
    public function getCollectionByWoocommerceCateId($woocommerceCateId);

    /**
     * Get Collection by Woocommerce parent id
     *
     * @param  int $woocommerceParentId
     * @return object
     */
    public function getCollectionByWoocommerceCateParentId($woocommerceParentId);
}
