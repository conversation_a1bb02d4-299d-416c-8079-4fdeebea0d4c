<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Api;

/**
 * @api
 */
interface ProductmapRepositoryInterface
{

    /**
     * Get shopify sync  collection of product by shopify rule id
     *
     * @param int $ruleId
     * @return object
     */
    public function getCollectionByRuleId($ruleId);

    /**
     * Get collection by woo commerce product id
     *
     * @param  int $woocommerceProductId
     * @return object
     */
    public function getRecordByWoocommerceProductId($woocommerceProductId);

    /**
     * Get record by magento product id
     *
     * @param  int $mageProductId
     * @return object
     */
    public function getRecordByMageProductId($mageProductId);

    /**
     * Get record by magento product id and account id
     *
     * @param  int $mageProductId
     * @param  int $ruleId
     * @return object
     */
    public function getRecordByMageProductIdAndAccountID($mageProductId, $ruleId);

    /**
     * Get collection by entity ids
     *
     * @param  array $entityIds
     * @return object
     */
    public function getCollectionByIds(array $entityIds);
}
