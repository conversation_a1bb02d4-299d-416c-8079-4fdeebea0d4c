<?php
/**
 * @category  Coditron
 * @package   Coditron_
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license    https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Api\Data;

interface WoocommerceaccountsInterface
{
    /**
     * @inheritdoc
     */
    public const ID = 'entity_id';
    
    /**
     * @inheritdoc
     */
    public const ATTRIBUTE_SET_ID = 'attribute_set_id';

    /**
     * @inheritdoc
     */
    public const WOOCOMMERCE_USER_ID = 'woocommerce_user_id';

    /**
     * @inheritdoc
     */
    public const WOOCOMMERCE_URL = 'woocommerce_url';

    /**
     * @inheritdoc
     */
    public const STORE_NAME = 'store_name';

    /**
     * @inheritdoc
     */
    public const WOOCOMMERCE_CONSUMER_KEY = 'woocommerce_consumer_key';

    /**
     * @inheritdoc
     */
    public const WOOCOMMERCE_CONSUMER_SECRET_KEY = 'woocommerce_consumer_secret_key';

    /**
     * Get ID.
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set ID.
     *
     * @param int $id
     * @return $this
     */
    public function setId($id);

    /**
     * Get attribute set id.
     *
     * @return int
     */
    public function getAttributeSetId();

    /**
     * Set attribute set id.
     *
     * @param int $attributeSetId
     * @return $this
     */
    public function setAttributeSetId($attributeSetId);

    /**
     * Get Woocommerce user id.
     *
     * @return string
     */
    public function getWoocommerceUserId();

    /**
     * Set Woocommerce user id.
     *
     * @param string $woocommerceUserId
     * @return $this
     */
    public function setWoocommerceUserId($woocommerceUserId);

    /**
     * Get Woocommerce URL.
     *
     * @return string
     */
    public function getWoocommerceUrl();

    /**
     * Set Woocommerce URL.
     *
     * @param string $woocommerceUrl
     * @return $this
     */
    public function setWoocommerceUrl($woocommerceUrl);

    /**
     * Get store name.
     *
     * @return string
     */
    public function getStoreName();

    /**
     * Set store name.
     *
     * @param string $storeName
     * @return $this
     */
    public function setStoreName($storeName);

    /**
     * Get Woocommerce consumer key.
     *
     * @return string
     */
    public function getWoocommerceConsumerKey();

    /**
     * Set Woocommerce consumer key.
     *
     * @param string $woocommerceConsumerKey
     * @return $this
     */
    public function setWoocommerceConsumerKey($woocommerceConsumerKey);

    /**
     * Get Woocommerce consumer secret key.
     *
     * @return string
     */
    public function getWoocommerceConsumerSecretKey();

    /**
     * Set Woocommerce consumer secret key.
     *
     * @param string $woocommerceConsumerSecretKey
     * @return $this
     */
    public function setWoocommerceConsumerSecretKey($woocommerceConsumerSecretKey);
}
