<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Api\Data;

interface ProductmapInterface
{
    /**
     * @inheritdoc
     */
    public const ID = 'entity_id';
    
    /**
     * @inheritdoc
     */
    public const WOOCOMMERCE_PRO_ID = 'woocommerce_pro_id';
    
    /**
     * @inheritdoc
     */
    public const NAME = 'name';
    
    /**
     * @inheritdoc
     */
    public const PRODUCT_TYPE = 'product_type';
    
    /**
     * @inheritdoc
     */
    public const MAGENTO_PRO_ID = 'magento_pro_id';
    
    /**
     * @inheritdoc
     */
    public const MAGE_CAT_ID = 'mage_cat_id';
    
    /**
     * @inheritdoc
     */
    public const CHANGE_STATUS = 'change_status';
    
    /**
     * @inheritdoc
     */
    public const CREATED_AT = 'created';
    
    /**
     * @inheritdoc
     */
    public const RULE_ID = "rule_id";

    /**
     * Get ID.
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set ID.
     *
     * @param int $id
     * @return object $this
     */
    public function setId($id);

    /**
     * Get woocommerceProId.
     *
     * @return string
     */
    public function getWoocommerceProId();

    /**
     * Set WoocommerceProId.
     *
     * @param int $shopifyProId
     * @return $this
     */
    public function setWoocommerceProId($woocommerceProId);

    /**
     * Get Name.
     *
     * @return string
     */
    public function getName();

    /**
     * Set Name.
     *
     * @param string $name
     * @return $this
     */
    public function setName($name);

    /**
     * Get ProductType.
     *
     * @return string
     */
    public function getProductType();

    /**
     * Set ProductType.
     *
     * @param int $productType
     * @return $this
     */
    public function setProductType($productType);

    /**
     * Get MagentoProId.
     *
     * @return string
     */
    public function getMagentoProId();

    /**
     * Set MagentoProId.
     *
     * @param int $magentoProId
     * @return $this
     */
    public function setMagentoProId($magentoProId);

    /**
     * Get MageCatId.
     *
     * @return string
     */
    public function getMageCatId();

    /**
     * Set MageCatId.
     *
     * @param int $mageCatId
     * @return $this
     */
    public function setMageCatId($mageCatId);

    /**
     * Get ChangeStatus.
     *
     * @return string
     */
    public function getChangeStatus();

    /**
     * Set ChangeStatus.
     *
     * @param string $changeStatus
     * @return $this
     */
    public function setChangeStatus($changeStatus);

    /**
     * Get CreatedAt.
     *
     * @return string
     */
    public function getCreatedAt();

    /**
     * Set CreatedAt.
     *
     * @param int $created
     * @return $this
     */
    public function setCreatedAt($created);

    /**
     * Get rule id.
     *
     * @return int
     */
    public function getRuleId();

    /**
     * Set rule id.
     *
     * @param int $ruleId
     */
    public function setRuleId($ruleId);
}
