<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Api\Data;

interface ImportedtmpproductInterface
{
   
    /**
     * @inheritdoc
     */
    public const ID = 'entity_id';

    /**
     * @inheritdoc
     */
    public const ITEM_ID = 'item_id';
    
    /**
     * @inheritdoc
     */
    public const PRODUCT_DATA = 'product_data';
    
    /**
     * @inheritdoc
     */
    public const CREATED_AT = 'created_at';

    /**
     * Get ID.
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set ID.
     *
     * @param int $id
     * @return $this
     */
    public function setId($id);

   /**
    * Get ItemId.

    * @return string
    */
    public function getItemId();

   /**
    * Set ItemId.
    *
    * @param int $itemId
    * @return $this
    */
    public function setItemId($itemId);

   /**
    * Get ProductData.

    * @return string
    */
    public function getProductData();

   /**
    * Set ProductData.
    *
    * @param int $productData
    * @return $this
    */
    public function setProductData($productData);

    /**
     * Get CreatedAt.
     *
     * @return string
     */
    public function getCreatedAt();

   /**
    * Set CreatedAt.
    *
    * @param int $createdAt
    * @return $this
    */
    public function setCreatedAt($createdAt);
}
