<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Api\Data;

interface CategoriesspecificationInterface
{
    /**
     * @inheritdoc
     */
    public const ID = 'entity_id';

    /**
     * @inheritdoc
     */
    public const WOOCOMMERCE_CATEGORY_ID = 'woocommerce_category_id';

    /**
     * @inheritdoc
     */
    public const WOOCOMMERCE_SPECIFICATION_NAME = 'woocommerce_specification_name';

    /**
     * @inheritdoc
     */
    public const MAGE_PRODUCT_ATTRIBUTE_CODE = 'mage_product_attribute_code';

    /**
     * @inheritdoc
     */
    public const CREATED_AT = 'created';

    /**
     * Get ID.
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set ID.
     *
     * @param int $entityId
     * @return $this
     */
    public function setId($entityId);

   /**
    * Get WoocommerceCategoryId.
    *
    * @return string
    */
    public function getWoocommerceCategoryId();

   /**
    * Set WoocommerceCategoryId.
    *
    * @param int $shopifyCategoryId
    * @return $this
    */
    public function setWoocommerceCategoryId($woocommerceCategoryId);

   /**
    * Get WoocommerceSpecificationName.
    *
    * @return string
    */
    public function getWoocommerceSpecificationName();

   /**
    * Set WoocommerceSpecificationName.
    *
    * @param string $shopifySpecificationName
    * @return object $this
    */
    public function setWoocommerceSpecificationName($woocommerceSpecificationName);

   /**
    * Get MageProductAttributeCode.
    *
    * @return string
    */
    public function getMageProductAttributeCode();

   /**
    * Set MageProductAttributeCode.
    *
    * @param string $mageProductAttributeCode
    * @return object $this
    */
    public function setMageProductAttributeCode($mageProductAttributeCode);

   /**
    * Get CreatedAt.
    *
    * @return string
    */
    public function getCreatedAt();

   /**
    * Set CreatedAt.
    *
    * @param int $createdAt
    * @return object $this
    */
    public function setCreatedAt($createdAt);
}
