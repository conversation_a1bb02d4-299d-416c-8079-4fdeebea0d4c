<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Api\Data;

interface WoocommercecategoryInterface
{
    /**
     * @inheritdoc
     */
    public const ID = 'id';
    
    /**
     * @inheritdoc
     */
    public const WOOCOMMERCE_CAT_ID = 'woocommerce_cat_id';
    
    /**
     * @inheritdoc
     */
    public const WOOCOMMERCE_CAT_PARENTID = 'woocommerce_cat_parentid';
    
    /**
     * @inheritdoc
     */
    public const WOOCOMMERCE_CAT_NAME = 'woocommerce_cat_name';
    
    /**
     * @inheritdoc
     */
    public const CREATED_AT = 'created';

    /**
     * Get ID.
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set ID.
     *
     * @param int $id
     * @return $this
     */
    public function setId($id);

   /**
    * Get WoocommerceCatId.
    *
    * @return string
    */
    public function getWoocommerceCatId();

   /**
    * Set WoocommerceCatId.
    *
    * @param int $woocommerceCatId
    * @return $this
    */
    public function setWoocommerceCatId($woocommerceCatId);

   /**
    * Get WoocommerceCatParentid.
    *
    * @return string
    */
    public function getWoocommerceCatParentid();

   /**
    * Set WoocommerceCatParentid.
    *
    * @param int $woocommerceCatParentid
    * @return $this
    */
    public function setWoocommerceCatParentid($woocommerceCatParentid);

   /**
    * Get WoocommerceCatName.
    *
    * @return string
    */
    public function getWoocommerceCatName();

   /**
    * Set woocommerceCatName.
    *
    * @param string $woocommerceCatName
    * @return $this
    */
    public function setWoocommerceCatName($woocommerceCatName);

   /**
    * Get CreatedAt.
    *
    * @return string
    */
    public function getCreatedAt();

    /**
     * Set CreatedAt.
     *
     * @param int $createdAt
     * @return $this
     */
    public function setCreatedAt($createdAt);
}
