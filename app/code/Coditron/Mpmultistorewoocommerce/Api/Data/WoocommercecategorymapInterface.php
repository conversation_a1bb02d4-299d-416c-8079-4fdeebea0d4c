<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Api\Data;

interface WoocommercecategorymapInterface
{
    /**
     * @inheritdoc
     */
    public const ID = 'id';
    
    /**
     * @inheritdoc
     */
    public const WOOCOMMERCE_CAT_ID = 'woocommerce_cat_id';
    
    /**
     * @inheritdoc
     */
    public const MAGE_CAT_ID = 'mage_cat_id';
    
    /**
     * @inheritdoc
     */
    public const PRO_CONDITION_ATTR = 'pro_condition_attr';
    
    /**
     * @inheritdoc
     */
    public const VARIATIONS_ENABLED = 'variations_enabled';
    
    /**
     * @inheritdoc
     */
    public const CREATED_AT = 'created';

    /**
     * Get ID.
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set ID.
     *
     * @param int|null $id
     * @return $this
     */
    public function setId($id);

   /**
    * Get woocommerceCatId.
    *
    * @return string
    */
    public function getWoocommerceCatId();

   /**
    * Set WoocommerceCatId.
    *
    * @param int $woocommerceCatId
    * @return $this
    */
    public function setWoocommerceCatId($woocommerceCatId);

   /**
    * Get MageCatId.
    *
    * @return string
    */
    public function getMageCatId();

   /**
    * Set MageCatId.
    *
    * @param int $mageCatId
    * @return $this
    */
    public function setMageCatId($mageCatId);

   /**
    * Get ProConditionAttr.
    *
    * @return string
    */
    public function getProConditionAttr();

   /**
    * Set ProConditionAttr.
    *
    * @param string $proConditionAttr
    * @return $this
    */
    public function setProConditionAttr($proConditionAttr);

   /**
    * Get VariationsEnabled.
    *
    * @return string
    */
    public function getVariationsEnabled();

   /**
    * Set VariationsEnabled.
    *
    * @param int $variationsEnabled
    * @return $this
    */
    public function setVariationsEnabled($variationsEnabled);

   /**
    * Get CreatedAt.
    *
    * @return string
    */
    public function getCreatedAt();

   /**
    * Set CreatedAt.
    *
    * @param string $createdAt
    * @return $this
    */
    public function setCreatedAt($createdAt);
}
