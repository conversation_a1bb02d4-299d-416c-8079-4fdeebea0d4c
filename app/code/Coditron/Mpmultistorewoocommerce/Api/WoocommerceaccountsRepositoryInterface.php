<?php
namespace Coditron\Mpmultistorewoocommerce\Api;

/**
 * @api
 */
interface WoocommerceaccountsRepositoryInterface
{
    /**
     * Get ConfigurationById
     *
     * @param  int $id
     * @return object
     */
    public function getConfigurationById($id);

    /**
     * Get by woo commerce  user id
     *
     * @param string $woocommerceUserId
     * @return object
     */
    public function getByUserId($woocommerceUserId);
}
