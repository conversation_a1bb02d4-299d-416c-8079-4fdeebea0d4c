<?php

namespace Co<PERSON>ron\Mpmultistorewoocommerce\Controller\Account;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\ResultFactory;
use Coditron\Mpmultistorewoocommerce\Model\WoocommerceaccountsFactory;
use Magento\Framework\Encryption\EncryptorInterface;
use Coditron\Mpmultistorewoocommerce\Helper\Data as DataHelper;
use Webkul\Marketplace\Helper\Data as MpHelper;
use Magento\Framework\Controller\Result\Redirect;

class Save extends Action
{
    private $resultPageFactory;
    private $woocommerceAccountsFactory;
    private $dataHelper;
    private $encryptor;
    private $mphelper;

    public function __construct(
        Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        WoocommerceaccountsFactory $woocommerceAccountsFactory,
        EncryptorInterface $encryptor,
        DataHelper $dataHelper,
        MpHelper $mphelper
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->woocommerceAccountsFactory = $woocommerceAccountsFactory;
        $this->encryptor = $encryptor;
        $this->dataHelper = $dataHelper;
        $this->mphelper = $mphelper;
    }

    public function execute()
    {
        $logger = $this->createLogger();

        $flag = false;
        $reserveId = 0;
        $resultRedirect = $this->resultRedirectFactory->create();
        $temp = $parameters = $this->getRequest()->getParams();

        $parameters['woocommerce_consumer_key'] = $this->beforeSave($parameters['woocommerce_consumer_key'])['value'];
        $parameters['woocommerce_consumer_secret_key'] = $this->beforeSave($parameters['woocommerce_consumer_secret_key'])['value'];
        
        $status = $this->checkForEmptyValue($parameters);
        $emptyValueCheck = ['status'=>'', 'msg'=> ''];

        if ($status['status']) {
            $data = $this->dataHelper->authorizeWooCommerceShop($temp);
            $logger->info("link authorization data link: " . print_r($data, true));
            
            if (isset($data['error_msg'])){
                $this->messageManager
                ->addError(
                     __($data['error_msg'])
                 );
                 return $this->resultRedirectFactory->create()->setPath('*/*/connect');
            } 
            $error = __('ShopiWoo commerce user didn\'t authorize successfully, Please try again.');
            if ($data != null && isset($data['api_request_http_code']) && $data['api_request_http_code'] == 200) {
                $parameters['woocommerce_base_currency'] = $data['woocommerce_base_currency'];
                $parameters['magento_base_currency'] = $this->dataHelper->getBaseCurrencyCode();
                $logger->info("magento_base_currency: " . print_r($parameters, true));
                $model = $woocommerceAccountsCollection = $this->woocommerceAccountsFactory->create();
                $model->addData($parameters)->save();
                $this->messageManager
                    ->addSuccess(
                         __('Woo Commerce details saved successfully, Now edit the record for syncronization process')
                     );
                if (isset($temp['id']) && isset($temp['back'])) {
                    return $this->resultRedirectFactory->create()->setPath('*/*/edit', ['id'=>$temp['id']]);
                } else {
                    return $this->resultRedirectFactory->create()->setPath('*/*/connect');
                 }
            } else {
                 $this->messageManager->addError($data['error_msg']);
                 return $this->resultRedirectFactory->create()->setPath('*/*/connect');
             }
         } else {
             $this->messageManager->addError(__('Something went wrong'));
             return $this->resultRedirectFactory->create()->setPath('*/*/connect');
         }
    }

    /**
     * Creates a logger instance.
     *
     * @return \Zend_Log
     */
    private function createLogger(): \Zend_Log
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/woocommerceconnectcontroller.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        return $logger;
    }

    /**
     * BeforeSave function check that value is obscure or not
     *
     * @param string $value
     * @return array
     */
    private function beforeSave($value)
    {
        if (!preg_match('/^\*+$/', $value) && !empty($value)) {
            $encrypted = $this->encryptor->encrypt($value);
            return ['value' => $encrypted, 'status' => true];
        } elseif (empty($value)) {
            return ['value' => $value, 'status' => false];
        }
        return ['value' => $value, 'status' => false];
    }

    /**
     * getTheConsumerKeyFromDbToVerify function get the api key
     *
     * @param int $id
     * @return string
     */
    private function getTheConsumerKeyFromDbToVerify($id = '')
    {
        $modal = $this->woocommerceAccountsFactory->create()->load($id);
        return $modal->getWoocommerceConsumerKey();
    }

     /**
     * getTheConsumerssecretKeyFromDbToVerify function get the api password
     *
     * @param int $id
     * @return string
     */
    private function getTheConsumerssecretKeyFromDbToVerify($id = '')
    {
        $modal = $this->woocommerceAccountsFactory->create()->load($id);
        return $modal->getWoocommerceConsumerSecretKey();
    }

         /**
      * CheckForEmptyValue function check for empty value
      *
      * @param array $parameters
      * @return array
      */
      private function checkForEmptyValue($parameters)
      {
          if ($parameters['woocommerce_consumer_key'] == '' ||
              $parameters['woocommerce_consumer_secret_key'] == '' ||
              $parameters['woocommerce_url'] == ''
          ) {
              return ['status' => 0, 'msg'=>'requird fields are empty'];
          }
          return ['status'=> 1, 'msg'=>''];
      }
}
