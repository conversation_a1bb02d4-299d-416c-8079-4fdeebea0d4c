<?php

namespace Coditron\Mpmultistorewoocommerce\Controller\Products;

use Magento\Framework\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Coditron\Mpmultistorewoocommerce\Api\ImportedtmpproductRepositoryInterface;

class AddImage extends \Magento\Customer\Controller\AbstractAccount
{
    /**
     * @var ImportedtmpproductRepositoryInterface
     */
    private $importedTmpProductRepository;

    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Model\Productmap
     */
    private $productMapRecord;

    /**
     * @var \Magento\Framework\Json\Helper\Data
     */
    private $jsonHelper;

    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Helper\Data
     */
    private $helper;
    
    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Model\ImportedImagesFactory
     */
    private $importedImages;
    
    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Logger\Logger
     */
    private $logger;

    /**
     * @param Context $context
     * @param \Magento\Framework\Json\Helper\Data $jsonHelper
     * @param \Webkul\MpMultiShopifyStoreMageConnect\Model\ImportedImagesFactory $importedImages
     * @param \Webkul\MpMultiShopifyStoreMageConnect\Helper\Data $helper
     */
    public function __construct(
        Context $context,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Coditron\Mpmultistorewoocommerce\Model\ImportedImagesFactory $importedImages,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $helper
    ) {
        parent::__construct($context);
        $this->jsonHelper = $jsonHelper;
        $this->importedImages = $importedImages;
        $this->helper = $helper;
        $this->logger = $helper->createlogger();
    }

    /**
     * Execute Function
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
        try {
            $ruleId = $this->getRequest()->getParam('ruleId');
            $tempData = $this->importedImages->create()->getCollection()->addFieldToFilter('rule_id', ['eq' => $ruleId])
                                ->setPageSize(1)->setCurPage(1)->getFirstItem();
            if ($tempData->getEntityId()) {
                $tempProData = $this->helper->addImages(
                    $tempData->getMagentoProId(),
                    $tempData->getImageUrl(),
                    $tempData->getIsDefault()
                );
                if ($tempProData) {
                    $tempData->delete();
                }
                $result = ['error' => 0, 'msg' => ''];
            } else {
                $data = $this->getRequest()->getParams();
                $total = (int) $data['count'] - (int) $data['skip'];
                $msg = '<div class="wk-mu-success wk-mu-box">'
                .__('Total %1 Product(s) Images Imported.', $total).'</div>';
                $msg .= '<div class="wk-mu-note wk-mu-box">'.__('Finished Execution.').'</div>';
                $result['msg'] = $msg;
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            $result = [
                'error' => 1,
                'msg' => __('Something went wrong, Please check error log.'),
                'actual_error' => $e->getMessage()
            ];
        }
        $this->getResponse()->representJson($this->jsonHelper->jsonEncode($result));
    }
}
