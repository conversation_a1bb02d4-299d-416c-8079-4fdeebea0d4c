<?php

namespace Coditron\Mpmultistorewoocommerce\Controller\Products;

use Magento\Framework\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\App\Action\Action;

class Import extends Action
{

    public $ruleId;
    private $resultJsonFactory;
    private $manageRawDataHelper;
    private $helper;
    private $date;
    

    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        \Coditron\Mpmultistorewoocommerce\Helper\ManageRawData $manageRawDataHelper,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $helper,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $date
    ) {
        parent::__construct($context);
        $this->resultJsonFactory = $resultJsonFactory;
        $this->manageRawDataHelper = $manageRawDataHelper;
        $this->helper = $helper;
        $this->date =  $date;
    }

    /**
     * Mapped Product List page.
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
        $data = $this->getRequest()->getPostValue();
        if (!$data) {
            return $this->resultRedirectFactory->create()
            ->setPath('mpmultistorewoocommerce/*/');
        }
        try {
            if ($this->getRequest()->isPost()) {
                $helper = $this->helper;
                $helper->ruleId = $this->getRequest()->getParam('id');
                $apiCredentials = $helper->getTheShopApiCredentials($helper->ruleId, true);
                $isValidApiCred = $helper->authorizeWooCommerceShop($apiCredentials);
                if ($isValidApiCred['status']) {
                    $ShopifyConfig = $helper->getWoocommerceConfiguration($helper->ruleId);
                    $dt = $this->date->date();
                    $currentDate = $dt->format('Y-m-d\TH:i:s');
                    $endTime = $dt->format('Y-m-d\TH:i:s');
                    $items = 0;
                    $response = [];
                    $errorMsg = '';
                    $pagenumber = $data['page'];
                    $params = [];
                    if (isset($data['cate_id']) && $data['cate_id']) {
                        $ShopifyConfig['CategoryID'] = $data['cate_id'];
                    }
                    $data = $helper->getWooCommerceProductList($ShopifyConfig);
                    if (!empty($data) && $data['status'] == true) {
                        $items = $this->manageRawDataHelper->manageProductRawData($data, $helper->ruleId, false, false, true);
                        $response = [
                            'data' => $items['item_count'],
                            'error_msg' => false,
                            'notImportedProduct' => $items['notImportedProduct']
                        ];
                    } else {
                        if ($data['status'] == true && empty($data['data'])) {
                            $errorMsg =  __('There are no products in your Shopify account');
                            $response = ['data' => $data['data'], 'error_msg' => $errorMsg];
                        } elseif ($data['status'] == false) {
                            $response = ['data' => '', 'error_msg' => $data['error_msg']];
                        } else {
                            $errorMsg = isset($results->Errors->LongMessage) ? $results->Errors->LongMessage :
                                                        __("Invalid response from Shopify");
                            $response = ['data' => $items, 'error_msg' => $errorMsg];
                        }
                    }
                } else {
                    $response = [
                        'data' => '',
                        'error_msg' => __($isValidApiCred['error_msg'])
                    ];
                }
            } else {
                $response = ['data' => $items,'error_msg' => 'invalid request'];
            }
        } catch (\Exception $e) {
            $this->helper->createlogger()->err($e->getMessage());
            $response = ['data' => '','error_msg' => $e->getMessage()];
        }
        return $this->resultJsonFactory->create()->setData($response);
    }
}
