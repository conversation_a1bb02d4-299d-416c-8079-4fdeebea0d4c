<?php

namespace Coditron\Mpmultistorewoocommerce\Controller\Products;

use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\Action;
use Magento\Framework\View\Result\PageFactory;
use Coditron\Mpmultistorewoocommerce\Api\ImportedtmpproductRepositoryInterface;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Products;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem\Driver\File;
use Magento\ImportExport\Model\Import\Adapter;

class Createproduct extends Action
{
    public const OPERATION_TYPE = 'product';
    public const ENTITY_TYPE = 'catalog_product';
    public const FIELD_IMPORT_IDS = '_import_ids';

    private $isProWithImg = false;
    private $importedTmpProductRepository;
    private $productMapRecord;
    private $jsonHelper;
    private $helper;
    private $file;
    private $directory;
    private $importFactory;
    private $backendSession;

    public function __construct(
        Context $context,
        ImportedtmpproductRepositoryInterface $importedTmpProductRepository,
        \Webkul\MpMultiShopifyStoreMageConnect\Model\Productmap $productMapRecord,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Magento\Framework\Filesystem\Driver\File $file,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Backend\Model\Session $backendSession,
        \Magento\ImportExport\Model\ImportFactory $importFactory,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $helper
    ) {
        parent::__construct($context);
        $this->directory = $filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);
        $this->importedTmpProductRepository = $importedTmpProductRepository;
        $this->productMapRecord = $productMapRecord;
        $this->backendSession = $backendSession;
        $this->importFactory = $importFactory;
        $this->jsonHelper = $jsonHelper;
        $this->helper = $helper;
        $this->file = $file;
    }

    /**
     * Create Product
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
        try {
            $ruleId = $this->getRequest()->getParam('ruleId');
            $formKey = $this->getRequest()->getParam('form_key');
            $configData = $this->helper->getShopifyConfiguration($ruleId);
            $this->isProWithImg = $configData['product_images_import_with'] ?? 0;
            $pageSize = $this->isProWithImg ? 1000 : 20;
            
            $tempData = $this->importedTmpProductRepository->getCollectionByProductTypeAndRuleId('product', $ruleId)
                                // ->addFieldToFilter('error', ['null' => true])
                                ->setPageSize($pageSize)->setCurPage(1);
            if ($tempData->getSize()) {
                $result = $this->prepareCsv($tempData, $ruleId);
                if (!$result['error']) {
                    $result = $this->importProducts($formKey, $ruleId);
                }
            } else {
                $data = $this->getRequest()->getParams();
                $total = (int) $data['count'];
                $msg = '<div class="wk-mu-success wk-mu-box">'.__('Total ').$total.__(' Product(s) Imported.').'</div>';
                $msg .= '<div class="wk-mu-note wk-mu-box">'.__('Finished Execution.').'</div>';
                $result['msg'] = $msg;
                $result['error'] = 0;
            }
        } catch (\Exception $e) {
            $this->logger->info('Controller Products CreateProduct : '.$e->getMessage());
            $result = [
                'error' => 1,
                'msg' => __('Something went wrong, Please check error log.'),
                'actual_error' => $e->getMessage()
            ];
        }
        
        $totalProcessed = count($tempData->getItems());
        if (!$result['error']) {
            $tempData->walk('delete');
            $result['total_processed'] = $totalProcessed;
        }

        $this->getResponse()->representJson($this->jsonHelper->jsonEncode($result));
    }

    /**
     * Prepare CSV
     *
     * @param object $tempData
     * @param int $ruleId
     * @return array
     */
    private function prepareCsv($tempData, $ruleId)
    {
        try {
            $filepath = "export/shopify_catalog_product_import_{$ruleId}.csv";
            $pathToDir = $this->directory->getAbsolutePath();
            if ($this->file->isExists($pathToDir . $filepath)) {
                $this->file->deleteFile($pathToDir . $filepath);
            }
            $this->directory->create('export');
            $stream = $this->directory->openFile($filepath, 'w+');
            $stream->lock();
    
            $header = [
                'sku',
                'store_view_code', 
                'product_online', 
                'status', 
                'attribute_set_code',
                'product_type',
                'category',
                'categories',
                'product_websites',
                'name',
                'description',
                'short_description',
                'weight',
                'price',
                'special_price',
                'url_key',
                'page_layout',
                'tax_class_name',
                'visibility',
                'qty',
                'is_in_stock',
                'rule_id',
                'additional_attributes',
                'configurable_variations',
                'configurable_variation_labels',
                'image_data',
                'shopify_pro_id',
                'shopify_variant_map'
            ];

            $this->processImageHeaders($header);
            
            $stream->writeCsv($header);
    
            foreach ($tempData->getItems() as $item) {
                $itemData = $item->getProductData();
                $dataArr = $this->jsonHelper->jsonDecode($itemData);
                $shopifyVariantId = isset($dataArr['shopify_variant_id']) ? $dataArr['shopify_variant_id'] : '';
                if ($shopifyVariantId) {
                    unset($dataArr['shopify_variant_id']);
                }
                $productSku = $dataArr['sku'];
                $this->processImageData($dataArr);
                $row = [];
                $config = false;
                $associatedData = $item->getAssociateProducts();
                $variants = [];
                foreach ($dataArr as $key => $value) {
                    if (!in_array($key, $header)) {
                        continue;
                    }
                    // if (is_array($value) && $key == 'shopify_variant_map') {
                    //     $value = $this->jsonHelper->jsonEncode($value);
                    // }
                    $row[] = $value;
                    if ($value == 'configurable') {
                        $config = true;
                    }
                }
                if ($config && $associatedData != null) {
                    $associatedData = $this->jsonHelper->jsonDecode($associatedData);
                    foreach ($associatedData as $key => $value) {
                        $prodvariants['shopify_variant_id'] = $value['shopify_variant_id'];
                        $prodvariants['product_sku'] = $value['sku'];
                        $variants[] = $prodvariants;
                    }
                    $this->logger->info('Controller Products set associatedData : '.json_encode($associatedData));
                    $this->assocProdCsv($associatedData, $stream, $header);
                } else {
                    if ($shopifyVariantId) {
                        $variants[] = [
                            'shopify_variant_id' => $shopifyVariantId,
                            'product_sku' => $productSku
                        ];
                    }
                }
                $row[] = $item->getItemId();
                $row[] = is_array($variants) ? json_encode($variants) : '';
                $stream->writeCsv($row);
            }
            $result = ['error' => 0, 'msg' => __('Import CSV created successfully.')];
    
            if (!count($tempData)) {
                $msg = '<div class="wk-mu-note wk-mu-box">'.__('No Products To Create.').'</div>';
                $result['msg'] = $msg;
            }
        } catch (\Exception $e) {
            $this->logger->info('Controller Products CreateProductCsv : '.$e->getMessage());
            return $result = [
                'error' => 1,
                'msg' => __('Something went wrong, Please check error log.'),
                'actual_error' => $e->getMessage()
            ];
        }
        return $result;
    }

    /**
     * Add assoc prod in csv
     *
     * @param array $associatedData
     * @param object $stream
     * @param array $header
     */
    private function assocProdCsv($associatedData, $stream, $header)
    {
        foreach ($associatedData as $items) {
            $this->processImageData($items);
            $assocRow = [];
            foreach ($items as $key => $value) {
                if (!in_array($key, $header)) {
                    continue;
                }
                if ($key == 'image_data') {
                    $value = $this->jsonHelper->jsonEncode($value);
                }
                $assocRow[] = $value;
            }
            $assocRow[] = '';
            $stream->writeCsv($assocRow);
        }
    }

    /**
     * Process image data
     *
     * @param array &$data
     * @return array
     */
    private function processImageData(&$data)
    {
        if (
            $this->isProWithImg == $this->helper::IMG_STORE_DEFAULT &&
            isset($data['image_data'])
        ) {
            $imageData = [
                'small_image'       => $data['image_data']['default'] ?? '',
                'thumbnail'         => $data['image_data']['default'] ?? '',
                'base_image'        => $data['image_data']['default'] ?? '',
                'additional_images' => isset($data['image_data']['images']) ?
                                       implode(',', $data['image_data']['images']) : '',
            ];
    
            // Remove 'image_data' and insert $imageData at the same index
            $index = array_search('image_data', array_keys($data), true);
            if ($index !== false) {
                $data = array_slice($data, 0, $index, true) + $imageData + array_slice($data, $index + 1, null, true);
            }
        } else {
            $data['image_data'] = $this->jsonHelper->jsonEncode($data['image_data']);
        }
    }
    
    /**
     * Process image headers
     *
     * @param array &$header
     * @return array
     */
    private function processImageHeaders(&$header)
    {
        if ($this->isProWithImg == $this->helper::IMG_STORE_DEFAULT) {
            $offset = array_search('image_data', $header);
            $imageHeader = ['small_image', 'thumbnail', 'base_image', 'additional_images'];
            array_splice($header, $offset, 1, $imageHeader);
        }
    }

    /**
     * Import Products from CSV
     *
     * @param string $formKey
     * @param int $ruleId
     * @return array
     */
    private function importProducts($formKey, $ruleId)
    {
        try {
            $exportedfilepath = "export/shopify_catalog_product_import_{$ruleId}.csv";
            $pathToDir = $this->directory->getAbsolutePath();
            $checkFileExist = $this->file->isExists($pathToDir . $exportedfilepath);
            $response = [];
            $paramsData = [
                'form_key' => $formKey,
                'entity' => self::ENTITY_TYPE,
                'behavior' => 'append',
                'validation_strategy' => 'validation-skip-errors',
                'allowed_error_count' => 10,
                '_import_field_separator' => ',',
                '_import_multiple_value_separator' => ',',
                'import_images_file_dir' => '',
                '_import_empty_attribute_value_constant' => '__EMPTY__VALUE__'
            ];
            if ($checkFileExist) {
                try {
                    $importModel = $this->importFactory->create();
                    $importModel->setData($paramsData);
                    $sourceModel = Adapter::findAdapterFor(
                        $exportedfilepath,
                        $this->directory,
                        $importModel->getData('_import_field_separator')
                    );
                    
                    $errorAggregator = $importModel->getErrorAggregator();
    
                    $isValid = $importModel->validateSource($sourceModel);
                    
                    // validation ids
                    $ids = $importModel->getValidatedIds();
                    $ids = !empty($ids) ? implode(',', $ids) : null;
    
                    // collecting all validation errors
                    $errors = $errorAggregator->getAllErrors();
    
                    $invalidRowsCount = $errorAggregator->getInvalidRowsCount();
    
                    /**
                     * clearing errors before import
                     * this is a work around for combining validation
                     * and import a two part process from admin
                     * making it work like single process
                    **/
                    $errorAggregator->clear();
    
                    $importModel->setData(self::FIELD_IMPORT_IDS, $ids);
    
                    $response['skipped_errors'] = [];
                    $response['error'] = 0;
                    
                    foreach ($errors as $e) {
                        if (!str_contains($e->getErrorMessage(), 'correspond to the number')) {
                            $response['skipped_errors'][] = $e->getErrorMessage();
                        }
                        
                    }
                    
                    if ($isValid && $importModel->getProcessedRowsCount() != $invalidRowsCount) {
    
                        if (!$importModel->importSource()) {
                            $response['error'] = 1;
                            $response['msg'] = $importModel->getFormatedLogTrace();
                        }
                    }
                } catch (\Exception $e) {
                    
                    // resource clean-up
                    $this->file->deleteFile($pathToDir . $exportedfilepath);
    
                    $this->helper->logCriticalMessage("Error import :- ". $e->getMessage());
                    
                    $response['error'] = 1;
                    $response['msg'] =  $e->getMessage();
                    return $response;
                }
            } else {
                $response['error'] = 1;
                $response['msg'] =  __('CSV File not found');
            }
        } catch (Exception $e) {
            
            // resource clean-up
            $this->file->deleteFile($pathToDir . $exportedfilepath);
            
            $this->helper->logCriticalMessage("Error import :-", $e->getMessage());
            
            $response = ['error' => 1, 'msg' => __('Product Creation failed.')];
            return $response;
        }
        
        // resource clean-up
        $this->file->deleteFile($pathToDir . $exportedfilepath);
        
        return $response;
    }
}