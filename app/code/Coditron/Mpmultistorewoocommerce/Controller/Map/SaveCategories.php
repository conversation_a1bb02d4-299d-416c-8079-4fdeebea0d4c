<?php

namespace Coditron\Mpmultistorewoocommerce\Controller\Map;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Message\ManagerInterface;

class SaveCategories extends Action
{
    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    public $resultPageFactory;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Helper\Data
     */
    public $dataHelper;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\Woocommercecategorymap
     */
    public $WooCommerceCategoryMap;

    /**
     * @var ManagerInterface
     */
    public $messageManager;

    /**
     * @var RedirectFactory
     */
    public $resultRedirectFactory;

    /**
     * @param Context $context
     * @param \Coditron\Mpmultistorewoocommerce\Helper\Data $dataHelper
     * @param \Coditron\Mpmultistorewoocommerce\Model\Woocommercecategorymap $WooCommerceCategoryMap
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param ManagerInterface $messageManager
     * @param RedirectFactory $resultRedirectFactory
     */
    public function __construct(
        Context $context,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $dataHelper,
        \Coditron\Mpmultistorewoocommerce\Model\Woocommercecategorymap $WooCommerceCategoryMap,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        ManagerInterface $messageManager,
        RedirectFactory $resultRedirectFactory
    ) {
        $this->dataHelper = $dataHelper;
        $this->WooCommerceCategoryMap = $WooCommerceCategoryMap;
        $this->resultPageFactory = $resultPageFactory;
        $this->messageManager = $messageManager;
        $this->resultRedirectFactory = $resultRedirectFactory;
        parent::__construct($context);
    }

    /**
     * Execute method
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        $logger = $this->createlogger();
        $status = true;
        $mappingData = $this->getRequest()->getParams();
        $helper = $this->dataHelper;
        $leafWoocommerceCategory = $mappingData['woocommerceLeafCate'];
        $leafMageCategory = $mappingData['mageLeafCate'];
        $msgResponse = $this->_getResponceMsgAsRequest(
            $leafWoocommerceCategory,
            $leafMageCategory,
            $helper,
            $mappingData['id']
        );

        if ($msgResponse) {
            $status = false;
            $this->messageManager->addErrorMessage($msgResponse);
        } else {
            try {
                $data = $helper->checkTheWoocommerceMappingCategoryExist($mappingData['id'], $leafWoocommerceCategory);
                if ($data['status'] == true) {
                    if ($data['data']['id'] == $leafWoocommerceCategory) {
                        /* save mapped data for Shopify and store category */
                        $mapData = [
                            'mage_cat_id' => $leafMageCategory,
                            'woocommerce_cat_id' => $leafWoocommerceCategory,
                            'woocommerce_cat_name' => $data['data']['name'],
                            'body_html' => $data['data']['description'],
                            'rule_id' => $mappingData['id']
                        ];
                        $mapWooCommerceMageCat = $this->WooCommerceCategoryMap;
                        $mapWooCommerceMageCat->setData($mapData)->save();
                        $this->messageManager->addSuccessMessage(__('Category has been successfully mapped with WooCommerce category.'));
                    } else {
                        $status = false;
                        $this->messageManager->addErrorMessage(__('Please first import category from WooCommerce to your store.'));
                    }
                } else {
                    $status = $data['status'];
                    $this->messageManager->addErrorMessage($data['error_msg']);
                }
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            }
        }

        $resultRedirect = $this->resultRedirectFactory->create();
        return $resultRedirect->setPath('*/*/category'); // Adjust the redirection path as needed
    }

    /**
     * Get ResponseMsgAsRequest
     *
     * @param int $leafWoocommerceCategory
     * @param int $leafMageCategory
     * @param \Coditron\Mpmultistorewoocommerce\Helper\Data $helper
     * @param int $ruleId
     * @return false|string
     */
    private function _getResponceMsgAsRequest(
        $leafWoocommerceCategory,
        $leafMageCategory,
        $helper,
        $ruleId
    ) {
        $msg = false;
        if ($leafWoocommerceCategory == false && $leafMageCategory == false) {
            $msg = __('Please select at least one category.');
        } elseif (!$leafWoocommerceCategory) {
            $msg = __('Please select leaf category.');
        } elseif ($helper->isMageCategoryMapped($leafMageCategory, $ruleId)) {
            $msg = __('This Store category is already mapped.');
        }
        return $msg;
    }

    public function createlogger(){
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/SaveCategories.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        return $logger;
    }
}
