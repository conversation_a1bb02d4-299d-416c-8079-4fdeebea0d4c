<?php

namespace Coditron\Mpmultistorewoocommerce\Controller\Map;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\App\RequestInterface;
use Magento\Ui\Component\MassAction\Filter;
use Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Productmap\CollectionFactory;

class ProMassDelete extends Action
{
    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $_customerSession;

    /**
     * @var PageFactory
     */
    protected $resultPageFactory;

    /**
     * @var \Webkul\Marketplace\Helper\Data
     */
    private $mpHelper;

    /**
     * @var \Magento\Customer\Model\Url
     */
    private $url;
    
    /**
     * @var \Magento\Framework\App\Request\Http
     */
    private $request;
    
    /**
     * @var \Magento\Framework\Data\Form\FormKey
     */
    private $formKey;
    
    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Api\ProductmapRepositoryInterface
     */
    private $coll;

    /**
     * @var Filter
     */
    private $filter;
    
    /**
     * @var collectionFactory
     */
    private $collectionFactory;

    /**
     * Construct function
     *
     * @param Context $context
     * @param Filter $filter
     * @param CollectionFactory $collectionFactory
     * @param PageFactory $resultPageFactory
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Webkul\Marketplace\Helper\Data $mpHelper
     * @param \Magento\Customer\Model\Url $url
     * @param \Magento\Framework\Data\Form\FormKey $formKey
     * @param \Magento\Framework\App\Request\Http $request
     * @param \Webkul\MpMultiShopifyStoreMageConnect\Api\ProductmapRepositoryInterface $coll
     */
    public function __construct(
        Context $context,
        Filter $filter,
        CollectionFactory $collectionFactory,
        PageFactory $resultPageFactory,
        \Magento\Customer\Model\Session $customerSession,
        \Webkul\Marketplace\Helper\Data $mpHelper,
        \Magento\Customer\Model\Url $url,
        \Magento\Framework\Data\Form\FormKey $formKey,
        \Magento\Framework\App\Request\Http $request,
        \Coditron\Mpmultistorewoocommerce\Api\ProductmapRepositoryInterface $coll
    ) {
        $this->filter = $filter;
        $this->collectionFactory = $collectionFactory;
        $this->_customerSession = $customerSession;
        $this->resultPageFactory = $resultPageFactory;
        $this->mpHelper = $mpHelper;
        $this->url = $url;
        $this->request = $request;
        $this->formKey = $formKey;
        $this->coll = $coll;
        $this->request->setParam('form_key', $this->formKey->getFormKey());
        parent::__construct($context);
    }

    /**
     * Pickup Order View Page
     *
     * @return \Magento\Framework\View\Result\Page
     */
    public function execute()
    {
        $collection = $this->filter->getCollection($this->collectionFactory->create());
        $catRecordDeleted = 0;
        foreach ($collection as $categoryMap) {
            $categoryMap->setId($categoryMap->getEntityId());
            $categoryMap->delete();
        }
        $this->messageManager->addSuccess(__("Deleted the enteries"));
        return $this->resultRedirectFactory->create()->setPath('*/*/product');
    }
}
