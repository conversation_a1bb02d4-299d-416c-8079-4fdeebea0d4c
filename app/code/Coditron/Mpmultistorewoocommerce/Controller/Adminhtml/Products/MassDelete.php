<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Products;

use Magento\Framework\Controller\ResultFactory;
use Magento\Backend\App\Action\Context;
use Coditron\Mpmultistorewoocommerce\Api\ProductmapRepositoryInterface;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Products;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;

class MassDelete extends Products
{
    /**
     * @var ProductmapRepositoryInterface
     */
    private $productMapRepository;
    
    /**
     * @var CollectionFactory
     */
    private $_productCollectionFactory;

    /**
     * @param Context                       $context
     * @param ProductmapRepositoryInterface $productMapRepository
     * @param CollectionFactory             $productCollectionFactory
     */
    public function __construct(
        Context $context,
        ProductmapRepositoryInterface $productMapRepository,
       // CollectionFactory $productCollectionFactory
    ) {
        $this->productMapRepository = $productMapRepository;
      //  $this->_productCollectionFactory = $productCollectionFactory;
        parent::__construct($context);
    }

    /**
     * Excute Function
     *
     * @return \Magento\Backend\Model\View\Result\Redirect
     */
    public function execute()
    {
        $params = $this->getRequest()->getParams();
        $productEntityIds = $params['productEntityIds'];
        if (!is_array($productEntityIds)) {
            $productEntityIds = explode(',', $productEntityIds);
        }
        $collection = $this->productMapRepository
                    ->getCollectionByIds($productEntityIds);
        $productMapCount = 0;
        $deletedIdsArr = [];
        foreach ($collection as $productMap) {
            array_push($deletedIdsArr, $productMap->getId());
            $productMap->delete();
            ++$productMapCount;
        }

        $this->messageManager->addSuccess(
            __("A total of %1 record(s) have been deleted.", $productMapCount)
        );

        return $this->resultFactory->create(
            ResultFactory::TYPE_REDIRECT
        )->setPath(
            '*/woocommerceaccount/edit',
            [
                'id'=>$params['rule_id'],
                'active_tab' => 'mapproduct'
            ]
        );
    }
}
