<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Products;

use Magento\Framework\Controller\ResultFactory;
use Magento\Backend\App\Action\Context;
use Coditron\Mpmultistorewoocommerce\Api\ProductmapRepositoryInterface;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Products;

class MassAssignToCategory extends Products
{

    /**
     * @var \Magento\Catalog\Api\ProductRepositoryInterface
     */
    private $productRepository;

    /**
     * @var ProductmapRepositoryInterface
     */
    private $productMapRepository;

    /**
     * Construct function
     *
     * @param Context                                         $context
     * @param \Magento\Catalog\Api\ProductRepositoryInterface $productRepository
     * @param ProductmapRepositoryInterface                   $productMapRepository
     */
    public function __construct(
        Context $context,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        ProductmapRepositoryInterface $productMapRepository
    ) {
        $this->productRepository = $productRepository;
        $this->productMapRepository = $productMapRepository;
        parent::__construct($context);
    }

    /**
     * Execute function
     *
     * @return \Magento\Backend\Model\View\Result\Redirect
     */
    public function execute()
    {
        $params = $this->getRequest()->getParams();
        $collection =  $this->productMapRepository
                    ->getCollectionByIds($params['productEntityIds']);
        $prodMapUpdate = 0;
        foreach ($collection as $proMap) {
            $catId = $this->getRequest()->getParam('magecate');
            $pro = $this->productRepository->getById($proMap->getMagentoProId());
            $pro->setCategoryIds([$catId]);
            $this->productRepository->save($pro, true);
            $cat = $proMap->getMageCatId();
            if ($cat == '' || $cat == ',') {
                $proMap->setMageCatId(trim(implode(',', $cat), ','))->save();
            } else {
                 $getSynchProCat = explode(',', trim($cat, ','));
                 $getSynchProCat[] = $catId;
                 $proMap->setMageCatId(implode(',', $getSynchProCat))->save();
            }
            ++$prodMapUpdate;
        }
        $this->messageManager->addSuccess(
            __("A total of %1 record(s) have been updated.", $prodMapUpdate)
        );

        return $this->resultFactory->create(
            ResultFactory::TYPE_REDIRECT
        )->setPath(
            '*/woocommerceaccount/edit',
            [
                'id'=>$params['rule_id'],
                'active_tab' => 'mapproduct'
            ]
        );
    }
}
