<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Products;

use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Backend\App\Action\Context;
use Coditron\Mpmultistorewoocommerce\Model\ImportedtmpproductFactory;
use Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Importedtmpproduct\CollectionFactory as ImportcollectionFactory;
use Coditron\Mpmultistorewoocommerce\Model\WoocommercecategorymapFactory;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Catalog\Api\Data\ProductInterfaceFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Store\Model\StoreManagerInterface;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Products;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Catalog\Model\Product\Gallery\Processor;
use Magento\Framework\Filesystem\Io\File;
use Magento\Catalog\Api\Data\ProductAttributeMediaGalleryEntryInterfaceFactory;
use Magento\Catalog\Api\ProductAttributeMediaGalleryManagementInterface;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Products\AttributeHandler;

class Createproduct extends Products
{
    protected $resultJsonFactory;
    protected $importedProductFactory;
    protected $importCollectionFactory;
    protected $productFactory;
    protected $storeManager;
    protected $woocommercecategorymapFactory;
    private $productRepository;
    private $productCollectionFactory;
    private $product;
    protected $filesystem;
    protected $fileIo;
    protected $galleryProcessor;
    protected $attributeHandler;

     /**
     * Directory List
     *
     * @var DirectoryList
     */
    protected $directoryList;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Helper\Data
     */
    private $helper;

     /**
     * @var ProductAttributeMediaGalleryEntryInterfaceFactory
     */
    private $mediaGalleryEntryFactory;

    /**
     * @var ProductAttributeMediaGalleryManagementInterface
     */
    private $mediaGalleryManagement;

    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        ImportedtmpproductFactory $importedProductFactory,
        Product $product,
        ProductInterfaceFactory $productFactory,
        CollectionFactory $productCollectionFactory,
        ProductRepositoryInterface $productRepository,
        WoocommercecategorymapFactory $woocommercecategorymapFactory,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $helper,
        StoreManagerInterface $storeManager,
        DirectoryList $directoryList,
        Filesystem $filesystem,
        File $fileIo,
        Processor $galleryProcessor,
        ImportcollectionFactory $importCollectionFactory,
        ProductAttributeMediaGalleryEntryInterfaceFactory $mediaGalleryEntryFactory,
        ProductAttributeMediaGalleryManagementInterface $mediaGalleryManagement,
        AttributeHandler $attributeHandler
    ) {
        $this->resultJsonFactory = $resultJsonFactory;
        $this->importedProductFactory = $importedProductFactory;
        $this->productFactory = $productFactory;
        $this->product = $product;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->productRepository = $productRepository;
        $this->woocommercecategorymapFactory = $woocommercecategorymapFactory;
        $this->helper = $helper;
        $this->storeManager = $storeManager;
        $this->directoryList = $directoryList;
        $this->filesystem = $filesystem;
        $this->fileIo = $fileIo;
        $this->galleryProcessor = $galleryProcessor;
        $this->importCollectionFactory = $importCollectionFactory;
        $this->mediaGalleryEntryFactory = $mediaGalleryEntryFactory;
        $this->mediaGalleryManagement = $mediaGalleryManagement;
        $this->attributeHandler = $attributeHandler;
        parent::__construct($context);
    }


    public function execute()
    {
        $result = $this->resultJsonFactory->create();
        $response = ['status' => false];

        try {
            $productImportDataArray = [];
            $productdefaultAttribute = 4;
            $categoryInputdata = [];
            if ($this->getRequest()->getContent()) {
                $postData = $this->getRequest()->getContent();
                $postDataArray = json_decode($postData, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new \Exception('Invalid JSON data: ' . json_last_error_msg());
                }

                if (isset($postDataArray['productImportData'])) {
                    $productImportDataArray = json_decode($postDataArray['productImportData'], true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        throw new \Exception('Invalid JSON data in productImportData: ' . json_last_error_msg());
                    }
                    $isAllProImport = $productImportDataArray['isAllProImport'] ?? null;
                    $productdefaultAttribute = $isAllProImport['attribute_set_id'] ?? '4';
                    $categoryInputdata['woocommerce_url'] = $isAllProImport['woocommerce_url'];
                    $categoryInputdata['woocommerce_consumer_key'] = $isAllProImport['woocommerce_consumer_key'];
                    $categoryInputdata['woocommerce_consumer_secret_key'] = $isAllProImport['woocommerce_consumer_secret_key'];
                    $cred = $this->helper->decryptTheApiCredential($categoryInputdata['woocommerce_consumer_key'], $categoryInputdata['woocommerce_consumer_secret_key']);
                    $categoryInputdata['woocommerce_consumer_key'] = $cred['woocommerce_consumer_key'];
                    $categoryInputdata['woocommerce_consumer_secret_key'] = $cred['woocommerce_consumer_secret_key'];
                }
            }

            $attributeSetId = $productdefaultAttribute;
            
            $woocommerceAttribute = $this->helper->getWooCommerceProductAttributes($categoryInputdata);

            foreach ($woocommerceAttribute['attribute'] as $woocommerceAttributeCreate) {

                $woocommerceAttributeterms = $this->helper->getWooCommerceProductAttributesTerms($categoryInputdata,$woocommerceAttributeCreate['id']);
                $isattributecreate = $this->attributeHandler->createOrUpdateAttribute(
                    $woocommerceAttributeCreate['slug'],
                    $woocommerceAttributeCreate['name'],
                    $woocommerceAttributeCreate['type'],
                    $attributeSetId,
                    $woocommerceAttributeterms
                );
            }

            if (empty($productImportDataArray)) {
                throw new \Exception('No product import data provided.');
            }

            $importedProductCollection = $this->importCollectionFactory->create()
                ->addFieldToFilter('rule_id', $productImportDataArray['isAllProImport']['entity_id']);

            $categoryMapCollection = $this->woocommercecategorymapFactory->create()->getCollection();
            $categorymapdata = [];
            foreach ($categoryMapCollection as $categoryMap) {
                $mageCatId = (string)$categoryMap->getData('mage_cat_id');
                $shopifyCatId = $categoryMap->getData('woocommerce_cat_id');
                if (isset($categorymapdata[$shopifyCatId])) {
                    $categorymapdata[$shopifyCatId] .= ',' . $mageCatId;
                } else {
                    $categorymapdata[$shopifyCatId] = $mageCatId;
                }
            }

            foreach ($importedProductCollection as $importedProduct) {
                $productData = json_decode($importedProduct->getProductData(), true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new \Exception('Invalid JSON data in imported product: ' . json_last_error_msg());
                }

                $categories = $productData['categories'] ?? [];
                $categorydata = array_column($categories, 'id');
                $resultArray = [];
                foreach ($categorydata as $categoryId) {
                    if (isset($categorymapdata[$categoryId])) {
                        $mageCatIds = explode(',', $categorymapdata[$categoryId]);
                        $resultArray = array_merge($resultArray, $mageCatIds);
                    }
                }
                $resultArray = array_unique($resultArray);
                $productWoocommerceAttribute = $productData['attributes'] ?? [];
                $woocommerceAttributes = [];

                foreach ($productWoocommerceAttribute as $attribute) {
                    $cleaned_string = preg_replace('/[^a-zA-Z0-9_]/', '_', $attribute['slug']);
                    $this->attributeHandler->createOrUpdateAttribute($cleaned_string,$attribute['name'],$attribute['options']);
                }

                

                $product = $this->productCollectionFactory->create()
                    ->addAttributeToSelect('*')
                    ->addAttributeToFilter("sku", $productData['sku'])
                    ->load();

                if ($product->count() == 1) {

                    $sku = $productData['sku'];
                    $proresult = $this->updateProduct($sku, $productdefaultAttribute, $productData, $resultArray);
                    $response = [
                        'status' => true,
                        'message' => 'Product updated successfully.'
                    ];
                } else {
  
                    $newProduct = $this->productFactory->create();
                    $newProduct->setSku($productData['sku']);
                    $newProduct->setName($productData['name']);
                    $newProduct->setTypeId($productData['type']);
                    $newProduct->save();
                    $proresult = $this->updateProduct($productData['sku'], $productdefaultAttribute, $productData, $resultArray);
                    $response = [
                        'status' => true,
                        'message' => 'Product created successfully.'
                    ];
                }
            }
        } catch (LocalizedException $e) {
            $response['message'] = $e->getMessage();
        } catch (\Exception $e) {          
            $response['message'] = $e->getMessage();
        }

        return $result->setData($response);
    }

    /**
     * Save product image from URL
     *
     * @param Product $product
     * @param string $imageUrl
     * @throws \Exception
     */
    protected function saveProductImage(Product $product, $imageUrl)
    {
        try {

            $mediaPath = $this->getMediaDirTmpDir();
            $this->fileIo->checkAndCreateFolder($mediaPath);

            $imageContent = file_get_contents($imageUrl);
            if ($imageContent === false) {
                throw new \Exception('Failed to download image from ' . $imageUrl);
            }

            $imageFileName = basename($imageUrl);
            $imagePath = $mediaPath . $imageFileName;
            $this->fileIo->write($imagePath, $imageContent);

            $imageType = ['image', 'small_image', 'thumbnail', 'swatch_image'];
            $this->addImageToMediaGallery($product, $imagePath, $imageType);
            return true;
           
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }
    }

    /**
     * Add image to product's media gallery
     *
     * @param Product $product
     * @param string $imagePath
     * @param array $imageType
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function addImageToMediaGallery(Product $product, $imagePath, $imageType = [])
    {
        $product->addImageToMediaGallery(
            $imagePath,
            $imageType,
            false,
            false
        );

        $this->productRepository->save($product);
    }

    /**
     * Media directory name for the temporary file storage
     * pub/media/images
     *
     * @return string
     */
    protected function getMediaDirTmpDir()
    {
        return $this->directoryList->getPath(DirectoryList::MEDIA) . DIRECTORY_SEPARATOR . 'wcproduct' . DIRECTORY_SEPARATOR;
    }

    /**
     * Update product
     *
     * @param string $sku
     * @param string $productdefaultAttribute
     * @param array $productData
     * @param array $resultArray
     * @throws \Exception
     */
    protected function updateProduct(string $sku, string $productdefaultAttribute, array $productData = [], array $resultArray = [])
    {
        $fullProduct = $this->product->loadByAttribute('sku', $sku);

        if ($productData) {
            $fullProduct->setSku($productData['sku']);
            $fullProduct->setName($productData['name']);
            $fullProduct->setTypeId($productData['type']);
            $fullProduct->setAttributeSetId((int)$productdefaultAttribute);
            $fullProduct->setCategoryIds($resultArray);
            $fullProduct->setWebsiteIds([$this->storeManager->getStore()->getWebsiteId()]);
            $fullProduct->setVisibility(4);
            $fullProduct->setStatus(1);
            $fullProduct->setPrice($productData['price']);
            $fullProduct->setStockData([
                'use_config_manage_stock' => 0, 
                'qty' => $productData['stock_quantity'], 
                'is_in_stock' => $productData['stock_status']
            ]);
            $fullProduct->setData('special_price', $productData['sale_price']);
            $fullProduct->save();

            if (isset($productData['images']) && is_array($productData['images'])) {
                $imageUrls = array_column($productData['images'], 'src');

                foreach ($imageUrls as $imageUrl) {
                    
                    try {
                        $imageresult = $this->saveProductImage($fullProduct, $imageUrl);

                    } catch (\Exception $e) {
                        $response['message'] = $e->getMessage();
                    }
                }
            }
        }
        return true;
    }
}