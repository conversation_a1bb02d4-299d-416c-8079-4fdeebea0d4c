<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Products;

use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Eav\Model\Entity\AttributeFactory;
use Magento\Store\Model\StoreManagerInterface;

class AttributeHandler
{
    /**
     * @var EavConfig
     */
    private $eavConfig;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var AttributeRepositoryInterface
     */
    private $attributeRepository;

    /**
     * @var AttributeOptionInterfaceFactory
     */
    private $attributeOptionFactory;

    /**
     * @var AttributeOptionLabelInterfaceFactory
     */
    private $attributeOptionLabelFactory;

    public function __construct(
        EavConfig $eavConfig,
        StoreManagerInterface $storeManager,
        AttributeFactory $eavattributeFactory,
        AttributeRepositoryInterface $attributeRepository,
    ) {
        $this->eavConfig = $eavConfig;
        $this->storeManager = $storeManager;
        $this->attributeFactory = $eavattributeFactory;
        $this->attributeRepository = $attributeRepository;
    }


    public function createOrUpdateAttribute($attributeCode, $attributeLabel, $type, $attributeSetId, $attributeoption)
    {
    
        $flag = false;
    
        // Retrieve attributes for the specified attribute set
        $attributes = $this->eavConfig->getEntityAttributes(\Magento\Catalog\Model\Product::ENTITY, $attributeSetId);
        $attributeMap = [];
        foreach ($attributes as $attribute) {
            $frontendLabel = $attribute->getFrontendLabel();
            if ($frontendLabel !== null) {
                $key = strtolower($frontendLabel);
                $attributeMap[$key] = $attribute->getAttributeCode();
            }
        }
    
        try {
            // Check if attribute label exists in the attribute map
            if (isset($attributeMap[strtolower($attributeLabel)])) {
                $existingAttributeCode = $attributeMap[strtolower($attributeLabel)];
              
                $attribute = $this->eavConfig->getAttribute(\Magento\Catalog\Model\Product::ENTITY, $existingAttributeCode);
                $this->updateAttributeOptions($attribute, $attributeoption);
                $flag = true;
            } else {
                $this->createAttribute($attributeCode, $attributeLabel, $type, $attributeSetId);
                $flag = true;
            }
        } catch (\Exception $e) {
           
        }
    
        return $flag;
    }
    
    /**
     * Create a new EAV attribute in Magento.
     *
     * @param string $attributeCode
     * @param string $attributeLabel
     * @param string $type
     * @param int $attributeSetId
     * @return AttributeInterface
     * @throws CouldNotSaveException
     * @throws InputException
     * @throws LocalizedException
     */
    private function createAttribute($attributeCode, $attributeLabel, $type, $attributeSetId)
    {
        $data = [
            'attribute_code' => $attributeCode,
            'frontend_input' => $type, // Adjust input type as needed
            'frontend_label' => $attributeLabel,
            'entity_type_id' => $this->eavConfig->getEntityType('catalog_product')->getId(),
            'is_user_defined' => true,
            'is_visible' => true,
            'is_required' => false,
            'is_global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_GLOBAL,
            'backend' => '',
            'frontend' => '',
            'class' => '',
            'source' => '',
            'default' => '',
            'searchable' => false,
            'filterable' => false,
            'comparable' => false,
            'visible_on_front' => false,
            'used_in_product_listing' => true,
            'unique' => false,
            'apply_to' => ''
        ];
    
        /** @var \Magento\Eav\Model\Entity\Attribute $attribute */
        $attribute = $this->attributeFactory->create();
        $attribute->addData($data);
        $attribute->save();
    
        // Assign attribute to attribute set
        $this->eavSetup->addAttributeToSet(
            'catalog_product',
            $attributeSetId,
            $this->eavConfig->getEntityType('catalog_product')->getDefaultGroupId(),
            $attributeCode
        );
    
        return $attribute;
    }
    
/**
 * Update attribute options.
 *
 * @param AttributeInterface $attribute
 * @param array $attributeOptions
 */
private function updateAttributeOptions($attribute, $attributeOptions)
{
    try {
        // Get existing options
        $existingOptions = $attribute->getSource()->getAllOptions();
        $existingOptionLabels = [];
        foreach ($existingOptions as $option) {
            if (isset($option['label']) && $option['label'] !== null) {
                $existingOptionLabels[strtolower($option['label'])] = $option['value'];
            }
        }

        // Prepare new options
        $newOptions = [];
        foreach ($attributeOptions['attribute'] as $option) {
            if (!isset($existingOptionLabels[strtolower($option['name'])])) {
                $newOptions[] = [
                    'label' => $option['name'],
                    'value' => $option['slug']
                ];
            }
        }

        if (!empty($newOptions)) {
            $attribute->setOption(['values' => $newOptions]);
            $attribute->save();
        }

    } catch (\Exception $e) {
    }
}

 /**
 * Update attribute options based on WooCommerce API data.
 *
 * @param string $attributeCode
 * @param array $optionsData
 * @throws CouldNotSaveException
 * @throws InputException
 * @throws LocalizedException
 */
private function createOrUpdateAttributeterms($productdefaultAttribute,$attributeLabel,$type)
{
    try {
        // Fetch existing options of the attribute
        $attribute = $this->eavConfig->getAttribute('catalog_product', $attributeCode);
        $options = $attribute->getSource()->getAllOptions();
        
        // Filter out invalid options
        $validOptions = [];
        foreach ($options as $option) {
            if ($option['value'] > 0) {
                $validOptions[] = $option;
            }
        }

        // Iterate over the provided options data from WooCommerce
        foreach ($optionsData as $optionData) {

            $optionLabel = $optionData; // Get the correct label
            $optionValue = strtolower($optionData); // Convert to lowercase

            if (!$this->optionExists($validOptions, $optionLabel)) {
                // Create new option
                $this->addOption($attribute, $optionLabel, $optionValue);
            }
        }

    } catch (\Exception $e) {
        throw new LocalizedException(__("Error updating attribute options: %1", $e->getMessage()));
    }
}

/**
 * Check if an option with given label already exists.
 *
 * @param array $options
 * @param string $label
 * @return bool
 */
private function optionExists($options, $label)
{
    foreach ($options as $option) {
        if ($option['label'] == $label) {
            return true;
        }
    }
    return false;
}

/**
 * Add a new option to the attribute.
 *
 * @param AttributeInterface $attribute
 * @param string $label
 * @param string $value
 * @throws CouldNotSaveException
 * @throws InputException
 * @throws LocalizedException
 */
private function addOption(AttributeInterface $attribute, $label, $value)
{
    try {
        $optionLabel = $this->attributeOptionLabelFactory->create();
        $optionLabel->setStoreId(0); // Default store ID
        $optionLabel->setLabel($label);

        $option = $this->attributeOptionFactory->create();
        $option->setLabel($optionLabel);
        $option->setValue($value);

        $attribute->addData([
            'option' => [
                'value' => [
                    'option_0' => [$option]
                ]
            ]
        ]);

        $attribute->save();

    } catch (\Exception $e) {
        throw new LocalizedException(__("Error adding option: %1", $e->getMessage()));
    }
}
}