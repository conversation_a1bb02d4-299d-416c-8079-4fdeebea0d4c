<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Products;

use Magento\Backend\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\Controller\Result\JsonFactory;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Products;
use Coditron\Mpmultistorewoocommerce\Model\ImportedtmpproductFactory;
use Magento\Framework\Controller\ResultFactory;

class Import extends Products
{
    /**
     * @var JsonFactory
     */
    private $resultJsonFactory;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Helper\ManageRawData
     */
    private $manageRawDataHelper;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Helper\Data
     */
    private $helper;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\TimezoneInterface
     */
    private $date;

    /**
     * @var ImportedtmpproductFactory
     */
    private $importedProductFactory;

    /**
     * Construct function
     *
     * @param Context                                                         $context
     * @param JsonFactory                                                     $resultJsonFactory
     * @param \Coditron\Mpmultistorewoocommerce\Helper\ManageRawData          $manageRawDataHelper
     * @param \Coditron\Mpmultistorewoocommerce\Helper\Data                   $helper
     * @param ImportedtmpproductFactory $importedProductFactory
     * @param \Magento\Framework\Stdlib\DateTime\TimezoneInterface            $date
     */
    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        \Coditron\Mpmultistorewoocommerce\Helper\ManageRawData $manageRawDataHelper,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $helper,
        ImportedtmpproductFactory $importedProductFactory,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $date
    ) {
        parent::__construct($context);
        $this->resultJsonFactory = $resultJsonFactory;
        $this->manageRawDataHelper = $manageRawDataHelper;
        $this->helper = $helper;
        $this->importedProductFactory = $importedProductFactory;
        $this->date = $date;
    }

    /**
     * Mapped Product List page.
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
        try {
            $productImportDataArray = [];

            if ($this->getRequest()->getContent()) {
                $postData = $this->getRequest()->getContent();
                if (!$postData) {
                    return $this->resultFactory->create(
                        ResultFactory::TYPE_REDIRECT
                    )->setPath('mpmultistorewoocommerce/*/');
                }
                $postDataArray = json_decode($postData, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new \Exception('Invalid JSON data: ' . json_last_error_msg());
                }
               if (isset($postDataArray['productImportData'])) {
                    $productImportDataArray = json_decode($postDataArray['productImportData'], true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        throw new \Exception('Invalid JSON data in productImportData: ' . json_last_error_msg());
                    }
                    $isAllProImport = $productImportDataArray['isAllProImport'] ?? null;
                    $cates = $productImportDataArray['cates'] ?? null;

                    if ($isAllProImport) {
                        $entityId = (string)$isAllProImport['entity_id'] ?? null;
                        $apiCredentials = $this->helper->getTheShopApiCredentials($entityId);
                      
                        $apikey = $this->helper->decryptTheApiCredential($apiCredentials['woocommerce_consumer_key'], $apiCredentials['woocommerce_consumer_secret_key']);
                    
                        $apiCredentials['woocommerce_consumer_key'] = $apikey['woocommerce_consumer_key'];
                        $apiCredentials['woocommerce_consumer_secret_key'] = $apikey['woocommerce_consumer_secret_key'];
                  
                        $isValidApiCred = $this->helper->authorizeWooCommerceShop($apiCredentials);
                    
                        if ($isValidApiCred['status']) {
                   
                            $dt = $this->date->date();
                            $currentDate = $dt->format('Y-m-d\TH:i:s');
                            $endTime = $dt->format('Y-m-d\TH:i:s');
                            $totalProductsCount = 0;
                            $productIds = [];
                            $allCategoryProducts = [];

                            if (isset($productImportDataArray['cates']) && $productImportDataArray['cates']) {
                                $catesArray = explode(',', $cates);

                                foreach ($catesArray as $cate) {

                                    $categoryProduct = $this->helper->getwoocommerceProductListFromShopCategryId($apiCredentials, trim($cate));
                                    $productCount = count($categoryProduct['data']);
                                    $totalProductsCount += $productCount;
                                    $productIds = array_merge($productIds, $categoryProduct['product_id']);
                                    $allCategoryProducts[$cate] = [
                                        'products' => $categoryProduct['data'],
                                        'count' => $productCount
                                    ];
                                }
                             
                                $productIds = array_unique($productIds);

                                foreach ($allCategoryProducts as $cate => $categoryData) {
                                    foreach ($categoryData['products'] as $product) {
                                        if (isset($product['id'])) { 
                                            $existingProduct = $this->importedProductFactory->create()->load($product['id'], 'item_id');
                                       
                                            if ($existingProduct->getId()) {
                                                $productModel = $existingProduct;
                                            } else {
                                                $productModel = $this->importedProductFactory->create();
                                            }
                                
                                            $productModel->setItemType($product['type']);
                                            $productModel->setItemId($product['id']);
                                            $productModel->setProductData(json_encode($product));
                                            $productModel->setAssociateProducts(json_encode($product['grouped_products']));
                                            $productModel->setVirtual($product['virtual'] ?? false); 
                                            $productModel->setDownloadable($product['downloadable'] ?? false); 
                                            $productModel->setCreatedAt($currentDate);
                                            $productModel->setRuleId($entityId); 
                                            $productModel->save();
                                
                                        } else {

                                           $response = [
                                            'status' => false,
                                            'error' => "Product  not impoted"
                                            ];
                                           return $this->resultJsonFactory->create()->setData($response);
                                        }
                                    }
                                }

                                $response = [
                                    'status' => true,
                                    'data' => $allCategoryProducts,
                                    'total_products' => $totalProductsCount,
                                    'product_ids' => $productIds
                                ];

                                return $this->resultJsonFactory->create()->setData($response['status']);
                            } else {
                                $errorMessage = 'No categories provided in the request.';

                                return $this->resultJsonFactory->create()->setData(['status' => false, 'message' => $errorMessage]);
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            $response = [
                'status' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ];
            return $this->resultJsonFactory->create()->setData($response['status']);
        }
    }
}
