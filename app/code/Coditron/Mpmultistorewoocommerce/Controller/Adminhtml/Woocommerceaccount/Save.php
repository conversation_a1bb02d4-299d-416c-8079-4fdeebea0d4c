<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Woocommerceaccount;

use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Woocommerceaccount;
use Magento\Framework\Controller\ResultFactory;
use Coditron\Mpmultistorewoocommerce\Helper\Data;

class Save extends Woocommerceaccount
{
    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    private $resultPageFactory;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\WoocommerceaccountsFactory
     */
    private $woocommerceAccountsFactory;

    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Helper\Data
     */
    private $dataHelper;

    /**
     * @var \Magento\Framework\Encryption\EncryptorInterface
     */
    private $encryptor;

     /**
      * Construct function
      *
      * @param \Magento\Backend\App\Action\Context $context
      * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
      * @param \Coditron\Mpmultistorewoocommerce\Model\WoocommerceaccountsFactory $woocommerceAccountsFactory
      * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
      * @param \Coditron\Mpmultistorewoocommerce\Helper\Data $dataHelper
      */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        \Coditron\Mpmultistorewoocommerce\Model\WoocommerceaccountsFactory $woocommerceAccountsFactory,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $dataHelper
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->woocommerceAccountsFactory = $woocommerceAccountsFactory;
        $this->encryptor = $encryptor;
        $this->dataHelper = $dataHelper;
    }

    /**
     * Woo commmerce account details
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
        $flag = false;
        $reserveId = 0;
        $resultRedirect = $this->resultRedirectFactory->create();
        $temp = $parameters = $this->getRequest()->getParams();

        $parameters['woocommerce_consumer_key'] = $this->beforeSave($parameters['woocommerce_consumer_key'])['value'];
        $parameters['woocommerce_consumer_secret_key'] = $this->beforeSave($parameters['woocommerce_consumer_secret_key'])['value'];
        
        $status = $this->checkForEmptyValue($parameters);
        $emptyValueCheck = ['status'=>'', 'msg'=> ''];

        if ($status['status']) {
            $data = $this->dataHelper->authorizeWooCommerceShop($temp);
            
            if (isset($data['error_msg'])){
                $this->messageManager
                ->addError(
                     __($data['error_msg'])
                 );
                 return $this->resultFactory->create(
                    ResultFactory::TYPE_REDIRECT
                )->setPath('*/*/');
            } 
            $error = __('ShopiWoo commerce user didn\'t authorize successfully, Please try again.');
            if ($data != null && isset($data['api_request_http_code']) && $data['api_request_http_code'] == 200) {
                $parameters['woocommerce_base_currency'] = $data['woocommerce_base_currency'];
                $parameters['magento_base_currency'] = $this->dataHelper->getBaseCurrencyCode();

                $model = $woocommerceAccountsCollection = $this->woocommerceAccountsFactory->create();
                $model->addData($parameters)->save();
                $this->messageManager
                    ->addSuccess(
                         __('Woo Commerce details saved successfully, Now edit the record for syncronization process')
                     );
                if (isset($temp['id']) && isset($temp['back'])) {
                    return $this->resultFactory->create(
                        ResultFactory::TYPE_REDIRECT
                    )->setPath('*/*/edit', ['id'=>$temp['id']]);
                } else {
                    return $this->resultFactory->create(
                         ResultFactory::TYPE_REDIRECT
                     )->setPath('*/*/');
                 }
            } else {
                 $this->messageManager->addError($data['error_msg']);
                 return $this->resultFactory->create(
                    ResultFactory::TYPE_REDIRECT
                 )->setPath('*/*/');
             }
         } else {
             $this->messageManager->addError(__('Something went wrong'));
             return $this->resultFactory->create(
                 ResultFactory::TYPE_REDIRECT
             )->setPath('*/*/');
         }
    }

    /**
     * BeforeSave function check that value is obscure or not
     *
     * @param string $value
     * @return array
     */
    private function beforeSave($value)
    {
        if (!preg_match('/^\*+$/', $value) && !empty($value)) {
            $encrypted = $this->encryptor->encrypt($value);
            return ['value' => $encrypted, 'status' => true];
        } elseif (empty($value)) {
            return ['value' => $value, 'status' => false];
        }
        return ['value' => $value, 'status' => false];
    }

    /**
     * getTheConsumerKeyFromDbToVerify function get the api key
     *
     * @param int $id
     * @return string
     */
    private function getTheConsumerKeyFromDbToVerify($id = '')
    {
        $modal = $this->woocommerceAccountsFactory->create()->load($id);
        return $modal->getWoocommerceConsumerKey();
    }

     /**
     * getTheConsumerssecretKeyFromDbToVerify function get the api password
     *
     * @param int $id
     * @return string
     */
    private function getTheConsumerssecretKeyFromDbToVerify($id = '')
    {
        $modal = $this->woocommerceAccountsFactory->create()->load($id);
        return $modal->getWoocommerceConsumerSecretKey();
    }

         /**
      * CheckForEmptyValue function check for empty value
      *
      * @param array $parameters
      * @return array
      */
      private function checkForEmptyValue($parameters)
      {
          if ($parameters['woocommerce_consumer_key'] == '' ||
              $parameters['woocommerce_consumer_secret_key'] == '' ||
              $parameters['woocommerce_url'] == ''
          ) {
              return ['status' => 0, 'msg'=>'requird fields are empty'];
          }
          return ['status'=> 1, 'msg'=>''];
      }
}
