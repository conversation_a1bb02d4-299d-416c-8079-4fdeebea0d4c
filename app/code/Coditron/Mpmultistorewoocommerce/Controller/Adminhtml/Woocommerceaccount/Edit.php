<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Woocommerceaccount;

use Magento\Framework\Controller\ResultFactory;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Woocommerceaccount;
use Magento\MysqlMq\Model\QueueManagement;

class Edit extends Woocommerceaccount
{
    /**
     * @var \Magento\Backend\Model\Session
     */
    private $backendSession;

    /**
     * @var \Magento\Framework\Registry
     */
    private $registry;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\Woocommerceaccounts 
     */
    private $woocommerceAccounts;

            /**
     * @var \Coditron\Mpmultistorewoocommerce\Helper\Data
     */
    private $helper;

    /**
     * @var \Magento\Framework\MessageQueue\QueueRepository
     */
    private $queueRepository;

    /**
     * @var \Magento\Framework\Message\ManagerInterface
     */
    protected $messageManager;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Coditron\Mpmultistorewoocommerce\Model\WoocommerceaccountsFactory $woocommerceAccounts
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $registry,
        \Coditron\Mpmultistorewoocommerce\Model\WoocommerceaccountsFactory $woocommerceAccounts,
        \Magento\Framework\MessageQueue\QueueRepository $queueRepository,
        \Magento\MysqlMq\Model\ResourceModel\Queue $queueResource,
        \Magento\Framework\Message\ManagerInterface $messageManager
    ) {
        $this->backendSession = $context->getSession();
        $this->registry = $registry;
        $this->woocommerceAccounts = $woocommerceAccounts;
        $this->queueRepository = $queueRepository;
        $this->queueResource = $queueResource;
        $this->messageManager = $messageManager;
        parent::__construct($context);
    }

    /**
     * Excute Function
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
        $ruleId = $this->getRequest()->getParam('id');
        $woocommerceAccountModel = $this->woocommerceAccounts->create();
         if ($this->getRequest()->getParam('id')) {
             $woocommerceAccountModel->load($this->getRequest()->getParam('id'));
         }
         $data = $this->backendSession->getFormData(true);
         if (!empty($data)) {
             $woocommerceAccountModel->setData($data);
         }
        $this->registry->register('woocommerceaccount_info', $woocommerceAccountModel);
        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        $resultPage->setActiveMenu('Coditron_Mpmultistorewoocommerce::manager');
        $resultPage->getConfig()->getTitle()->prepend(__('Woo Commrece Account'));
        $resultPage->getConfig()->getTitle()->prepend(
            $woocommerceAccountModel->getId() ? $woocommerceAccountModel->getGroupCode() : __('Woo Commrece Account')
        );

        $left = $resultPage->getLayout()
            ->createBlock(\Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tabs::class);
        $resultPage->addLeft($left);
        $content = $resultPage->getLayout()
            ->createBlock(\Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit::class);
        $resultPage->addContent($content);
        return $resultPage;
    }
}
