<?php
/**
* Language translation Record Index Controller.
* @category  Coditron
* @package   Coditron_Mpmultistorewoocommerce
* @autor    Coditron
* @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
* @license   https://www.coditron.com/LICENSE.txt
*/
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Woocommerceaccount;

use Magento\Backend\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommerceaccounts\CollectionFactory;

class Index extends \Magento\Backend\App\Action
{
   /**
    * @var PageFactory
    */
   private $resultPageFactory;

   /**
    * @var CollectionFactory
    */
   private $collectionFactory;

   /**
    * @param Context $context
    * @param PageFactory $resultPageFactory
    * @param CollectionFactory $collectionFactory
    */
   public function __construct(
       Context $context,
       PageFactory $resultPageFactory,
       CollectionFactory $collectionFactory
   ) {
       parent::__construct($context);
       $this->resultPageFactory = $resultPageFactory;
       $this->collectionFactory = $collectionFactory;
   }

   /**
    * Execute method to load WooCommerce accounts and display them.
    *
    * @return \Magento\Framework\Controller\ResultInterface
    */
   public function execute()
   {

        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $resultPage = $this->resultPageFactory->create();
        $resultPage->setActiveMenu('Mpmultistorewoocommerce::woocommerce_account_connect');
        $resultPage->getConfig()->getTitle()->prepend(__('Woo Commerce Accounts'));
        return $resultPage;

   }
}
