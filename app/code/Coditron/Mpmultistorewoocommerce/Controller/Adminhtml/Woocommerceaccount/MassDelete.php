<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Woocommerceaccount;

use Magento\Framework\Controller\ResultFactory;
use Magento\Backend\App\Action\Context;
use Magento\Ui\Component\MassAction\Filter;
use Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommerceaccounts\CollectionFactory;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Woocommerceaccount;

class MassDelete extends Woocommerceaccount
{
    /**
     * Massactions filter.
     *
     * @var Filter
     */
    private $filter;

    /**
     * @var CollectionFactory
     */
    private $collectionFactory;

    /**
     * @param Context           $context
     * @param Filter            $filter
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(
        Context $context,
        Filter $filter,
        CollectionFactory $collectionFactory
    ) {
    
        $this->filter = $filter;
        $this->collectionFactory = $collectionFactory;
        parent::__construct($context);
    }

    /**
     * Excute Function
     *
     * @return \Magento\Backend\Model\View\Result\Redirect
     */
    public function execute()
    {
        $collection = $this->filter
                            ->getCollection(
                                $this->collectionFactory->create()
                            );
        $storeRecordDeleted = 0;
        foreach ($collection->getItems() as $shopifyStore) {
            $shopifyStore->setId($shopifyStore->getEntityId());
            $shopifyStore->delete();
            ++$storeRecordDeleted;
        }
        $this->messageManager->addSuccess(
            __("A total of %1 record(s) have been deleted.", $storeRecordDeleted)
        );

        return $this->resultFactory
                    ->create(ResultFactory::TYPE_REDIRECT)->setPath('*/*/index');
    }
}
