<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Woocommerceaccount;

use Magento\Framework\Controller\ResultFactory;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Woocommerceaccount;

class NewWcAccount extends Woocommerceaccount
{
    /**
     * Excute Function
     *
     * @return \Magento\Backend\Model\View\Result\Forward
     */
    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Forward $result */
        $result = $this->resultFactory->create(ResultFactory::TYPE_FORWARD);
        $result->forward('edit');
        return $result;
    }
}
