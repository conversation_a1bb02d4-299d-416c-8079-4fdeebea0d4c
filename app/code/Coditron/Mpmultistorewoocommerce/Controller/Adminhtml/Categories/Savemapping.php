<?php

/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Categories;

use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Categories;

class Savemapping extends Categories
{

    /**
     * @var Magento\Framework\Controller\Result\Json
     */
    private $resultJsonFactory;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Helper\Data
     */
    private $dataHelper;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\Woocommercecategorymap
     */
    private $woocommercecategorymap;

    /**
     * Construct function
     *
     * @param \Magento\Backend\App\Action\Context                             $context
     * @param \Coditron\Mpmultistorewoocommerce\Helper\Data              $dataHelper
     * @param \Coditron\Mpmultistorewoocommerce\Model\Woocommercecategorymap $woocommercecategorymap
     * @param JsonFactory                                                     $resultJsonFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $dataHelper,
        \Coditron\Mpmultistorewoocommerce\Model\Woocommercecategorymap $woocommercecategorymap,
        JsonFactory $resultJsonFactory
    ) {
        $this->dataHelper = $dataHelper;
        $this->woocommercecategorymap = $woocommercecategorymap;
        $this->resultJsonFactory = $resultJsonFactory;
        parent::__construct($context);
    }

    /**
     * Wwoo commerce account details
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
        $status = true;
        $mappingData = $this->getRequest()->getParams();
        $helper = $this->dataHelper;
        $leafWoocommerceCategory = $mappingData['woocommerceRootCate'];
        $leafMageCategory = $mappingData['mageLeafCate'];
        $msgResponse = $this->_getResponceMsgAsRequest(
            $leafWoocommerceCategory,
            $leafMageCategory,
            $helper,
            $mappingData['id']
        );
        $response = [];
        if ($msgResponse) {
            $status = false;
            $msg = $msgResponse;
        } else {
            try {
                $data = $helper->checkTheWoocommerceMappingCategoryExist($mappingData['id'], $leafWoocommerceCategory);
                if ($data['status'] == true) {
                     if ($data['data']['id'] == $leafWoocommerceCategory) {
                         /* save maped data for Shopify and store category**/
                         $mapData = [
                             'mage_cat_id' => $leafMageCategory,
                             'woocommerce_cat_id' => $leafWoocommerceCategory,
                             'woocommerce_cat_name' => $data['data']['name'],
                             'body_html' => $data['data']['description'],
                             'rule_id' => $mappingData['id']
                         ];
                         $mapWooCommerceMageCat = $this->woocommercecategorymap;
                         $mapWooCommerceMageCat->setData($mapData)->save();
                         $msg = __('Category has been successfully mapped with Woo commerce category');
                     } else {
                         $status = false;
                         $msg = __('Please first import category from Woo commerce to your store.');
                     }
                 } else {
                     $status = $data['status'];
                     $msg = $data['error_msg'];
             }
            } catch (\Exception $e) {
                $msg = $e->getMessage();
            }
        }
        $response['status'] = $status;
        $response['msg'] = $msg;
        return $this->resultJsonFactory->create()->setData($response);
    }

    /**
     * Get ResponceMsgAsRequest
     *
     * @param int $leafWoocommerceCategory
     * @param int $leafMageCategory
     * @param Webkul\Shopifymagentoconnect\Helper\Data $helper
     * @param int $ruleId
     * @return false|string
     */
    private function _getResponceMsgAsRequest(
        $leafWoocommerceCategory,
        $leafMageCategory,
        $helper,
        $ruleId
    ) {
        $msg = false;
        if ($leafWoocommerceCategory == false && $leafMageCategory == false) {
            $msg = __('Please select atleast one category.');
        } elseif (!$leafWoocommerceCategory) {
            $msg = __('Please select leaf category.');
        } elseif ($helper->isMageCategoryMapped($leafMageCategory, $ruleId)) {
            $msg = __('This Store category already mapped.');
        }
        return $msg;
    }
}
