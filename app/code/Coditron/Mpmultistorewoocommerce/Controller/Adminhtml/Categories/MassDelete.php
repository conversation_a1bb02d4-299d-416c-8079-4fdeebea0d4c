<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Categories;

use Magento\Framework\Controller\ResultFactory;
use Magento\Backend\App\Action\Context;
use Coditron\Mpmultistorewoocommerce\Api\WoocommercecategorymapRepositoryInterface;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Categories;

class MassDelete extends Categories
{
    /**
     * @var WoocommercecategorymapRepositoryInterface
     */
    private $woocommerceCategoryMapRepository;

    /**
     * Construct function
     *
     * @param Context                               $context
     * @param WoocommercecategorymapRepositoryInterface $woocommerceCategoryMapRepository
     */
    public function __construct(
        Context $context,
        WoocommercecategorymapRepositoryInterface $woocommerceCategoryMapRepository
    ) {
    
        $this->woocommerceCategoryMapRepository = $woocommerceCategoryMapRepository;
        parent::__construct($context);
    }

    /**
     * Function execute
     *
     * @return \Magento\Backend\Model\View\Result\Redirect
     */
    public function execute()
    {
        $params = $this->getRequest()->getParams();
        $cateEntityIds = $params['cateEntityIds'];
        if (!is_array($cateEntityIds)) {
            $cateEntityIds = explode(',', $cateEntityIds);
        }
        $collection = $this->woocommerceCategoryMapRepository
                ->getCollectionByIds($cateEntityIds);
        $catRecordDeleted = 0;
        foreach ($collection as $categoryMap) {
            $categoryMap->setId($categoryMap->getEntityId());
            $categoryMap->delete();
            ++$catRecordDeleted;
        }
        $this->messageManager->addSuccess(
            __("A total of %1 record(s) have been deleted.", $catRecordDeleted)
        );
        return $this->resultFactory->create(
            ResultFactory::TYPE_REDIRECT
        )->setPath(
            '*/woocommerceaccount/edit',
            [
                'id'=>$params['rule_id'],
                'active_tab' => 'mapcategory'
            ]
        );
    }
}
