<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Categories;

use Magento\Framework\Controller\Result\JsonFactory;
use Coditron\Mpmultistorewoocommerce\Api\WoocommercecategoryRepositoryInterface;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Categories;
use Magento\Framework\Controller\ResultFactory;

class Getwoocommercechildcategory extends Categories
{
    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    private $resultJsonFactory;

    /**
     * @var WoocommercecategoryRepositoryInterface
     */
    private $woocommerceCategoryRepository;

    /**
     * Construct function
     *
     * @param \Magento\Backend\App\Action\Context   $context
     * @param JsonFactory                           $resultJsonFactory
     * @param WoocommercecategoryRepositoryInterface    $woocommerceCategoryRepository
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        JsonFactory $resultJsonFactory,
        WoocommercecategoryRepositoryInterface $woocommerceCategoryRepository
    ) {
    
        parent::__construct($context);
        $this->resultJsonFactory = $resultJsonFactory;
        $this->woocommerceCategoryRepository = $woocommerceCategoryRepository;
    }

    /**
     * Function execute
     *
     * @return \Magento\Framework\Controller\Result\Json
     */
    public function execute()
    {
        $data = $this->getRequest()->getPostValue();
        if (!$data) {
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
                ->setPath('mpmultistorewoocommerce/*/');
        }
        $childcategory = $this->woocommerceCategoryRepository
                        ->getCollectionByWoocommerceCateParentId($data['cat_id']);
        return $this->resultJsonFactory->create()->setData($childcategory->toArray());
    }
}
