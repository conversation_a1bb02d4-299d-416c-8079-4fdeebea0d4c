<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Categories;

use Magento\Framework\Controller\Result\JsonFactory;
use Coditron\Mpmultistorewoocommerce\Controller\Adminhtml\Categories;
use Magento\Framework\Controller\ResultFactory;

class Getmagechildcategory extends Categories
{
    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    private $resultJsonFactory;

    /**
     * @var \Magento\Catalog\Model\CategoryFactory
     */
    private $categoryFactory;

    /**
     * ValidateTest constructor.
     *
     * @param Action\Context $context
     * @param JsonFactory $resultJsonFactory
     * @param \Magento\Catalog\Model\CategoryFactory $categoryFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        JsonFactory $resultJsonFactory,
        \Magento\Catalog\Model\CategoryFactory $categoryFactory
    ) {
    
        parent::__construct($context);
        $this->resultJsonFactory = $resultJsonFactory;
        $this->categoryFactory = $categoryFactory;
    }

    /**
     * Function execute
     *
     * @return \Magento\Framework\Controller\Result\Json
     */
    public function execute()
    {
        $data = $this->getRequest()->getPostValue();
        if (!$data) {
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
            ->setPath('mpmultistorewoocommerce/*/');
        }
        $childcategoryObj = $this->categoryFactory->create();
        $childcategory = $childcategoryObj->getCollection()->addFieldToFilter(
            'parent_id',
            ['eq' => $data['cat_id']]
        )->load();
        $categoryList = [];
        foreach ($childcategory as $category) {
            $category = $this->categoryFactory->create()->load($category->getEntityId());
            $categoryList[] = ['value' => $category->getEntityId(),'lable' => $category->getName(),];
        }
        $categoryList = ['totalRecords' => count($childcategory),'items' => $categoryList,];
        return $this->resultJsonFactory->create()->setData($categoryList);
    }
}
