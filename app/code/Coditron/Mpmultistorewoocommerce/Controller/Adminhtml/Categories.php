<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;

abstract class Categories extends Action
{

    /**
     * @inheritdoc
     */
    protected function _isAllowed()
    {
        return $this->_authorization
                    ->isAllowed(
                        'Coditron_Mpmultistorewoocommerce::Categories'
                    );
    }
}
