<?php
/**
 * Language translation Record Index Controller.
 * @category  Coditron
 * @package   Coditron_
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license    https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Controller\Adminhtml;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;

abstract class Woocommerceaccount extends Action
{
    
    /**
     * @inheritdoc
     */
    protected function _isAllowed()
    {
        return $this->_authorization
                    ->isAllowed(
                        'Coditron_Mpmultistorewoocommerce::WooCommerceAccount'
                    );
    }
}
