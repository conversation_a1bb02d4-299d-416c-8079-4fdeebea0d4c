<?php
$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
$_helper = $objectManager->create(\Webkul\Marketplace\Helper\Data::class);
$isPartner= $_helper->isSeller();
$urlInterface = $objectManager->get(Magento\Framework\UrlInterface::class);
$magentoCurrentUrl = $urlInterface->getCurrentUrl();
$isSellerGroup = $_helper->isSellerGroupModuleInstalled();
$WooCommerceyHelper = $objectManager->create(\Coditron\Mpmultistorewoocommerce\Helper\FData::class);
$sellerDetails = $WooCommerceyHelper->getAccountDetailsBySellerId();
$showAddit = false;
if ($sellerDetails->getSize()>0) {
    $showAddit = true;
}
$pickupEnable = true;
$accConnectUrl = $block->getUrl(
    'mpmultistorewoocommerce/account/connect',
    ['_secure' => $block->getRequest()->isSecure()]
);
$mapCategory = $block->getUrl(
    'mpmultistorewoocommerce/map/category',
    ['_secure' => $block->getRequest()->isSecure()]
);
$mapProduct = $block->getUrl(
    'mpmultistorewoocommerce/map/product',
    ['_secure' => $block->getRequest()->isSecure()]
);
$mapOrder = $block->getUrl(
    'mpmultistorewoocommerce/map/orders',
    ['_secure' => $block->getRequest()->isSecure()]
);
$exportProduct = $block->getUrl(
    'mpmultistorewoocommerce/export/products',
    ['_secure' => $block->getRequest()->isSecure()]
);
$templateView = $block->getUrl(
    'mpmultistorewoocommerce/templates/view',
    ['_secure' => $block->getRequest()->isSecure()]
);
$priceRule = $block->getUrl(
    'mpmultistorewoocommerce/pricerule/view',
    ['_secure' => $block->getRequest()->isSecure()]
);
?>
<style type="text/css">
    @media only screen and (max-width: 767px){

        .block-collapsible-nav.wk-mp-main{
            top: 20px;
        }
    }
</style>
<?php if ($pickupEnable && $isPartner) { ?>
    <?php if (($isSellerGroup && $_helper->isAllowedAction('mpmultistorewoocommerce/account/connect'))
                || !$isSellerGroup) { ?>
    <li 
class="level-0 <?=/* @noEscape */ strpos($magentoCurrentUrl, 'mpmultistorewoocommerce')? "current active":"";?>">
        <a href="#" onclick="return false;" class="">
            <span><?=/* @noEscape */ __('WooCommerce Connect')?></span>
        </a>
        <div class="wk-mp-submenu">
            <strong class="wk-mp-submenu-title"><?=/* @noEscape */ __('WooCommerce Connect')?></strong>
            <a href="#" class="action-close _close" data-role="wk-mp-close-submenu"></a>
            <ul>
                <li data-ui-id="menu-webkul-marketplace-menu" class="item-menu  parent  level-1">
                    <strong class="wk-mp-submenu-group-title">
                        <span><?=/* @noEscape */ __('Menu')?></span>
                    </strong>
                    <div class="wk-mp-submenu">
                        <ul>
                            <?php if (($isSellerGroup && $_helper->
                            isAllowedAction('mpmultistorewoocommerce/account/connect'))
                              || !$isSellerGroup) { ?>
                                  <?php if ($pickupEnable) {?>
                                <li class="level-2">
                                    <a href="<?=/* @noEscape */ $accConnectUrl ?>">
                                        <span><?=/* @noEscape */ __('Connect Account') ?></span>
                                    </a>
                                </li>
                            <?php } ?>
                            <?php } ?>
                            <?php if (($isSellerGroup && $_helper->
                            isAllowedAction('mpmultistorewoocommerce/map/category'))
                            || !$isSellerGroup) { ?>
                            <?php if ($showAddit) {?>
                                <li class="level-2">
                                    <a href="<?=/* @noEscape */ $mapCategory ?>">
                                        <span><?=/* @noEscape */ __('Map Categories') ?></span>
                                    </a>
                                </li>
                            <?php } ?>
                            <?php } ?>
                            <?php if (($isSellerGroup && $_helper->
                            isAllowedAction('mpmultistorewoocommerce/map/product'))
                            || !$isSellerGroup) { ?>
                            <?php if ($showAddit) { ?>
                                <li class="level-2">
                                    <a href="<?=/* @noEscape */ $mapProduct ?>">
                                        <span><?=/* @noEscape */ __('Map Products') ?></span>
                                    </a>
                                </li>
                            <?php } ?>
                            <?php } ?>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </li>
<?php } ?>
<?php } ?>