<?php

$id = $block->FhelperData->getAccountDetails()["entity_id"];
$mappedCates = $block->getMappedCategories();
$releventData = [
    'imageImportUrl' => $block->getUrl('*/products/imageprofiler'),
    'importProductSelector' => '#wc-import-product',
    'importAjaxUrl' => $block->getUrl('*/products/import'),
    'profilerSelector' => '#wc-run-profiler',
    'profilerAjaxUrl' => $block->getUrl('*/products/profiler', ['id'=>$id]),
    'isAllProImport' => $mappedCates['isAllProImport'],
    'cates' => $mappedCates['cates'],
    'entityId' => $id
];
$serializedData = json_encode($releventData);
?>
<script type="text/x-magento-init">
    {
        "*":{
            "Coditron_Mpmultistorewoocommerce/js/map/import-product":<?=/* @noEscape */ $serializedData ?>
        }
    }
</script>
<button id = "wc-import-product" class="wk_woocommerce_button"><?=/* @noEscape */ __('Import') ?></button>
<button id = "wc-run-profiler" class="wk_woocommerce_button"><?=/* @noEscape */ __('Run Profiler') ?></button>
<button id = "wc_run_image_profiler" class="wk_run_image_profiler"><?=/* @noEscape */ __('Import Images') ?></button>
