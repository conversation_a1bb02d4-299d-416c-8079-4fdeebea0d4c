<?php
$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
$WooCommerceHelper = $objectManager->create(\Coditron\Mpmultistorewoocommerce\Helper\FData::class);
$data = $WooCommerceHelper->getAccountDetails();

$magentoCats = [];
$attributeCats = $block->listCategories();

if ($attributeCats) {
    foreach ($attributeCats as $cats) {
        $magentoCats[] = $cats;
    }
}
$WooCat = $block->helper->getCategData($data["entity_id"]);

?>
<form method="post" enctype="multipart/form-data" data-mage-init='{"validation":{}}' id="wk_ssp_add_form"
    action="<?= /* @noEscape */ $block->getUrl('mpmultistorewoocommerce/map/savecategories'); ?>">
    <fieldset class="fieldset">
        <legend class="legend wk_ssp_set_width">
            <span><?= /* @noEscape */ __("Add Category Mapping"); ?></span>
            <button class="button wk-mp-btn" type="submit" id="wk_ssp_save_button">
                <span><span><?= /* @noEscape */ __("Save Mapping"); ?></span></span>
            </button>
        </legend>
        <div class="field required wk_categories_woocommerce">
            <label for="mageLeafCate" class="label">
                <span><?= /* @noEscape */ __("Magento Category") ?></span>
            </label>
            <div class="control">
                <select id="mageLeafCate" name="mageLeafCate" data-validate="{required:true}" aria-required="true"
                    class="input-text required-entry" title="<?= /* @noEscape */ __("Magento Category"); ?>">
                    <?php foreach ($magentoCats as $cats) { ?>
                        <option value='<?=/* @noEscape */ $cats["value"]?>'><?=/* @noEscape */ $cats["label"]?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
        <div class="field required wk_categories_woocommerce">
            <label for="woocommerceLeafCate" class="label">
                <span><?= /* @noEscape */ __("WooCommerce Category"); ?></span>
            </label>
            <div class="control">
                <select id="woocommerceLeafCate" name="woocommerceLeafCate" data-validate="{required:true}"
                class="input-text required-entry" title="woocommerce Category" aria-required="true">
                    <?php foreach ($WooCat as $cats) { ?>
                        <option value='<?=/* @noEscape */ $cats["value"]?>'><?=/* @noEscape */ $cats["label"]?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
        <input name="front" value='1' type="hidden"/>
        <input name="id" value="<?=/* @noEscape */ $data["entity_id"] ?>" type="hidden"/>
    </fieldset>
</form>
<hr>
<br>
<h3><p><?= /* @noEscape */ __("Manage Category Mapping List") ?></p></h3>
