<?php
/**
 * @category   Webkul
 * @package    Webkul_MpMultiShopifyStoreMageConnect
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
$productCount = $block->getImportedProduct();
$ruleId = $block->getRequest()->getParam('id');
$imageUrl  = $block->getViewFileUrl('Webkul_MpMultiShopifyStoreMageConnect::images/loader.gif');
?>
<fieldset class="fieldset">
    <div class="wk-mu-note wk-mu-box">
        <?=/* @noEscape */ __('Starting Execution'); ?>...
    </div>
    <div class="wk-mu-notice wk-mu-box">
        <?=/* @noEscape */ __("Please don't close or refresh the window while importing product(s)."); ?>
    </div>
    <div class="wk-mu-success wk-mu-box">
        <?=/* @noEscape */ __('Total '); ?>
        <?=/* @noEscape */ $productCount; ?>
        <?=/* @noEscape */ __(' product(s) to import'); ?>.
    </div>
    <?php if ($productCount > 0): ?>
        <div class="wk-mu-info-bar">
            <?php __('Importing '); ?> <span class="wk-current">1</span> of <?=/* @noEscape */ $productCount;?>
            <img 
                id="loader-image" 
                src="<?=/* @noEscape */ $imageUrl ?>" 
            />
        </div>
        <div class="wk-mu-progress-bar">
            <div class="wk-mu-progress-bar-current"></div>
        </div>
        <div class="wk-mu-error-msg-container">
        </div>
    <?php else: ?>
        <div class="wk-mu-note wk-mu-box">
            <?=/* @noEscape */ __('No product to import.'); ?>
        </div>
        <div class="wk-mu-note wk-mu-box">
            <?=/* @noEscape */ __('Finsihed Execution.'); ?>
        </div>
    <?php endif; ?>
</fieldset>
<?php
    $viewModel = $block->getViewModel();
    $releventData = [
        'importUrl' => $block->getUrl('mpmultishopifystoremageconnect/products/createproduct'),
        'fieldsetSelector'    => '.fieldset',
        'progressBarSelector'    => '.wk-mu-progress-bar-current',
        'infoBarSelector'    => '.wk-mu-info-bar',
        'currentSelector'    => '.wk-current',
        'productCount'        => $productCount,
        'ruleId'            => $ruleId
    ];

    $serializedData = json_encode($releventData);

    ?>
<script type="text/x-magento-init">
    {
        "*":{
            "Webkul_MpMultiShopifyStoreMageConnect/js/map/product-profiler-plugin":<?=/* @noEscape */ $serializedData ?>
        }
    }
</script>
