<?php
$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
$mpHelper = $objectManager->create(\Webkul\Marketplace\Helper\Data::class);

$isPartner= $mpHelper->isSeller();
$magentoCurrentUrl = $block->getCurrentUrl();
$isSellerGroup = $mpHelper->isSellerGroupModuleInstalled();
$WooCommerceyHelper = $objectManager->create(\Coditron\Mpmultistorewoocommerce\Helper\FData::class);
$sellerDetails = $WooCommerceyHelper->getAccountDetailsBySellerId();
$showAddit = false;
if ($sellerDetails->getSize()>0) {
    $showAddit = true;
}
?>
<?php
if ($isPartner) {?>
        <?php if (($isSellerGroup && $mpHelper->isAllowedAction('mpmultistorewoocommerce/account/connect'))
                || !$isSellerGroup) { ?>
        <li class="mpshipping nav item <?= /* @noEscape */ strpos($magentoCurrentUrl, 'mpshipping')? "current":"";?>">
            <a href="#">
                <?= /* @noEscape */ __('WooCommerce Connect') ?>
            </a>

            <ul class="nav items">
                <?php if (($isSellerGroup && $mpHelper->
                isAllowedAction('mpmultistorewoocommerce/account/connect'))
                || !$isSellerGroup) { ?>
                <li class="nav item 
                <?= strpos($magentoCurrentUrl, 'mpshipping/shipping/view') !== false ? "current":"";?>">
                    <a href="<?= /* @noEscape */ $block->getUrl('mpmultistorewoocommerce/account/connect'); ?>">
                        <?= /* @noEscape */ __('Account Connect') ?>
                    </a>
                </li>
                <?php } ?>
                <?php if ($showAddit) { ?>
                    <?php if (($isSellerGroup &&
                    $mpHelper->isAllowedAction('mpmultistorewoocommerce/map/category'))
                    || !$isSellerGroup) { ?>
                <li class="nav item 
                    <?= strpos($magentoCurrentUrl, 'mpshipping/shippingset/view') !== false ? "current":"";?>">
                    <a href="<?= /* @noEscape */ $block->getUrl('mpmultistorewoocommerce/map/category'); ?>">
                        <?= /* @noEscape */ __('Map Categories') ?>
                    </a>
                </li>
                <?php } ?>
                <?php } ?>
                <?php if ($showAddit) { ?>
                    <?php if (($isSellerGroup &&
                    $mpHelper->isAllowedAction('mpmultistorewoocommerce/map/product'))
                    || !$isSellerGroup) { ?>
                <li class="nav item 
                    <?= strpos($magentoCurrentUrl, 'mpshipping/shippingset/view') !== false ? "current":"";?>">
                    <a href="<?= /* @noEscape */ $block->getUrl('mpmultistorewoocommerce/map/product'); ?>">
                        <?= /* @noEscape */ __('Map Products') ?>
                    </a>
                </li>
                <?php } ?>
                <?php } ?>                
            </ul>
        </li>
    <?php
    }
}
?>