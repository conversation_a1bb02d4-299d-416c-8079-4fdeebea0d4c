<?php

$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
$WooCommerceHelper = $objectManager->create(\Coditron\Mpmultistorewoocommerce\Helper\FData::class);
$data = $WooCommerceHelper->getAccountDetails();
$sellerId = $WooCommerceHelper->getSellerId();

$attributeSetList = $block->listAttributeSet();
$attributeSets = [];
if ($attributeSetList) {
    foreach ($attributeSetList->getItems() as $list) {
        $attributeSets[] = $list->getData();
    }
}
$categorySets = [];
$attributeCats = $block->listCategories();
if ($attributeCats) {
    foreach ($attributeCats as $cats) {
        $categorySets[] = $cats;
    }
}
$storeViews = [];
$stores = $block->getStores();
foreach ($stores as $store) {
    if ($store["code"] != "admin") {
        $storeViews[] = $store->getData();
    }
}
$impOptionTypes = $block->optionTypes();
$priceRuleOptions = $block->priceRuleOptions();
$orderStatus = $block->orderStatus();
$baseCurrCode = $block->baseCurrCode();
$qtyHint = '<small>' . __('(Default qty will be used in case qty is 0 while export)') . '</small>';
$productTypes = $block->getProductOptions();

$templates = $block->getTemplates();
?>
<form method="post" action="<?= /* @noEscape */ $block->getUrl('mpmultistorewoocommerce/account/save'); ?>" enctype="multipart/form-data" data-mage-init='{"validation":{}}' id="wk_ssp_add_form">
    <fieldset class="fieldset">
        <legend class="legend wk_ssp_set_width">
            <span><?= /* @noEscape */ __("Add Account"); ?></span>
            <button class="button wk-mp-btn" type="submit" id="wk_ssp_save_button">
                <span><span><?= /* @noEscape */ __("Save Account"); ?></span></span>
            </button>
        </legend>
        <div class="field required">
            <label for="store_name" class="label">
                <span><?= /* @noEscape */ __("Store Name"); ?></span>
            </label>
            <div class="control">
                <input type="text" data-validate="{required:true}" class="input-text required-entry" title="Store Name" name="store_name" id="store_name" aria-required="true" value="<?= /* @noEscape */ $data['store_name']; ?>" <?php if ($data["store_name"] != "") { echo "disabled"; } ?>>
            </div>
        </div>
        <div class="seller_id">
            <input type="hidden" name="seller_id" value="<?php /* @escapeNotVerified */ echo $WooCommerceHelper->getSellerId(); ?>" >
        </div>
        <div class="field required">
            <label for="attribute_set_id" class="label">
                <span><?= /* @noEscape */ __("Attribute Set"); ?></span>
            </label>
            <div class="control">
                <select id="attribute_set_id" name="attribute_set_id" data-validate="{required:true}" class="input-text required-entry" title="Attribute Set" aria-required="true">
                    <?php foreach ($attributeSets as $attributeSet) { ?>
                        <option <?php if ($data["attribute_set_id"] == $attributeSet["attribute_set_id"]) { echo "selected"; } ?> value='<?= /* @noEscape */ $attributeSet["attribute_set_id"] ?>'>
                            <?= /* @noEscape */ $attributeSet["attribute_set_name"] ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
        </div>

        <div class="field required">
            <label for="woocommerce_consumer_key" class="label">
                <span><?= /* @noEscape */ __("Consumer Key"); ?></span>
            </label>
            <div class="control">
                <input id="woocommerce_consumer_key" name="woocommerce_consumer_key" data-validate="{required:true}" value='<?= /* @noEscape */ $data["woocommerce_consumer_key"] ?>' title="Consumer Key" type="password" class="input-text required-entry" aria-required="true">
            </div>
        </div>
        <div class="field required">
            <label for="woocommerce_consumer_secret_key" class="label">
                <span><?= /* @noEscape */ __("Consumer Secret Key"); ?></span>
            </label>
            <div class="control">
                <input id="woocommerce_consumer_secret_key" name="woocommerce_consumer_secret_key" data-validate="{required:true}" value='<?= /* @noEscape */ $data["woocommerce_consumer_secret_key"] ?>' title="Consumer Secret Key" type="password" class="input-text required-entry" aria-required="true">
            </div>
        </div>
        <div class="field required">
            <label for="woocommerce_url" class="label">
                <span><?= /* @noEscape */ __("Store URL"); ?></span>
            </label>
            <div class="control">
                <input type="text" data-validate="{required:true}" class="input-text required-entry" title="Store URL" name="woocommerce_url" id="woocommerce_url" aria-required="true" value="<?= /* @noEscape */ $data['woocommerce_url']; ?>">
            </div>
        </div>
        <div class="field required">
            <label for="default_cate" class="label">
                <span><?= /* @noEscape */ __("Default Category"); ?></span>
            </label>
            <div class="control">
                <select id="default_cate" name="default_cate" data-validate="{required:true}" class="input-text required-entry" title="Default Category" aria-required="true">
                    <?php foreach ($attributeCats as $attributeCat) { ?>
                        <option <?php if ($data["default_cate"] == $attributeCat["value"]) { echo "selected"; } ?> value='<?= /* @noEscape */ $attributeCat["value"] ?>'>
                            <?= /* @noEscape */ $attributeCat["label"] ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
        </div>
        <div class="field required">
            <label for="default_store_view" class="label">
                <span><?= /* @noEscape */ __("Default Store Views"); ?></span>
            </label>
            <div class="control">
                <select id="default_store_view" name="default_store_view" data-validate="{required:true}" class="input-text required-entry" title="Default Store Views" aria-required="true">
                    <?php foreach ($storeViews as $storeView) { ?>
                        <option <?php if ($data["default_store_view"] == $storeView["store_id"]) { echo "selected"; } ?> value='<?= /* @noEscape */ $storeView["store_id"] ?>'>
                            <?= /* @noEscape */ $storeView["name"] ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
        </div>
        <div class="field required">
            <label for="order_status" class="label">
                <span><?= /* @noEscape */ __("Order Status"); ?></span>
            </label>
            <div class="control">
                <select id="order_status" name="order_status" data-validate="{required:true}" class="input-text required-entry" title="Order Status" aria-required="true">
                    <?php foreach ($orderStatus as $options) { ?>
                        <option <?php if ($data["order_status"] == $options["value"]) { echo "selected"; } ?> value='<?= /* @noEscape */ $options["value"] ?>'>
                            <?= /* @noEscape */ $options["label"] ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
        </div>
        <?php if (array_key_exists("entity_id", $data)) { ?>
            <input name="id" value='<?= /* @noEscape */ $data["entity_id"] ?>' type="hidden" />
        <?php } ?>
        <input name="front" value='1' type="hidden" />
    </fieldset>
</form>
<script>
    require(['jquery'], function ($) {
        $("#woocommerce_app_select").on('change', function() {
            var woocommerce_app_select = this.value;

                $("#woocommerce_custom_app").removeClass("field required");
                $("#woocommerce_custom_app").empty();
                $("#woocommerce_private_app_key").addClass("field required");
                $("#woocommerce_private_app_pwd").addClass("field required");
                $("#woocommerce_private_app_key").empty();
                $("#woocommerce_private_app_key").append('<label for="woocommerce_consumer_key" class="label"><span>'
                    + "<?= /* @noEscape */ __('Consumer Secret Key'); ?>"
                    + '</span></label><div class="control"><input id="woocommerce_consumer_key" name="woocommerce_consumer_key"'
                    + 'data-validate="{required:true}" value="'
                    + "<?= /* @noEscape */ $data['woocommerce_consumer_key'] ?>"
                    + '" title="API Key" type="password" class="input-text required-entry" aria-required="true"></div>');
                $("#woocommerce_private_app_pwd").empty();
                $("#woocommerce_private_app_pwd").append('<label for="woocommerce_consumer_secret_key" class="label"><span>'
                    + "<?= /* @noEscape */ __('Password'); ?>"
                    + '</span></label><div class="control"><input id="woocommerce_consumer_secret_key" name="woocommerce_consumer_secret_key"'
                    + 'data-validate="{required:true}" value="'
                    + "<?= /* @noEscape */ $data['woocommerce_consumer_secret_key'] ?>"
                    + '" title="Password" type="password" class="input-text required-entry"'
                    + 'aria-required="true"></div>');
            
        });
    });
</script>
