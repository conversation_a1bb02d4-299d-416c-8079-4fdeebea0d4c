<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
        <head>
        <css src="Webkul_Marketplace::css/wk_block.css"/>
        <css src="Webkul_Marketplace/css/style.css"/>
        <css src="Webkul_Marketplace/css/product.css"/>
        <css src="Webkul_Marketplace/css/layout.css"/>
        <css src="Webkul_MpMultiShopifyStoreMageConnect/css/styles.css"/>
    </head>
    <body>
        <referenceContainer name="content">            
            <block class="Coditron\Mpmultistorewoocommerce\Block\Account\Connect" 
            name="mpmultistorewoocommerce_account_details_connect" 
            template="Coditron_Mpmultistorewoocommerce::account/connect.phtml" cacheable="false"/>
        </referenceContainer>
    </body>
</page>
