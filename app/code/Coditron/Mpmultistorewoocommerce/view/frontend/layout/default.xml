<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	   <head>
        <css src="Coditron_Mpmultistorewoocommerce::css/styles.css"/>
    </head>
	<body>
		<referenceContainer name="layout2_seller_account_navigation">
            <block name="mpwoocommerce_connect_menu" ifconfig="mpmultistorewoocommerce/general_settings/enable" before="-" template="Coditron_Mpmultistorewoocommerce::layout2/account/navigation.phtml"/>
        </referenceContainer> 
	</body> 
</page>
