<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="Webkul_Marketplace::css/wk_block.css"/>
        <css src="Webkul_Marketplace/css/style.css"/>
        <css src="Webkul_Marketplace/css/product.css"/>
        <css src="Webkul_Marketplace/css/layout.css"/>
        <css src="Webkul_MpMultiShopifyStoreMageConnect/css/styles.css"/>
    </head>
    <body>
        <referenceBlock name="seller.page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Marketplace WooCommerce Map Product</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="seller.content">
            <block class="Coditron\Mpmultistorewoocommerce\Block\Map\Product\Profiler" name="mpmultistorewoocommerce_account_map_products" 
            template="Coditron_Mpmultistorewoocommerce::map/product.phtml" cacheable="false"/>
            <uiComponent name="wc_map_products_list"/>
        </referenceContainer>
    </body>
</page>
