<?xml version="1.0"?>
<page layout="checkout" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<head>
		<css src="Webkul_MpMultiShopifyStoreMageConnect::css/styles.css"/>
	</head>
	<body>
		<referenceContainer name="content">
			<block class="Coditron\Mpmultistorewoocommerce\Block\Map\Image\Profiler" name="image_profiler_run"
            	template="Coditron_Mpmultistorewoocommerce::image/profiler.phtml"/>
		</referenceContainer>
	</body>
</page>
