<?xml version="1.0"?>
<!-- 
/**
 * @category   Webkul
 * @package    Webkul_MpMultiShopifyStoreMageConnect
 * <AUTHOR> Software Private Limited
  * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */  -->
<page layout="checkout" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<head>
		<css src="Webkul_MpMultiShopifyStoreMageConnect::css/styles.css"/>
	</head>
	<body>
		<referenceContainer name="content">
			<block class="Coditron\Mpmultistorewoocommerce\Block\Map\Product\Profiler" name="profiler_run" 
            template="Coditron_Mpmultistorewoocommerce::product/profiler.phtml" cacheable="false"/>
		</referenceContainer>
	</body>
</page>