<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd" design_abstraction="custom">
	<head>
		<css src="Coditron_Mpmultistorewoocommerce::css/styles.css"/>
	</head>
	<body>
		<referenceContainer name="seller_account_navigation">
			<block class="Webkul\Marketplace\Block\Account\Navigation" ifconfig="mpmultistorewoocommerce/general_settings/enable" name="seller_mpWoocommerce_navigation" 
			template="Coditron_Mpmultistorewoocommerce::account/navigation.phtml">
			</block>
		</referenceContainer>
	</body>
</page>
