<?xml version="1.0"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpMultiShopifyStoreMageConnect
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="Webkul_Marketplace::css/wk_block.css"/>
        <css src="Webkul_Marketplace/css/style.css"/>
        <css src="Webkul_Marketplace/css/product.css"/>
        <css src="Webkul_Marketplace/css/layout.css"/>
    </head>
    <body>
        <referenceBlock name="seller.page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Marketplace WooCommerce Account Connect</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="seller.content">
            <block class="Coditron\Mpmultistorewoocommerce\Block\Account\Connect" 
            name="mpwoocommerce_account_details_connect" 
            template="Coditron_Mpmultistorewoocommerce::account/connect.phtml" cacheable="false"/>
        </referenceContainer>
    </body>
</page>
