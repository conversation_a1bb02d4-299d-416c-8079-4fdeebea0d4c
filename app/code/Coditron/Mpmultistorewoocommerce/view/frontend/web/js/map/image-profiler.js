/**
 * @category   Webkul
 * @package    Webkul_MpMultiShopifyStoreMageConnect
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
/*jshint jquery:true*/
define([
    'jquery',
    'mage/translate',
    "mage/loader",
    "Magento_Ui/js/modal/alert"
], function ($, $t, loader, alert) {
    'use strict';
    var self, ruleId;
    var total = 0;
    var skipCount = 0, count = 0;
    $.widget('mage.shopifyImageImport', {
        _create: function () {
            self = this;
            skipCount = 0;
            total = self.options.imageCount;
            ruleId = self.options.ruleId;
            if (total > 0) {
                self.importProductImage(1, 0);
            }
        },
        importProductImage: function (count, skipCount)
        {
            $.ajax({
                type: 'POST',
                url: self.options.imageImportUrl,
                async: true,
                dataType: 'json',
                data : {
                    'form_key':window.FORM_KEY,
                    'count':count,
                    'skip':skipCount,
                    'ruleId':self.options.ruleId
                },
                success:function(data) {
                    if(data['error'] == 1) {
                        $(".product_create_error_msg").prepend($('<div>')
                            .addClass('message message-error error')
                            .text(data['msg'])
                        );
                        skipCount++;
                    }
                    var width = (100/total)*count;
                    $(".wk-mu-progress-bar-current").animate({width: width+"%"},'slow', function() {
                        if(count >= total) {
                            $(".wk-mu-info-bar").text($.mage.__('Completed'));
                            $(".fieldset").append(data['msg']);
                        } else {
                            count++;
                            $(".wk-current").text(count);
                            self.importProductImage(count, skipCount);
                        }
                    });
                }
            });
        }
    });
    return $.mage.shopifyImageImport;
});
