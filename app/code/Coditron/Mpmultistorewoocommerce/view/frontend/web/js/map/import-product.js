/**
 * @category   Webkul
 * @package Webkul_MpMultiShopifyStoreMageConnect
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
/*jshint jquery:true*/
define([
    'jquery',
    'mage/translate',
    'Magento_Ui/js/modal/alert'
], function ($,$t,alert) {
    'use strict';
    var id,popup;var flag = 1;
    var requests = [], page=1;var totalImportedProduct=0;
    var index = 0;var mappedCates = [];
    $.widget('mage.productImportScript', {
        _create: function () {
            var self = this;
            var errorHtml = '';
            mappedCates = self.options.cates.split(',');
            $(this.options.importProductSelector).click(function (e) {
                id = self.options.entityId;
                var subcat=$(this);
                e.preventDefault();
                // import Shopify all product. when import all product is selected in the configuration.
                if (parseInt(self.options.isAllProImport)) {
                    $.ajax({
                        url: self.options.importAjaxUrl,
                        data: {form_key: window.FORM_KEY, page : page, 'id' : id},
                        type: 'POST',
                        dataType:'JSON',
                        beforeSend:function () {
                            $("body").trigger('processStart');
                        },
                        complete:function () {
                            requestNext(errorHtml);
                        },
                        success: function (shopifyPro) {
                            if (shopifyPro.error_msg) {
                                flag = 0;
                                $("body").trigger('processStop');
                                $('body').find('.loading-mask').hide();
                                $('<div>').html(shopifyPro.error_msg)
                                .modal({
                                    title: $.mage.__('Attention'),
                                    autoOpen: true,
                                    buttons: [{
                                    text: 'OK',
                                        attr: {
                                            'data-action': 'cancel'
                                        },
                                        'class': 'action-primary',
                                        click: function () {
                                            this.closeModal();
                                            // location.reload(false);
                                        }
                                    }]
                                });
                            } else {
                                shopifyPro.notImportedProduct.forEach(function(element) {
                                    errorHtml = errorHtml + '<div style="color:red;">'+element+'</div>';
                                }, this);
                                totalImportedProduct = parseInt(totalImportedProduct) + parseInt(shopifyPro.data);
                            }
                        },
                        error: function (error) {
                            $("body").trigger('processStop');
                            console.log(error);
                        }
                    }).done(function () {
                        $('#loader-image').removeClass('show');
                    });
                } else {
                    //import shopify product category wise.
                    if (mappedCates.length >= 1 && mappedCates[0]!="") {
                        ajaxCall(index);
                    } else {
                        $('<div>').html('You have not mapped any WooCommerce category.')
                        .modal({
                            title: $.mage.__('Attention'),
                            autoOpen: true,
                            buttons: [{
                             text: 'OK',
                                attr: {
                                    'data-action': 'cancel'
                                },
                                'class': 'action-primary',
                                click: function () {
                                        this.closeModal();
                                        location.reload(false);
                                }
                            }]
                        });
                    }
                }
            });
            var ajaxCall = function (index) {
                if (index < mappedCates.length) {
                    $.ajax({
                        url: self.options.importAjaxUrl,
                        data: {
                            form_key: window.FORM_KEY,
                            page : page,
                            'cate_id' :mappedCates[index],
                            'id' : id
                        },
                        type: 'POST',
                        dataType:'JSON',
                        showLoader : true,
                        beforeSend:function () {
                            $("body").trigger('processStart');
                        },
                        success: function (shopifyPro) {
                            if (shopifyPro.error_msg) {
                                $("body").trigger('processStop');
                                $('body').find('.loading-mask').hide();
                                $('<div>').html(shopifyPro.error_msg)
                                .modal({
                                    title: $.mage.__('Attention'),
                                    autoOpen: true,
                                    buttons: [{
                                     text: 'OK',
                                        attr: {
                                            'data-action': 'cancel'
                                        },
                                        'class': 'action-primary',
                                        click: function () {
                                                this.closeModal();
                                                location.reload(false);
                                        }
                                    }]
                                });
                            } else {
                                shopifyPro.notImportedProduct.forEach(function(element) {
                                    errorHtml = errorHtml + '<div style="color:red;">'+element+'</div>';
                                }, this);
                                totalImportedProduct = parseInt(totalImportedProduct) + parseInt(shopifyPro.data);
                                index++;
                                ajaxCall(index);
                            }
                        },
                        error: function (error) {
                            console.log(error);
                        }
                    }).done(function () {
                        $('#loader-image').removeClass('show');
                    });
                } else {
                    requestNext(errorHtml);
                }
            }

            var requestNext = function (errorHtml = "") {
                if (requests.length) {
                    $("body").trigger('processStart');
                    $.ajax(requests.shift()).then(requestNext);
                } else {
                    $("body").trigger('processStop');
                    $('.popup-inner').find('.count').remove();
                    $('.loading-mask').hide();
                    if (flag) {
                        if (parseInt(totalImportedProduct)) {
                            var msg='Total '+totalImportedProduct +' products imported in your store from Shopify run products profiler for create these products in your store';
                            msg=msg+errorHtml;
                        } else {
                            if (parseInt(totalImportedProduct) == 0)
                            var msg='Total '+totalImportedProduct +' products imported in your store from Shopify.';
                            msg=msg+errorHtml;
                        }

                        $('<div>').html(msg)
                        .modal({
                            title: $.mage.__('Attention'),
                            autoOpen: true,
                            buttons: [{
                             text: 'OK',
                                attr: {
                                    'data-action': 'cancel'
                                },
                                'class': 'action-primary',
                                click: function () {
                                    this.closeModal();
                                    // location.reload(false);
                                }
                            }]
                        });
                    }
                }
            };
            $('#wk_run_image_profiler').click(function (e) {
                self.openInPopUp(self.options.imageImportUrl);
            });
            $(self.options.profilerSelector).click(function (e) {
                self.openInPopUp(self.options.profilerAjaxUrl);
            });
        },
        afterChildClose:function () {
            if (popup.location != "about:blank") {
                $('button[title="Reset Filter"]').trigger('click');
            }
        },
        openInPopUp:function (url) {
            var width = '1100';
            var height = '400';
            var scroller = 1;
            var screenX = typeof window.screenX != 'undefined' ? window.screenX : window.screenLeft;
            var screenY = typeof window.screenY != 'undefined' ? window.screenY : window.screenTop;
            var outerWidth = typeof window.outerWidth != 'undefined' ? window.outerWidth : document.body.clientWidth;
            var outerHeight = typeof window.outerHeight != 'undefined' ? window.outerHeight : (document.body.clientHeight - 22);
            var left = parseInt(screenX + ((outerWidth - width) / 2), 10);
            var top = parseInt(screenY + ((outerHeight - height) / 2.5), 10);
            var settings = (
                'width=' + width +
                ',height=' + height +
                ',left=' + left +
                ',top=' + top +
                ',scrollbars=' + scroller
                );
           popup = window.open(url,'',settings);
           popup.onunload = self.afterChildClose;
        }
    });
    return $.mage.productImportScript;
});
