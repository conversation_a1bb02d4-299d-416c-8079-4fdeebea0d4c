<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">wc_map_products_list.wc_map_products_list_data_source</item>
        </item>
    </argument>
    <settings>
        <spinner>content_column</spinner>
        <deps>
            <dep>wc_map_products_list.wc_map_products_list_data_source</dep>
        </deps>
    </settings>
    <dataSource name="wc_map_products_list_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="cacheRequests" xsi:type="boolean">false</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <dataProvider 
        class="Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Productmap\ProductMappingDataProvider"
        name="wc_map_products_list_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>entity_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="params" xsi:type="array">
                        <item name="filters_modifier" xsi:type="array"/>
                    </item>
                </item>
            </argument>
            <settings>
                <statefull>
                    <property name="applied" xsi:type="boolean">false</property>
                </statefull>
            </settings>
        </filters>
        <paging name="listing_paging"/>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">wc_map_products_list.wc_map_products_list.content_column.productIds</item>
                    <item name="indexField" xsi:type="string">entity_id</item>
                </item>
            </argument>
            <action name="massdelete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">massdelete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                        <item name="url" xsi:type="url" path="mpmultistorewoocommerce/map/promassdelete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Delete</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you want to delete selected products?</item>
                        </item>
                    </item>
                </argument>
            </action>
        </massaction>
    </listingToolbar>
    <columns name="content_column" class="Magento\Ui\Component\Listing\Columns">
        <selectionsColumn name="productIds">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">entity_id</item>
                    <item name="sortOrder" xsi:type="number">0</item>
                    <item name="preserveSelectionsOnFilter" xsi:type="boolean">true</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="entity_id" sortOrder="10">
            <settings>
                <filter>textRange</filter>
                <addField>true</addField>
                <label translate="true">ID</label>
                <!-- <sorting>asc</sorting> -->
            </settings>
        </column>
        <column name="woocommerce_pro_id" sortOrder="30">
            <settings>
                <filter>text</filter>
                <label translate="true">Woocommerce Product Id</label>
            </settings>
        </column>
        <column name="name">
            <settings>
                <filter>text</filter>
                <label translate="true">Product Name</label>
            </settings>
        </column>
         <column name="product_type" sortOrder="212">
            <settings>
                <filter>text</filter>
                <label translate="true">Type</label>
            </settings>
        </column>
        <column name="magento_pro_id" sortOrder="30">
            <settings>
                <label translate="true">Product Id</label>
            </settings>
        </column>
    </columns>
</listing>
