<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">wc_map_categories_list.wc_map_categories_list_data_source</item>
            <item name="deps" xsi:type="string">wc_map_categories_list.wc_map_categories_list_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">cat_map_columns</item>
    </argument>
    <dataSource name="wc_map_categories_list_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategorymap\CategoryMappingDataProvider</argument>
            <argument name="name" xsi:type="string">wc_map_categories_list_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="marketplace/mui_index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="cacheRequests" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </argument>
    </dataSource>
    <listingToolbar name="listing_top">
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters"/>
        <paging name="listing_paging"/>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">wc_map_categories_list.wc_map_categories_list.cat_map_columns.productIds</item>
                    <item name="indexField" xsi:type="string">entity_id</item>
                </item>
            </argument>
            <action name="massdelete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">massdelete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                        <item name="url" xsi:type="url" path="mpmultishopifystoremageconnect/map/catmassdelete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Delete</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you want to delete selected category map record ?</item>
                        </item>
                    </item>
                </argument>
            </action>
        </massaction>
    </listingToolbar>
    <columns name="cat_map_columns" class="Magento\Ui\Component\Listing\Columns">
        <selectionsColumn name="productIds">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">entity_id</item>
                    <item name="sortOrder" xsi:type="number">0</item>
                    <item name="preserveSelectionsOnFilter" xsi:type="boolean">true</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="entity_id" sortOrder="10">
            <settings>
                <filter>textRange</filter>
                <addField>true</addField>
                <label translate="true">ID</label>
            </settings>
        </column>
        <column name="mage_cat_id" sortOrder="30">
            <settings>
                <label translate="true">Magento Category</label>
            </settings>
        </column>
        <column name="woocommerce_cat_name">
            <settings>
                <label translate="true">WooCommerce Category</label>
            </settings>
        </column>
        <column name="published_at_woocommerce" class="Magento\Ui\Component\Listing\Columns\Date"
        component="Magento_Ui/js/grid/columns/date" sortOrder="100">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Published At WooCommerce</label>
                <sorting>desc</sorting>
            </settings>
        </column>
    </columns>
</listing>
