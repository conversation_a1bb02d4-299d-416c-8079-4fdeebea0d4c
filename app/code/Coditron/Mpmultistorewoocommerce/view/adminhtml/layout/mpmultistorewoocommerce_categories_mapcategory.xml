<?xml version="1.0"?>
<!--
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */  -->
<layout xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="admin-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/layout_generic.xsd">
    <update handle="formkey"/>
    <container name="content">
    	<block class="Magento\Framework\View\Element\Template" name="Mpmultistorewoocommerce-mapping-form" template="Coditron_Mpmultistorewoocommerce::category/mapping-form.phtml">
            <arguments>
                <argument
                    name="view_model"
                    xsi:type="object">Coditron\Mpmultistorewoocommerce\ViewModel\Category
                </argument>
            </arguments> 
        </block>
    </container>
    <container name="root">
    	<block class="Magento\Framework\View\Element\Template" name="Mpmultistorewoocommerce-mapping-event" template="Coditron_Mpmultistorewoocommerce::category/map.phtml">
            <arguments>
                <argument
                    name="view_model"
                    xsi:type="object">Coditron\Mpmultistorewoocommerce\ViewModel\Category
                </argument>
            </arguments>
        </block>
        <block class="Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tab\MapCategoryGrid" name="mapping_grid"/>
    </container>
</layout>
