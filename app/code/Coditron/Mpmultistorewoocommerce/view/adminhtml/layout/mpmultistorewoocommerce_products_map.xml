<?xml version="1.0"?>
<!--
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */  -->
<layout xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="admin-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/layout_generic.xsd">
	<update handle="formkey"/>
    <container name="content">
    	<block class="Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Product\Profiler"  name="Mpmultistorewoocommerce-product-mapping-area" template="Coditron_Mpmultistorewoocommerce::product/woocommerce-map-product-buttons.phtml"/>
    </container>
   <!-- <container name="root">
    	<block class="Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Product\Profiler" name="protabs.script" template="Coditron_Mpmultistorewoocommerce::product/import-script.phtml">
            <arguments>
                <argument name="view_model" xsi:type="object">Coditron\Mpmultistorewoocommerce\ViewModel\Product</argument>
            </arguments>
        </block>
    	 <block class="Coditron\Mpmultistorewoocommerce\Block\Adminhtml\Woocommerceaccount\Edit\Tab\MapProductGrid" name="mapping-products-grid"/> 
    </container> -->
</layout>
    