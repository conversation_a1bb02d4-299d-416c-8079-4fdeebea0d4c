<?xml version="1.0"?>
<!--
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
 -->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="admin-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
      <!--  <head><css src="Webkul_MpMultiShopifyStoreMageConnect::css/styles.css"/>
        <css src="Webkul_MpMultiShopifyStoreMageConnect::css/acc.css"/> 
      </head>-->
        <body>
                <referenceContainer name="content">
                       <block
                        class="Magento\Framework\View\Element\Template"
                         name="coditron_mpmultistorewoocommerce_account_edit_js"
                         template="Coditron_Mpmultistorewoocommerce::account/authorize.phtml"
                        >
                         <arguments>
                          <argument name="view_model" xsi:type="object">Coditron\Mpmultistorewoocommerce\ViewModel\Authorize</argument>
                         </arguments>
                        </block>
                </referenceContainer>
        </body>
</page>
