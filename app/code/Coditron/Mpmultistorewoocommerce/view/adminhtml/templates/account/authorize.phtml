<?php
   /**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
    $viewModel = $block->getViewModel();
    $ruleId = $block->getRequest()->getParam('id');
    $releventData = [
        'authorizeUrl' => $block->getUrl('mpmultistorewoocommerce/woocommerceaccount/authorizeuser'),
        'ruleId'     => $ruleId
    ];
    $serializedData = $viewModel->jsonEncode($releventData);
    ?>
