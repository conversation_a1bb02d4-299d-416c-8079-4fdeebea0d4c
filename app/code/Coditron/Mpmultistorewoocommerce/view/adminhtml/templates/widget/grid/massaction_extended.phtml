<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
?>

<div id="<?= $block->getHtmlId() ?>" class="admin__grid-massaction">

    <?php if ($block->getHideFormElement() !== true):?>
    <form action="" id="<?= $block->getHtmlId() ?>-form" method="post">
    <?php endif ?>
        <div class="admin__grid-massaction-form">
        <?= $block->getBlockHtml('formkey') ?>
            <select
                id="<?= $block->getHtmlId() ?>-select"
                class="local-validation admin__control-select">
                <option class="admin__control-select-placeholder" 
                value="" selected><?= /* @noEscape */ __('Actions') ?></option>
                <?php foreach ($block->getItems() as $_item): ?>
    <option 
    value="<?= /* @noEscape */ $_item->getId() ?>"<?= ($_item->getSelected() ? ' selected="selected"' : '') ?>>
                    <?= /* @noEscape */ $_item->getLabel() ?></option>
                <?php endforeach; ?>
            </select>
            <span class="outer-span" id="<?= $block->getHtmlId() ?>-form-hiddens"></span>
            <span class="outer-span" id="<?= $block->getHtmlId() ?>-form-additional"></span>
            <?= $block->getApplyButtonHtml() ?>
        </div>
    <?php if ($block->getHideFormElement() !== true):?>
    </form>
    <?php endif ?>
    <div class="no-display">
        <?php foreach ($block->getItems() as $_item): ?>
            <div id="<?= $block->getHtmlId() ?>-item-<?= /* @noEscape */ $_item->getId() ?>-block">
                <?= $_item->getAdditionalActionBlockHtml() ?>
            </div>
        <?php endforeach; ?>
    </div>
    <div class="mass-select-wrap">
        <select
            id="<?= $block->getHtmlId() ?>-mass-select"
            class="action-select-multiselect"
            data-menu="grid-mass-select">
            <optgroup label="<?= /* @noEscape */ __('Mass Actions') ?>">
                <option disabled selected></option>
            <?php if ($block->getUseSelectAll()):?>
                <option value="selectAll">
                    <?= /* @noEscape */ __('Select All') ?>
                </option>
                <option value="unselectAll">
                    <?= /* @noEscape */ __('Unselect All') ?>
                </option>
            <?php endif; ?>
                <option value="selectVisible">
                    <?= /* @noEscape */ __('Select Visible') ?>
                </option>
                <option value="unselectVisible">
                    <?= /* @noEscape */ __('Unselect Visible') ?>
                </option>
            </optgroup>
        </select>
        <label for="<?= $block->getHtmlId() ?>-mass-select"></label>
    </div>
<script>
    require(['jquery'], function($){
        'use strict';
        $('#<?= $block->getHtmlId() ?>-mass-select').change(function () {
            var massAction = $('option:selected', this).val();
            switch (massAction) {
                <?php if ($block->getUseSelectAll()):?>
                case 'selectAll':
                    return <?= /* @noEscape */ $block->getJsObjectName() ?>.selectAll();
                    break;
                case 'unselectAll':
                    return <?= /* @noEscape */ $block->getJsObjectName() ?>.unselectAll();
                    break;
                <?php endif; ?>
                case 'selectVisible':
                    return <?= /* @noEscape */ $block->getJsObjectName() ?>.selectVisible();
                    break;
                case 'unselectVisible':
                    return <?= /* @noEscape */ $block->getJsObjectName() ?>.unselectVisible();
                    break;
            }
            this.blur();
        });
    });

    <?php if (!$block->getParentBlock()->canDisplayContainer()): ?>
        <?= /* @noEscape */ $block->getJsObjectName() ?>.setGridIds('<?= /* @noEscape */ $block->getGridIdsJson() ?>');
    <?php endif; ?>
</script>
</div>