<!-- 
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */  -->
 <?php
    $loaderPath = $block->getViewFileUrl("Coditron_Mpmultistorewoocommerce::images/loader.gif");
    $loaderElement = '<img src="'.$loaderPath.'" class=loader style=margin-left:5px></img>';
    $viewModel = $block->getViewModel();
    $releventData = [
        "loaderPath"                =>$loaderPath,
        "formSelector"        => '#edit_form',
        "saveMapSelector"    =>'.wc-save-mapping',
        'mageCategorySelector'        => '.mage_category',
        'saveMappingAjaxUrl'        => $block->getUrl('*/*/savemapping'),
        'getMageChildCategoryAjaxUrl'    => $block->getUrl('*/*/getmagechildcategory'),
        'woocommerceCategorySelector'    => '.woocommerce_category',
        'getWoocommerceChildCategoryAjaxUrl'     => $block->getUrl('*/*/getwoocommercechildcategory')
    ];
    $serializedData = $viewModel->jsonEncode($releventData);
    ?>
<script type="text/x-magento-init">
    {
        "*":{
            "Coditron_Mpmultistorewoocommerce/js/category-map-plugin":<?=/* @noEscape */ $serializedData ?>
        }
    }
</script>