<!--
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */  -->
<?php
    $viewModel = $block->getViewModel();
    $mageTopLevelCate = $viewModel->getHelper()->getTopLevelMageCategory();
    $woocommerceTopLevelCate = $viewModel->getHelper()->getTopLevelWoocommerceCategory();

    // $woocommerceTopLevelCate = [];
?>
<fieldset class="fieldset wk-mapping-fieldset" id="catmap_base_fieldset">
    <legend class="admin__legend legend">
        <span><?=/* @noEscape */__('Map New Category')?></span>
    </legend><br>
    <button id="wc-save-mapping" title="Save Woocommerce mapping" type="button" class="button wc-save-mapping primary">
        <span class="button">
            <span><?=/* @noEscape */ __('Save Mapping') ?></span>
        </span>
    </button>
    <div>
        <label>
            <span><?=/* @noEscape */ __('Store Category') ?></span>
        </label>
        <div class="admin__field-control control">
            <select class="mage_category _required select admin__control-select" name="mage_category">
            <?php
            foreach ($mageTopLevelCate as $key => $info) { ?>
                <option value=<?=/* @noEscape */ $info['value']; ?>><?=/* @noEscape */ $info['label']; ?></option>
                <?php
            }
            ?>
            </select>
        </div>
    </div>

    <div>
        <div style="margin-top:55px">     
            <label>
                <span><?=/* @noEscape */ __('Woo Commerce Category') ?></span>
            </label>
        </div>
        <div class="admin__field-control control" style="margin-top:10px">
            <select class="shopify_category _required select admin__control-select" name="woocommerce_category">
            <?php
            foreach ($woocommerceTopLevelCate as $key => $info) { ?>
                    <option value=<?=/* @noEscape */ $info['value']; ?>><?=/* @noEscape */ $info['label']; ?></option>
                <?php
            }
            ?>
            </select>
        </div>
    </div>
</fieldset>