<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
?>
<div class="page-main-actions">
    <div class='wk-product-import-buttons'>
        <button id="wc-import-product" title="<?php echo __('Import Product From Woo Commerce'); ?>" 
        type="button" class="button wk-import-product primary">
            <span class="button">
                <span><?php echo __('Import Product From Woo Commerce'); ?></span>
            </span>
        </button>
        <button id="wc-run-profiler" title="<?php echo __('Create imported product'); ?>" 
        type="button" class="button wk-run-profiler primary">
            <span class="button">
                <span><?php echo __('Create imported product'); ?></span>
            </span>
        </button>
    </div>
    <img id="loader" src="<?php echo $this->getViewFileUrl('Coditron_Mpmultistorewoocommerce::images/loader.gif'); ?>" style="display:none;" alt="Loading...">
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script type="text/javascript">
document.getElementById('wc-import-product').addEventListener('click', function() {
    console.log("in button click");
    var productImportData = <?php echo json_encode($block->getProductImportScriptData()); ?>;
    //console.log(productImportData);
    document.getElementById('loader').style.display = 'block';
    jQuery.ajax({
        url: '<?php echo $block->getUrl('mpmultistorewoocommerce/products/import'); ?>',
        type: 'POST',
        data: JSON.stringify({ productImportData: productImportData }),
        contentType: 'application/json',
        dataType: 'json',
        success: function(response) {
            console.log(response);
            document.getElementById('loader').style.display = 'none';
            // Handle success (e.g., update the UI, show a success message, etc.)
        },
        error: function(xhr, status, error) {
            console.error('Form submission failedd:', error);
            document.getElementById('loader').style.display = 'none';
            // Handle error (e.g., show an error message, retry, etc.)
        }
    });
});
document.getElementById('wc-run-profiler').addEventListener('click', function() {
    console.log("in create product button click");
    var productImportData = <?php echo json_encode($block->getProductImportScriptData()); ?>;
    //console.log(productImportData);
    document.getElementById('loader').style.display = 'block';
    jQuery.ajax({
        url: '<?php echo $block->getUrl('mpmultistorewoocommerce/products/createproduct'); ?>',
        type: 'POST',
        data: JSON.stringify({ productImportData: productImportData }),
        contentType: 'application/json',
        dataType: 'json',
        success: function(response) {
            console.log(response);
            document.getElementById('loader').style.display = 'none';
            // Handle success (e.g., update the UI, show a success message, etc.)
        },
        error: function(xhr, status, error) {
            console.error('Form submission failedd:', error);
            document.getElementById('loader').style.display = 'none';
            // Handle error (e.g., show an error message, retry, etc.)
        }
    });
});
</script>
