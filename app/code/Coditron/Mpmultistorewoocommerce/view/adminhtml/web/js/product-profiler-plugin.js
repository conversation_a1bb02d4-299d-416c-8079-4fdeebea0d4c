/**
 * @category   Webkul
 * @package    Webkul_MpMultiShopifyStoreMageConnect
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
/*jshint jquery:true*/
define([
    'jquery',
    'mage/translate',
    'Magento_Ui/js/modal/alert'
], function ($,$t,alert) {
    'use strict';
    var skipCount,total;
    $.widget('mage.productProfilerPlugin', {
        _create: function () {
            var self = this;
            skipCount = 0;
            var total = self.options.productCount;
            var ruleId = self.options.ruleId;
            if (total > 0) {
                importProduct(0);
            }
            function importProduct(count)
            {
                $.ajax({
                    type: 'POST',
                    url:self.options.importUrl,
                    async: true,
                    dataType: 'json',
                    data : {form_key: window.FORM_KEY, 'ruleId':ruleId, 'count':count},
                    success: function(data) {
                        if(data['skipped_errors'].length) {
                            $.each(data['skipped_errors'], function (i, error){
                                $(".wk-mu-error-msg-container").prepend($('<div>')
                                                        .addClass('message message-error error')
                                                        .text(error)
                                                    );
                            });
                        }
                        if(data['error'] == 1 && data['msg'] != '') {
                            $(".wk-mu-error-msg-container").prepend($('<div>')
                                                    .addClass('message message-error error')
                                                    .text(data['msg'])
                                                );
                        } else {
                            count += data['total_processed'];
                            var width = (100/total)*count;
                            console.log(count, width, total)
                            $(".wk-mu-progress-bar-current").animate({width: width+"%"},'slow', function() {
                                if(count >= total) {
                                    $(".wk-mu-info-bar").text($.mage.__('Completed'));
                                    $(".fieldset").append(data['msg']);
                                } else {
                                    $(".wk-current").text(count);
                                    importProduct(count);
                                }
                            });
                        }
                    }
                });
            }
        }
    });
    return $.mage.productProfilerPlugin;
});
