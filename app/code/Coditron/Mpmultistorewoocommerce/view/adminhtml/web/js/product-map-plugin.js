/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
/*jshint jquery:true*/
define(["jquery", "mage/translate", "Magento_Ui/js/modal/alert"], function (
  $,
  $t,
  alert
) {
  "use strict";
  var id, popup;
  var flag = 1;
  var requests = [],
    page_info = "";
  var totalImportedProduct = 0;
  var index = 0;
  var mappedCates = [];
  var self;
  var cate_id = "";
  var pageInfoList = [];
  var errorHtml = "";
  $.widget("mage.productImportScript", {
    _create: function () {
      self = this;
      // self.options.cates = '1';
      mappedCates = self.options.cates.split(",");
      $(this.options.importProductSelector).click(function (e) {
        id = $("#entity_id").val();
        e.preventDefault();
        // import Shopify all product
        if (parseInt(self.options.isAllProImport)) {
          self.importProducts();
        } else {
          alert("in elsse loop");
          cate_id = mappedCates.pop();
          console.log(cate_id);
          if (typeof cate_id !== "undefined" && cate_id != "") {
            self.importProducts(cate_id);
            //ajaxCall(index);
          } else {
            $("<div>")
              .html("You have not mapped any Woo Commerce category.")
              .modal({
                title: $.mage.__("Attention"),
                autoOpen: true,
                buttons: [
                  {
                    text: "OK",
                    attr: {
                      "data-action": "cancel",
                    },
                    class: "action-primary",
                    click: function () {
                      this.closeModal();
                    },
                  },
                ],
              });
          }
        }
      });

      var ajaxCall = function (index) {
        if (index < mappedCates.length) {
          self.importProducts(mappedCates[index]);
        }
      };

      $(self.options.profilerSelector).click(function (e) {
        var width = "1100";
        var height = "400";
        var scroller = 1;
        var screenX =
          typeof window.screenX != "undefined"
            ? window.screenX
            : window.screenLeft;
        var screenY =
          typeof window.screenY != "undefined"
            ? window.screenY
            : window.screenTop;
        var outerWidth =
          typeof window.outerWidth != "undefined"
            ? window.outerWidth
            : document.body.clientWidth;
        var outerHeight =
          typeof window.outerHeight != "undefined"
            ? window.outerHeight
            : document.body.clientHeight - 22;
        var left = parseInt(screenX + (outerWidth - width) / 2, 10);
        var top = parseInt(screenY + (outerHeight - height) / 2.5, 10);
        var settings =
          "width=" +
          width +
          ",height=" +
          height +
          ",left=" +
          left +
          ",top=" +
          top +
          ",scrollbars=" +
          scroller;
        popup = window.open(self.options.profilerAjaxUrl, "", settings);
        popup.onunload = self.afterChildClose;
      });

      $(self.options.imageImportSelector).click(function (e) {
        var width = "1100";
        var height = "400";
        var scroller = 1;
        var screenX =
          typeof window.screenX != "undefined"
            ? window.screenX
            : window.screenLeft;
        var screenY =
          typeof window.screenY != "undefined"
            ? window.screenY
            : window.screenTop;
        var outerWidth =
          typeof window.outerWidth != "undefined"
            ? window.outerWidth
            : document.body.clientWidth;
        var outerHeight =
          typeof window.outerHeight != "undefined"
            ? window.outerHeight
            : document.body.clientHeight - 22;
        var left = parseInt(screenX + (outerWidth - width) / 2, 10);
        var top = parseInt(screenY + (outerHeight - height) / 2.5, 10);
        var settings =
          "width=" +
          width +
          ",height=" +
          height +
          ",left=" +
          left +
          ",top=" +
          top +
          ",scrollbars=" +
          scroller;
        popup = window.open(self.options.importImgProfiler, "", settings);
        popup.onunload = self.afterChildClose;
      });
    },
    afterChildClose: function () {
      if (popup.location != "about:blank") {
        $('button[title="Reset Filter"]').trigger("click");
      }
    },
    importProducts: function (cate_id = "") {
      pageInfoList.push(page_info);
      $.ajax({
        url: self.options.importAjaxUrl,
        data: {
          form_key: window.FORM_KEY,
          page_info: page_info,
          id: id,
          cate_id: cate_id,
        },
        type: "POST",
        dataType: "JSON",
        // showLoader : true,
        beforeSend: function () {
          $("body").trigger("processStart");
        },
        success: function (shopifyPro) {
          if (shopifyPro.error_msg) {
            flag = 0;
            totalImportedProduct = 0;
            $("body").trigger("processStop");
            $("body").find(".loading-mask").hide();
            $("<div>")
              .html(shopifyPro.error_msg)
              .modal({
                title: $.mage.__("Attention"),
                autoOpen: true,
                buttons: [
                  {
                    text: "OK",
                    attr: {
                      "data-action": "cancel",
                    },
                    class: "action-primary",
                    click: function () {
                      this.closeModal();
                    },
                  },
                ],
              });
          } else {
            if (
              cate_id == "" &&
              shopifyPro.total_imported == 0 &&
              pageInfoList.indexOf(shopifyPro.next_page_info) == -1
            ) {
              page_info = shopifyPro.next_page_info;
              totalImportedProduct =
                parseInt(totalImportedProduct) + parseInt(shopifyPro.data);
              self.importProducts();
            } else if (
              cate_id != "" &&
              shopifyPro.total_imported == 0 &&
              pageInfoList.indexOf(shopifyPro.next_page_info) == -1
            ) {
              page_info = shopifyPro.next_page_info;
              totalImportedProduct =
                parseInt(totalImportedProduct) + parseInt(shopifyPro.data);
              self.importProducts(cate_id);
            } else if (
              cate_id != "" &&
              (pageInfoList.indexOf(shopifyPro.next_page_info) != -1 ||
                shopifyPro.next_page_info == "")
            ) {
              page_info = shopifyPro.next_page_info;
              totalImportedProduct =
                parseInt(totalImportedProduct) + parseInt(shopifyPro.data);
              cate_id = mappedCates.pop();
              if (typeof cate_id !== "undefined") {
                self.importProducts(cate_id);
              } else {
                // totalImportedProduct = parseInt(totalImportedProduct) + parseInt(shopifyPro.data);
                $("body").trigger("processStop");
                $(".popup-inner").find(".count").remove();
                $(".loading-mask").hide();
                if (flag) {
                  if (parseInt(totalImportedProduct)) {
                    var msg =
                      "Total " +
                      totalImportedProduct +
                      " products imported in your store from Woo commerce run products profiler for create these products in your store";
                  } else {
                    var msg =
                      "All products have been imported, run products profiler for create these products in your store";
                  }
                  $("<div>")
                    .html(msg)
                    .modal({
                      title: $.mage.__("Attention"),
                      autoOpen: true,
                      buttons: [
                        {
                          text: "OK",
                          attr: {
                            "data-action": "cancel",
                          },
                          class: "action-primary",
                          click: function () {
                            this.closeModal();
                          },
                        },
                      ],
                    });
                }
                totalImportedProduct = 0;
              }
            } else {
              totalImportedProduct =
                parseInt(totalImportedProduct) + parseInt(shopifyPro.data);
              $("body").trigger("processStop");
              $(".popup-inner").find(".count").remove();
              $(".loading-mask").hide();
              if (flag) {
                if (parseInt(totalImportedProduct)) {
                  var msg =
                    "Total " +
                    totalImportedProduct +
                    " products imported in your store from Woo commerce run products profiler for create these products in your store";
                } else {
                  var msg =
                    "All products have been imported, run products profiler for create these products in your store";
                }

                $("<div>")
                  .html(msg)
                  .modal({
                    title: $.mage.__("Attention"),
                    autoOpen: true,
                    buttons: [
                      {
                        text: "OK",
                        attr: {
                          "data-action": "cancel",
                        },
                        class: "action-primary",
                        click: function () {
                          this.closeModal();
                        },
                      },
                    ],
                  });
              }
              totalImportedProduct = 0;
            }
          }
        },
        error: function (error) {
          totalImportedProduct = 0;
          $("body").trigger("processStop");
          console.log(error);
        },
      }).done(function () {
        $("#loader-image").removeClass("show");
      });
    },
  });
  return $.mage.productImportScript;
});
