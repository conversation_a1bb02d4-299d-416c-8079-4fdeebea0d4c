/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
/*jshint jquery:true*/
define(["jquery", "mage/translate"], function ($, $t) {
  "use strict";
  $.widget("mage.categoryMap", {
    _create: function () {
      var self = this;
      var loaderPath = self.options.loaderPath;
      var loader = $("<img />", {
        src: loaderPath,
        class: "loader",
        style: "margin-left:5px;",
      });
      $(self.options.formSelector).on(
        "change",
        self.options.mageCategorySelector,
        function () {
          var cat_id = $(this).val();
          if (cat_id == "") {
            $("<div>")
              .html($.mage.__("please select magento category"))
              .modal({
                title: $.mage.__("Attention"),
                autoOpen: true,
                buttons: [
                  {
                    text: $.mage.__("OK"),
                    attr: {
                      "data-action": "cancel",
                    },
                    class: "action-primary",
                    click: function () {
                      this.closeModal();
                    },
                  },
                ],
              });
            return false;
          }
          var subcat = $(this);
          subcat.after(loader.clone());
          subcat.nextAll(".mage_category").remove();
          if ($(".mage_category").length > 1) {
            subcat.attr("style", "margin-top: 10px;min-width:300px;");
          }

          $.ajax({
            url: self.options.getMageChildCategoryAjaxUrl,
            data: { form_key: window.FORM_KEY, cat_id: cat_id },
            type: "POST",
            dataType: "JSON",
            success: function (magecat) {
              if (magecat.totalRecords) {
                var select = $("<select>", {
                  class:
                    "required-entry mage_category _required select admin__control-select",
                  style: "margin-top: 10px;min-width:300px;",
                  id: "mage_category_" + $(".mage_category").length,
                }).append($("<option>").val("").text("Select Sub Category"));
                $(magecat.items).each(function (i, cat) {
                  select.append($("<option>").val(cat.value).text(cat.lable));
                });
                $(".mage_category:last").next().after(select);
              } else {
                subcat
                  .attr("style", subcat.attr("style") + "border-color:green;")
                  .attr("name", "leaf_mage_category");
              }
              subcat.next("img").remove();
            },
            error: function (error) {
              $("<div>")
                .html(error)
                .modal({
                  title: $.mage.__("Attention"),
                  autoOpen: true,
                  buttons: [
                    {
                      text: $.mage.__("OK"),
                      attr: {
                        "data-action": "cancel",
                      },
                      class: "action-primary",
                      click: function () {
                        this.closeModal();
                      },
                    },
                  ],
                });
            },
          });
        }
      );

      $(self.options.formSelector).on(
        "change",
        self.options.woocommerceCategorySelector,
        function () {
          var cat_id = $(this).val();
          if (cat_id == "") {
            $("<div>")
              .html($.mage.__("please select woo commerce category"))
              .modal({
                title: $.mage.__("Attention"),
                autoOpen: true,
                buttons: [
                  {
                    text: $.mage.__("OK"),
                    attr: {
                      "data-action": "cancel",
                    },
                    class: "action-primary",
                    click: function () {
                      this.closeModal();
                    },
                  },
                ],
              });
            return false;
          }
          var subcat = $(this);
          subcat.after(loader);
          subcat.nextAll(".woocommerce_category").remove();
          if ($(".woocommerce_category").length > 1) {
            subcat.attr("style", "margin-top: 10px;min-width:250px;");
          }

          $.ajax({
            url: self.options.getWoocommerceChildCategoryAjaxUrl,
            data: { form_key: window.FORM_KEY, cat_id: cat_id },
            type: "POST",
            dataType: "JSON",
            success: function (woocommerceCat) {
              if (woocommerceCat.totalRecords) {
                var select = $("<select>", {
                  class:
                    "required-entry shopify_category _required select admin__control-select",
                  style: "margin-top:10px; min-width: 250px;",
                  id:
                    "woocommerce_category" + $(".woocommerce_category").length,
                }).append(
                  $("<option>").val("").text("Select Sub woo commerce Category")
                );
                $(woocommerceCat.items).each(function (i, cat) {
                  select.append(
                    $("<option>")
                      .val(cat.woocommerce_cat_id)
                      .text(cat.woocommerce_cat_name)
                  );
                });
                $(".woocommerce_category:last").next().after(select);
              } else {
                subcat
                  .attr("style", subcat.attr("style") + "border-color:green;")
                  .attr("name", "leaf_woo_commerce_category");
              }
              subcat.next("img").remove();
            },
            error: function (error) {
              $("<div>")
                .html(error)
                .modal({
                  title: $.mage.__("Attention"),
                  autoOpen: true,
                  buttons: [
                    {
                      text: $.mage.__("OK"),
                      attr: {
                        "data-action": "cancel",
                      },
                      class: "action-primary",
                      click: function () {
                        this.closeModal();
                      },
                    },
                  ],
                });
              subcat.next("img").remove();
            },
          });
        }
      );

      $(self.options.formSelector).on(
        "click",
        self.options.saveMapSelector,
        function () {
          var woocommerceLeafCate = $(
            'select[name="leaf_woo_commerce_category"]'
          ).val();
          var mageLeafCate = $('select[name="leaf_mage_category"]').val();
          var woocommerceRootCate = $(
            'select[name="woocommerce_category"]'
          ).val();
          var mageRootCate = $('select[name="mage_category"]').val();
          var id = $("#entity_id").val();
          if (woocommerceRootCate && mageLeafCate) {
            $.ajax({
              url: self.options.saveMappingAjaxUrl,
              data: {
                form_key: window.FORM_KEY,
                woocommerceLeafCate: woocommerceRootCate,
                mageLeafCate: mageLeafCate,
                woocommerceRootCate: woocommerceRootCate,
                mageRootCate: mageRootCate,
                id: id,
              },
              type: "POST",
              dataType: "JSON",
              showLoader: true,
              success: function (response) {
                if (response.status) {
                  $('button[title="Reset Filter"]').trigger("click");
                }
                setTimeout(function () {
                  $("<div>")
                    .html(response.msg)
                    .modal({
                      title: $.mage.__("Attention"),
                      autoOpen: true,
                      buttons: [
                        {
                          text: $.mage.__("OK"),
                          attr: {
                            "data-action": "cancel",
                          },
                          class: "action-primary",
                          click: function () {
                            this.closeModal();
                          },
                        },
                      ],
                    });
                }, 4000);
              },
              error: function () {
                $("<div>")
                  .html("Something went wrong")
                  .modal({
                    title: $.mage.__("Attention"),
                    autoOpen: true,
                    buttons: [
                      {
                        text: $.mage.__("OK"),
                        attr: {
                          "data-action": "cancel",
                        },
                        class: "action-primary",
                        click: function () {
                          this.closeModal();
                        },
                      },
                    ],
                  });
              },
            });
          } else {
            alert("Please select the category");
          }
        }
      );
    },
  });
  return $.mage.categoryMap;
});
