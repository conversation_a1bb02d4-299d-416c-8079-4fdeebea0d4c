<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Language translation  UI component
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license    https://www.coditron.com/LICENSE.txt
*/
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Ui/etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">mpmultistorewoocommerce_woocommerceaccount_list.mpmultistorewoocommerce_woocommerceaccount_list_data_source</item>
            <item name="deps" xsi:type="string">mpmultistorewoocommerce_woocommerceaccount_list.mpmultistorewoocommerce_woocommerceaccount_list_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">woocommerceaccount_records_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="wcaccount" xsi:type="array">
                <item name="name" xsi:type="string">wcaccount</item>
                <item name="label" xsi:type="string" translate="true">Add Woo Commerce Account</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">*/*/newwcaccount</item> 
            </item>
        </item>
    </argument>
    <dataSource name="mpmultistorewoocommerce_woocommerceaccount_list_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">mpmultistorewoocommerce_woocommerceaccount_list_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">entity_id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
            </item>
        </argument>
    </dataSource>
    <container name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="template" xsi:type="string">ui/grid/toolbar</item>
                <item name="stickyTmpl" xsi:type="string">ui/grid/sticky/toolbar</item>
            </item>
        </argument>
        <component name="columns_controls">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="columnsData" xsi:type="array">
                        <item name="provider" xsi:type="string">mpmultistorewoocommerce_woocommerceaccount_list.mpmultistorewoocommerce_woocommerceaccount_list.woocommerceaccount_records_columns</item>
                    </item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/controls/columns</item>
                    <item name="displayArea" xsi:type="string">dataGridActions</item>
                </item>
            </argument>
        </component>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="columnsProvider" xsi:type="string">mpmultistorewoocommerce_woocommerceaccount_list.mpmultistorewoocommerce_woocommerceaccount_list.woocommerceaccount_records_columns</item>
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">mpmultistorewoocommerce_woocommerceaccount_list.mpmultistorewoocommerce_woocommerceaccount_list.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.filters</item>
                    </item>
                    <item name="templates" xsi:type="array">
                        <item name="filters" xsi:type="array">
                            <item name="select" xsi:type="array">
                                <item name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</item>
                                <item name="template" xsi:type="string">ui/grid/filters/elements/ui-select</item>
                            </item>
                        </item>
                    </item>
                    <item name="childDefaults" xsi:type="array">
                        <item name="provider" xsi:type="string">mpmultistorewoocommerce_woocommerceaccount_list.mpmultistorewoocommerce_woocommerceaccount_list.listing_top.listing_filters</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">mpmultistorewoocommerce_woocommerceaccount_list.mpmultistorewoocommerce_woocommerceaccount_list.woocommerceaccount_records_columns.${ $.index }:visible</item>
                        </item>
                    </item>
                </item>
                <item name="observers" xsi:type="array">
                    <item name="column" xsi:type="string">column</item>
                </item>
            </argument>
        </filters>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">mpmultistorewoocommerce_woocommerceaccount_list.mpmultistorewoocommerce_woocommerceaccount_list.woocommerceaccount_records_columns.ids</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/tree-massactions</item>
                    <item name="indexField" xsi:type="string">entity_id</item>
                </item>
            </argument>
            <action name="delete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">delete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                        <item name="url" xsi:type="url" path="mpmultistorewoocommerce/woocommerceaccount/massdelete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Attention</item>
                            <item name="message" xsi:type="string" translate="true">Do you want to delete selected woo commerce account record?</item>
                        </item>
                    </item>
                </argument>
            </action>
        </massaction>
        <paging name="listing_paging">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">mpmultistorewoocommerce_woocommerceaccount_list.mpmultistorewoocommerce_woocommerceaccount_list.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.paging</item>
                    </item>
                    <item name="selectProvider" xsi:type="string">mpmultistorewoocommerce_woocommerceaccount_list.mpmultistorewoocommerce_woocommerceaccount_list.woocommerceaccount_records_columns.ids</item>
                </item>
            </argument>
        </paging>
    </container>
    <columns name="woocommerceaccount_records_columns">
         <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">entity_id</item>
                    <item name="sortOrder" xsi:type="number">0</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="entity_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="sorting" xsi:type="string">desc</item>
                    <item name="label" xsi:type="string" translate="true">Id</item>
                </item>
            </argument>
        </column>
        <column name="store_name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="sorting" xsi:type="string">desc</item>
                    <item name="label" xsi:type="string" translate="true">Store Name</item>
                </item>
            </argument>
        </column>
        <actionsColumn name="actions" class="Coditron\Mpmultistorewoocommerce\Ui\Component\Listing\Columns\Woocommerceaccount\WoocommerceaccountAction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">id</item>
                    <item name="sortOrder" xsi:type="number">15</item>
                    <item name="label" xsi:type="string" translate="true">Actions</item>
                </item>
            </argument>
        </actionsColumn>
    </columns>
</listing>

