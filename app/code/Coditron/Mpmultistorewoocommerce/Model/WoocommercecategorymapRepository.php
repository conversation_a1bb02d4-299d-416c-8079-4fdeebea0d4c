<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model;

use Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategorymap;

/**
 * WoocommercecategorymapRepository Class for Woo commerce Category.
 */
class WoocommercecategorymapRepository implements
    \Coditron\Mpmultistorewoocommerce\Api\WoocommercecategorymapRepositoryInterface
{
    /**
     * Get Resource model
     *
     * @var \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\WoocommercecategorymapFactory
     */
    private $resourceModel;
    
    /**
     * @var WoocommercecategorymapFactory
     */
    private $_woocommerceCategoryMapFactory;
    
    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategorymap\CollectionFactory
     */
    private $_collectionFactory;

    /**
     * @param WoocommercecategorymapFactory $woocommerceCategoryMapFactory
     * @param CollectionFactory $cf
     * @param Woocommercecategorymap $resourceModel
     */
    public function __construct(
        WoocommercecategorymapFactory $woocommerceCategoryMapFactory,
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategorymap\CollectionFactory $cf,
        Woocommercecategorymap $resourceModel
    ) {
        $this->resourceModel = $resourceModel;
        $this->_woocommerceCategoryMapFactory = $woocommerceCategoryMapFactory;
        $this->_collectionFactory = $cf;
    }

    /**
     * Get woo commerce mapped category collection by rule id
     *
     * @param int $ruleId
     * @return object
     */
    public function getCollectionByRuleId($ruleId)
    {
        $mappedCateColl = $this->_woocommerceCategoryMapFactory
                        ->create()->getCollection()
                        ->addFieldToFilter(
                            'rule_id',
                            [
                                'eq'=>$ruleId
                            ]
                        );                 
        return $mappedCateColl;
    }

    /**
     * Get collection by mage category id
     *
     * @param int $mageCateId
     * @param int $ruleId
     * @return object
     */
    public function getCollectionByMageCateIdnRuleId($mageCateId, $ruleId)
    {
        $mappedCateColl = $this->_woocommerceCategoryMapFactory
                        ->create()->getCollection()
                        ->addFieldToFilter(
                            'rule_id',
                            [
                                'eq'=>$ruleId
                            ]
                        )->addFieldToFilter(
                            'mage_cat_id',
                            [
                                'eq'=>$mageCateId
                            ]
                        );
        return $mappedCateColl;
    }

    /**
     * Get collection by entity ids
     *
     * @param array $entityIds
     * @return object
     */
    public function getCollectionByIds(array $entityIds)
    {
        $synCateCollection =  $this->_woocommerceCategoryMapFactory->create()
            ->getCollection()
            ->addFieldToFilter(
                'entity_id',
                [
                    'in'=>$entityIds
                ]
            );
        return $synCateCollection;
    }

    /**
     * Get record by magento category id
     *
     * @param int $mageCateIds
     * @param int $ruleId
     * @return object
     */
    public function getCollectionByMageCateIdsnRuleId($mageCateIds, $ruleId)
    {
        $mappedCateColl = $this->_woocommerceCategoryMapFactory
                        ->create()->getCollection()
                        ->addFieldToFilter(
                            'mage_cat_id',
                            [
                                'in'=>$mageCateIds
                            ]
                        )->addFieldToFilter(
                            'rule_id',
                            [
                                'eq'=>$ruleId
                            ]
                        );
        return $mappedCateColl;
    }

    /**
     * Get collection by rule id and wwoocommerce cate id
     *
     * @param  int $wcCateId
     * @param  int $ruleId
     * @return object
     */
    public function getCollectionByWoocommerceCateIdnRuleId($wcCateId, $ruleId)
    {
        $mappedCateColl = $this->_woocommerceCategoryMapFactory
                        ->create()->getCollection()
                        ->addFieldToFilter(
                            'rule_id',
                            [
                                'eq'=>$ruleId
                            ]
                        )->addFieldToFilter(
                            'woocommerce_cat_id',
                            [
                                'eq'=>$wcCateId
                            ]
                        );
        return $mappedCateColl;
    }
}
