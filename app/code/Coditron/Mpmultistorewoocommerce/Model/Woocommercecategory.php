<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model;

use Coditron\Mpmultistorewoocommerce\Api\Data\WoocommercecategoryInterface;
use Magento\Framework\DataObject\IdentityInterface;

class Woocommercecategory extends \Magento\Framework\Model\AbstractModel implements WoocommercecategoryInterface
{
    /**
     * CMS page cache tag.
     */
    public const CACHE_TAG = 'mpmultiwoocommerce_categories';

    /**
     * @var string
     */
    protected $_cacheTag = 'mpmultiwoocommerce_categories';

    /**
     * Prefix of model events names.
     *
     * @var string
     */
    protected $_eventPrefix = 'mpmultiwoocommerce_categories';

    /**
     * Initialize resource model.
     */
    protected function _construct()
    {
        $this->_init(\Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategory::class);
    }
    /**
     * Get EntityId.
     *
     * @return int
     */
    public function getId()
    {
        return $this->getData(self::ID);
    }

    /**
     * Set EntityId.
     *
     * @param int $id
     */
    public function setId($id)
    {
        return $this->setData(self::ID, $id);
    }

    /**
     * Get WoocommerceCatId.
     *
     * @return varchar
     */
    public function getWoocommerceCatId()
    {
        return $this->getData(self::WOOCOMMERCE_CAT_ID);
    }

    /**
     * Set WoocommerceCatId.
     *
     * @param int $woocommerceCatId
     */
    public function setWoocommerceCatId($woocommerceCatId)
    {
        return $this->setData(self::WOOCOMMERCE_CAT_ID, $woocommerceCatId);
    }

    /**
     * Get WoocommerceCatParentid.
     *
     * @return varchar
     */
    public function getWoocommerceCatParentid()
    {
        return $this->getData(self::WOOCOMMERCE_CAT_PARENTID);
    }

    /**
     * Set WoocommerceCatParentid.
     *
     * @param int $woocommerceCatParentid
     */
    public function setWoocommerceCatParentid($woocommerceCatParentid)
    {
        return $this->setData(self::WOOCOMMERCE_CAT_PARENTID, $woocommerceCatParentid);
    }

    /**
     * Get WoocommerceCatName.
     *
     * @return varchar
     */
    public function getWoocommerceCatName()
    {
        return $this->getData(self::WOOCOMMERCE_CAT_NAME);
    }

    /**
     * Set WoocommerceCatName.
     *
     * @param string $woocommerceCatName
     */
    public function setWoocommerceCatName($woocommerceCatName)
    {
        return $this->setData(self::WOOCOMMERCE_CAT_NAME, $woocommerceCatName);
    }

    /**
     * Get CreatedAt.
     *
     * @return varchar
     */
    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set CreatedAt.
     *
     * @param string $createdAt
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }
}
