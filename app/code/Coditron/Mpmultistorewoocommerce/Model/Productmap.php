<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model;

use Coditron\Mpmultistorewoocommerce\Api\Data\ProductmapInterface;
use Magento\Framework\DataObject\IdentityInterface;

class Productmap extends \Magento\Framework\Model\AbstractModel implements ProductmapInterface //, IdentityInterface
{
    /**
     * CMS page cache tag.
     */
    public const CACHE_TAG = 'mpmultiwoocommercesynchronize_product';

    /**
     * @var string
     */
    protected $_cacheTag = 'mpmultiwoocommercesynchronize_product';

    /**
     * Prefix of model events names.
     *
     * @var string
     */
    protected $_eventPrefix = 'mpmultiwoocommercesynchronize_product';

    /**
     * Initialize resource model.
     */
    protected function _construct()
    {
        $this->_init(\Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Productmap::class);
    }
    /**
     * Get EntityId.
     *
     * @return int
     */
    public function getId()
    {
        return $this->getData(self::ID);
    }

    /**
     * Set EntityId.
     *
     * @param int $id
     */
    public function setId($id)
    {
        return $this->setData(self::ID, $id);
    }

    /**
     * Get woocommerceProId.
     *
     * @return varchar
     */
    public function getWoocommerceProId()
    {
        return $this->getData(self::WOOCOMMERCE_PRO_ID);
    }

    /**
     * Set woocommerceProId.
     *
     * @param int $woocommerceProId
     */
    public function setWoocommerceProId($woocommerceProId)
    {
        return $this->setData(self::WOOCOMMERCE_PRO_ID, $shopifyProId);
    }

    /**
     * Get Name.
     *
     * @return varchar
     */
    public function getName()
    {
        return $this->getData(self::NAME);
    }

    /**
     * Set Name.
     *
     * @param varchar $name
     */
    public function setName($name)
    {
        return $this->setData(self::NAME, $name);
    }

    
    /**
     * Get ProductType.
     *
     * @return varchar
     */
    public function getProductType()
    {
        return $this->getData(self::PRODUCT_TYPE);
    }

    /**
     * Set ProductType.
     *
     * @param varchar $productType
     */
    public function setProductType($productType)
    {
        return $this->setData(self::PRODUCT_TYPE, $productType);
    }

    /**
     * Get MagentoProId.
     *
     * @return varchar
     */
    public function getMagentoProId()
    {
        return $this->getData(self::MAGENTO_PRO_ID);
    }

    /**
     * Set MagentoProId.
     *
     * @param int $magentoProId
     */
    public function setMagentoProId($magentoProId)
    {
        return $this->setData(self::MAGENTO_PRO_ID, $magentoProId);
    }

    /**
     * Get MageCatId.
     *
     * @return varchar
     */
    public function getMageCatId()
    {
        return $this->getData(self::MAGE_CAT_ID);
    }

    /**
     * Set MageCatId.
     *
     * @param int $mageCatId
     */
    public function setMageCatId($mageCatId)
    {
        return $this->setData(self::MAGE_CAT_ID, $mageCatId);
    }

    /**
     * Get ChangeStatus.
     *
     * @return varchar
     */
    public function getChangeStatus()
    {
        return $this->getData(self::CHANGE_STATUS);
    }

    /**
     * Set ChangeStatus.
     *
     * @param int $changeStatus
     */
    public function setChangeStatus($changeStatus)
    {
        return $this->setData(self::CHANGE_STATUS, $changeStatus);
    }

    /**
     * Get CreatedAt.
     *
     * @return varchar
     */
    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set CreatedAt.
     *
     * @param string $createdAt
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * Get rule id.
     *
     * @return int
     */
    public function getRuleId()
    {
        return $this->getData(self::RULE_ID);
    }

    /**
     * Set rule id.
     *
     * @param int $ruleId
     */
    public function setRuleId($ruleId)
    {
        return $this->setData(self::RULE_ID, $ruleId);
    }
}
