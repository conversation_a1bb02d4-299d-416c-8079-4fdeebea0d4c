<?php

namespace Coditron\Mpmultistorewoocommerce\Model;

use Magento\Framework\Model\AbstractModel;
use Magento\Framework\DataObject\IdentityInterface;

class ImportedImages extends AbstractModel 
{

    /**
     * @var string
     */
    public const NOROUTE_ENTITY_ID = 'no-route';

    public function _construct()
    {
        $this->_init(\Coditron\Mpmultistorewoocommerce\Model\ResourceModel\ImportedImages::class);
    }
}
