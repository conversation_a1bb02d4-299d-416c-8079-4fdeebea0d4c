<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model;

use Coditron\Mpmultistorewoocommerce\Api\Data\ImportedtmpproductInterface;
use Magento\Framework\DataObject\IdentityInterface;

class Importedtmpproduct extends \Magento\Framework\Model\AbstractModel implements ImportedtmpproductInterface
{
    /**
     * CMS page cache tag.
     */
    public const CACHE_TAG = 'mpmultiwoocommerce_tempwoocommerce';

    /**
     * @var string
     */
    public $_cacheTag = 'mpmultiwoocommerce_tempwoocommerce';

    /**
     * Prefix of model events names.
     *
     * @var string
     */
    public $_eventPrefix = 'mpmultiwoocommerce_tempwoocommerce';

    /**
     * Initialize resource model.
     */
    protected function _construct()
    {
        $this->_init(\Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Importedtmpproduct::class);
    }
    /**
     * Get EntityId.
     *
     * @return int
     */
    public function getId()
    {
        return $this->getData(self::ID);
    }

    /**
     * Set EntityId.
     *
     * @param int $entityId
     */
    public function setId($entityId)
    {
        return $this->setData(self::ID, $entityId);
    }

    /**
     * Get ItemId.
     *
     * @return varchar
     */
    public function getItemId()
    {
        return $this->getData(self::ITEM_ID);
    }

    /**
     * Set ItemId.
     *
     * @param int $itemId
     */
    public function setItemId($itemId)
    {
        return $this->setData(self::ITEM_ID, $itemId);
    }

    /**
     * Get ProductData.
     *
     * @return varchar
     */
    public function getProductData()
    {
        return $this->getData(self::PRODUCT_DATA);
    }

    /**
     * Set ProductData.
     *
     * @param array $productData
     */
    public function setProductData($productData)
    {
        return $this->setData(self::PRODUCT_DATA, $productData);
    }

    /**
     * Get CreatedAt.
     *
     * @return varchar
     */
    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set CreatedAt.
     *
     * @param string $createdAt
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }
}
