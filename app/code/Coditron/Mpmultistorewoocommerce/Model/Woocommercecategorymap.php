<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model;

use Coditron\Mpmultistorewoocommerce\Api\Data\WoocommercecategorymapInterface;
use Magento\Framework\DataObject\IdentityInterface;

class Woocommercecategorymap extends \Magento\Framework\Model\AbstractModel implements WoocommercecategorymapInterface
{
    /**
     * CMS page cache tag.
     */
    public const CACHE_TAG = 'mpmultiwoocommercesynchronize_category';

    /**
     * @var string
     */
    protected $_cacheTag = 'mpmultiwoocommercesynchronize_category';

    /**
     * Prefix of model events names.
     *
     * @var string
     */
    protected $_eventPrefix = 'mpmultiwoocommercesynchronize_category';

    /**
     * Initialize resource model.
     */
    protected function _construct()
    {
        $this->_init(\Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategorymap::class);
    }
    /**
     * Get EntityId.
     *
     * @return int
     */
    public function getId()
    {
        return $this->getData(self::ID);
    }

    /**
     * Set EntityId.
     *
     * @param int $id
     */
    public function setId($id)
    {
        return $this->setData(self::ID, $id);
    }

    /**
     * Get WoocommerceCatId.
     *
     * @return varchar
     */
    public function getWoocommerceCatId()
    {
        return $this->getData(self::WOOCOMMERCE_CAT_ID);
    }

    /**
     * Set WoocommerceCatId.
     *
     * @param int $woocommerceCatId
     */
    public function setWoocommerceCatId($woocommerceCatId)
    {
        return $this->setData(self::WOOCOMMERCE_CAT_ID, $woocommerceCatId);
    }

    /**
     * Get MageCatId.
     *
     * @return varchar
     */
    public function getMageCatId()
    {
        return $this->getData(self::MAGE_CAT_ID);
    }

    /**
     * Set MageCatId.
     *
     * @param int $mageCatId
     */
    public function setMageCatId($mageCatId)
    {
        return $this->setData(self::MAGE_CAT_ID, $mageCatId);
    }

    /**
     * Get ProConditionAttr.
     *
     * @return varchar
     */
    public function getProConditionAttr()
    {
        return $this->getData(self::PRO_CONDITION_ATTR);
    }

    /**
     * Set ProConditionAttr.
     *
     * @param int $proConditionAttr
     */
    public function setProConditionAttr($proConditionAttr)
    {
        return $this->setData(self::PRO_CONDITION_ATTR, $proConditionAttr);
    }

    /**
     * Get VariationsEnabled.
     *
     * @return varchar
     */
    public function getVariationsEnabled()
    {
        return $this->getData(self::VARIATIONS_ENABLED);
    }

    /**
     * Set VariationsEnabled.
     *
     * @param int $variationsEnabled
     */
    public function setVariationsEnabled($variationsEnabled)
    {
        return $this->setData(self::VARIATIONS_ENABLED, $variationsEnabled);
    }

    /**
     * Get CreatedAt.
     *
     * @return varchar
     */
    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set CreatedAt.
     *
     * @param string $createdAt
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }
}
