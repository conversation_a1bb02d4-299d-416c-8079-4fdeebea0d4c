<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model;
use Coditron\Mpmultistorewoocommerce\Api\Data\WoocommerceaccountsInterface;

/**
 * WoocommerceaccountsRepository Class for Woo commerce Account.
 */
class WoocommerceaccountsRepository implements \Coditron\Mpmultistorewoocommerce\Api\WoocommerceaccountsRepositoryInterface
{
    /**
     * Get Resource model
     *
     * @var \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommerceaccounts
     */
    private $resourceModel;
    
    /**
     * @var woocommerceAccountsFactory
     */
    private $_woocommerceAccountsFactory;
    
    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommerceaccounts\CollectionFactory
     */
    private $_collectionFactory;

    /**
     * Construct function
     *
     * @param woocommerceaccountsFactory $woocommerceAccountsFactory
     * @param CollectionFactory $collectionFactory
     * @param \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommerceaccounts $resourceModel
     */
    public function __construct(
        \Coditron\Mpmultistorewoocommerce\Model\WoocommerceaccountsFactory $woocommerceaccountsFactory,
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommerceaccounts\CollectionFactory $collectionFactory,
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommerceaccounts $resourceModel
    ) {
        $this->resourceModel = $resourceModel;
        $this->_woocommerceAccountsFactory = $woocommerceaccountsFactory;
        $this->_collectionFactory = $collectionFactory;
    }

    /**
     * Get Configuration By Id
     *
     * @param  int $id
     * @return object
     */
    public function getConfigurationById($id)
    {
        $woocommerceConfiguration = $this->_woocommerceAccountsFactory->create()->load($id);
        return $woocommerceConfiguration;
    }

    /**
     * Get By User Id
     *
     * @param  int $shopifyUserId
     * @return object
     */
    public function getByUserId($woocommerceUserId)
    {
        $woocommerceAccount = $this->_woocommerceAccountsFactory
                            ->create()
                            ->getCollection()
                            ->addFieldToFilter('woocommerce_user_id', ['eq'=>$shopifyUserId]);
        ;
        return $woocommerceAccount;
    }
}
