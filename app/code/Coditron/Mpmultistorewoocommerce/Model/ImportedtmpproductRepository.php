<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model;

use Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Importedtmpproduct;

/**
 * ImportedtmpproductRepository Class for Woo commerce Imported Product.
 */
class ImportedtmpproductRepository implements
    \Coditron\Mpmultistorewoocommerce\Api\ImportedtmpproductRepositoryInterface
{
    /**
     * @var ImportedtmpproductFactory
     */
    private $importedTmpProductFactory;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Importedtmpproduct
     */
    private $resourceModel;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Importedtmpproduct\CollectionFactory
     */
    private $collectionFactory;

    /**
     * Construct function
     *
     * @param ImportedtmpproductFactory $importedTmpProductFactory
     * @param CollectionFactory         $cf
     * @param Importedtmpproduct        $resourceModel
     */
    public function __construct(
        ImportedtmpproductFactory $importedTmpProductFactory,
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Importedtmpproduct\CollectionFactory $cf,
        Importedtmpproduct $resourceModel
    ) {
        $this->resourceModel = $resourceModel;
        $this->importedTmpProductFactory = $importedTmpProductFactory;
        $this->collectionFactory = $cf;
    }

    /**
     * Get a record by item id and product type
     *
     * @param  string $productType
     * @param  int $itemId
     * @return object
     */
    public function getRecordByItemIdnProductType($productType, $itemId)
    {
        $temItemRecord = $this->importedTmpProductFactory->create()->getCollection();
        $temItemRecord->addFieldToFilter(
            'item_type',
            $productType
        )->addFieldToFilter(
            'item_id',
            $itemId
        );
                    //->getFirstItem();
        return $temItemRecord;
    }

    /**
     * Get tempdate collection by rule id and product type
     *
     * @param  string $productType
     * @param  int $ruleId
     * @return object
     */
    public function getCollectionByProductTypeAndRuleId($productType, $ruleId)
    {
        $temItemRecord = $this->importedTmpProductFactory
                            ->create()->getCollection()
                            ->addFieldToFilter(
                                'item_type',
                                $productType
                            )->addFieldToFilter(
                                'rule_id',
                                $ruleId
                            );
        return $temItemRecord;
    }
}
