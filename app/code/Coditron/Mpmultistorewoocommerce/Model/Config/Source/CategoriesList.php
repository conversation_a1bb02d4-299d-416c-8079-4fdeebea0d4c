<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model\Config\Source;

class CategoriesList implements \Magento\Framework\Option\ArrayInterface
{
    /**
     * @var object
     */
    private $categoryFactory;

    /**
     * @param \Magento\Catalog\Model\CategoryFactory $categoryFactory
     */
    public function __construct(\Magento\Catalog\Model\CategoryFactory $categoryFactory)
    {
        $this->categoryFactory = $categoryFactory;
    }

    /**
     * Return options array.
     *
     * @param int $store
     * @return array
     */
    public function toOptionArray($store = null)
    {
        $categoriesArr = [];
        $categories = $this->categoryFactory->create()->getCollection();

        foreach ($categories as $category) {
            $category = $this->categoryFactory->create()->load($category->getEntityId());
            if ($category->getName() === 'Root Catalog') {
                continue;
            }
            $categoriesArr[] = ['value' => $category->getEntityId(),'label' => $category->getName()];
        }

        return $categoriesArr;
    }

    /**
     * Get options in "key-value" format.
     *
     * @return array
     */
    public function toArray()
    {
        $optionList = $this->toOptionArray();
        $optionArray = [];
        foreach ($optionList as $option) {
            $optionArray[$option['value']] = $option['label'];
        }

        return $optionArray;
    }
}
