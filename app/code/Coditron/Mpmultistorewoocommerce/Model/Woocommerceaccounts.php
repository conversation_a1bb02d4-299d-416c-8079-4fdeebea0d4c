<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */

namespace Coditron\Mpmultistorewoocommerce\Model;

use Coditron\Mpmultistorewoocommerce\Api\Data\WoocommerceaccountsInterface;
use Magento\Framework\Model\AbstractModel;

class Woocommerceaccounts extends AbstractModel implements WoocommerceaccountsInterface
{
    protected function _construct()
    {
        $this->_init('Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommerceaccounts');
    }
    /**
     * Get ID.
     *
     * @return int|null
     */
    public function getId()
    {
        return $this->getData(self::ID);
    }

    /**
     * Set ID.
     *
     * @param int $id
     * @return $this
     */
    public function setId($id)
    {
        return $this->setData(self::ID, $id);
    }

    /**
     * Get attribute set id.
     *
     * @return int
     */
    public function getAttributeSetId()
    {
        return $this->getData(self::ATTRIBUTE_SET_ID);
    }

    /**
     * Set attribute set id.
     *
     * @param int $attributeSetId
     * @return $this
     */
    public function setAttributeSetId($attributeSetId)
    {
        return $this->setData(self::ATTRIBUTE_SET_ID, $attributeSetId);
    }

    /**
     * Get Woocommerce user id.
     *
     * @return string
     */
    public function getWoocommerceUserId()
    {
        return $this->getData(self::WOOCOMMERCE_USER_ID);
    }

    /**
     * Set Woocommerce user id.
     *
     * @param string $woocommerceUserId
     * @return $this
     */
    public function setWoocommerceUserId($woocommerceUserId)
    {
        return $this->setData(self::WOOCOMMERCE_USER_ID, $woocommerceUserId);
    }

    /**
     * Get Woocommerce URL.
     *
     * @return string
     */
    public function getWoocommerceUrl()
    {
        return $this->getData(self::WOOCOMMERCE_URL);
    }

    /**
     * Set Woocommerce URL.
     *
     * @param string $woocommerceUrl
     * @return $this
     */
    public function setWoocommerceUrl($woocommerceUrl)
    {
        return $this->setData(self::WOOCOMMERCE_URL, $woocommerceUrl);
    }

    /**
     * Get store name.
     *
     * @return string
     */
    public function getStoreName()
    {
        return $this->getData(self::STORE_NAME);
    }

    /**
     * Set store name.
     *
     * @param string $storeName
     * @return $this
     */
    public function setStoreName($storeName)
    {
        return $this->setData(self::STORE_NAME, $storeName);
    }

    /**
     * Get Woocommerce consumer key.
     *
     * @return string
     */
    public function getWoocommerceConsumerKey()
    {
        return $this->getData(self::WOOCOMMERCE_CONSUMER_KEY);
    }

    /**
     * Set Woocommerce consumer key.
     *
     * @param string $woocommerceConsumerKey
     * @return $this
     */
    public function setWoocommerceConsumerKey($woocommerceConsumerKey)
    {
        return $this->setData(self::WOOCOMMERCE_CONSUMER_KEY, $woocommerceConsumerKey);
    }

    /**
     * Get Woocommerce consumer secret key.
     *
     * @return string
     */
    public function getWoocommerceConsumerSecretKey()
    {
        return $this->getData(self::WOOCOMMERCE_CONSUMER_SECRET_KEY);
    }

    /**
     * Set Woocommerce consumer secret key.
     *
     * @param string $woocommerceConsumerSecretKey
     * @return $this
     */
    public function setWoocommerceConsumerSecretKey($woocommerceConsumerSecretKey)
    {
        return $this->setData(self::WOOCOMMERCE_CONSUMER_SECRET_KEY, $woocommerceConsumerSecretKey);
    }
}
