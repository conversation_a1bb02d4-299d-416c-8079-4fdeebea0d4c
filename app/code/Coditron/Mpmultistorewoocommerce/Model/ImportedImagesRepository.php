<?php
namespace Coditron\Mpmultistorewoocommerce\Model;

class ImportedImagesRepository 
{

    protected $modelFactory = null;
    protected $collectionFactory = null;

    public function __construct(
        \Coditron\Mpmultistorewoocommerce\Model\ImportedImagesFactory $modelFactory,
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\ImportedImages\CollectionFactory $collectionFactory
    ) {
        $this->modelFactory = $modelFactory;
        $this->collectionFactory = $collectionFactory;
    }
    public function getById($id)
    {
        $model = $this->modelFactory->create()->load($id);
        if (!$model->getId()) {
            throw new \Magento\Framework\Exception\NoSuchEntityException(
                __('The CMS block with the "%1" ID doesn\'t exist.', $id)
            );
        }
        return $model;
    }

    /**
     * Save function
     *
     * @param \Webkul\MpMultiShopifyStoreMageConnect\Model\ImportedImages $subject
     * @return \Webkul\MpMultiShopifyStoreMageConnect\Model\ImportedImages
     */
    public function save(\Coditron\Mpmultistorewoocommerce\Model\ImportedImages $subject)
    {
        try {
            $subject->save();
        } catch (\Exception $exception) {
            throw new \Magento\Framework\Exception\CouldNotSaveException(__($exception->getMessage()));
        }
         return $subject;
    }

    /**
     * Get list
     *
     * @param Magento\Framework\Api\SearchCriteriaInterface $creteria
     * @return Magento\Framework\Api\SearchResults
     */
    public function getList(\Magento\Framework\Api\SearchCriteriaInterface $creteria)
    {
        $collection = $this->collectionFactory->create();
         return $collection;
    }

    /**
     * Delete Image
     *
     * @param \Webkul\MpMultiShopifyStoreMageConnect\Model\ImportedImages $subject
     * @return boolean
     */
    public function delete(\Coditron\Mpmultistorewoocommerce\Model\ImportedImages $subject)
    {
        try {
            $subject->delete();
        } catch (\Exception $exception) {
            throw new \Magento\Framework\Exception\CouldNotDeleteException(__($exception->getMessage()));
        }
        return true;
    }

    /**
     * Delete by id
     *
     * @param int $id
     * @return boolean
     */
    public function deleteById($id)
    {
        return $this->delete($this->getById($id));
    }
}
