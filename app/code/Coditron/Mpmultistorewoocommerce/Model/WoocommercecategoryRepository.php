<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model;

use Coditron\Mpmultistorewoocommerce\Api\Data\WoocommercecategoryInterface;

/**
 * WoocommercecategoryRepository Class for Shopify Category.
 */
class WoocommercecategoryRepository implements \Coditron\Mpmultistorewoocommerce\Api\WoocommercecategoryRepositoryInterface
{
    /**
     * Get Resource model
     *
     * @var WoocommercecategoryFactory $woocommercecategoryFactory
     */
    private $resourceModel;
    
    /**
     * @var WoocommercecategoryFactory $woocommerceCategoryFactory
     */
    private $_woocommerceCategoryFactory;

    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategory\CollectionFactory
     */
    private $_collectionFactory;

    /**
     * Construct function
     *
     * @param WoocommercecategoryFactory $woocommerceCategoryFactory
     * @param CollectionFactory $collectionFactory
     * @param \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategory $resourceModel
     */
    public function __construct(
        WoocommercecategoryFactory $woocommerceCategoryFactory,
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategory\CollectionFactory $collectionFactory,
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategory $resourceModel
    ) {
        $this->resourceModel = $resourceModel;
        $this->_woocommerceCategoryFactory = $woocommerceCategoryFactory;
        $this->_collectionFactory = $collectionFactory;
    }

    /**
     * Get woocommerce category collection by woo commerce category id
     *
     * @param int $woocommerceCateId
     * @return object
     */
    public function getCollectionByWoocommerceCateId($woocommerceCateId)
    {
        $woocommerceExistCatColl = $this->_woocommerceCategoryFactory->create()->getCollection()
            ->addFieldToFilter(
                'woocommerce_cat_id',
                ['eq' => $woocommerceCateId]
            );
        return $woocommerceExistCatColl;
    }

    /**
     * Get Collection by woo commerce parent id
     *
     * @param  int $woocommerceParentId
     * @return object
     */
    public function getCollectionByWoocommerceCateParentId($woocommerceParentId)
    {
        $woocommerceExistCatColl = $this->_woocommerceCategoryFactory->create()->getCollection()
            ->addFieldToFilter(
                'woocommerce_cat_parentid',
                ['eq' => $woocommerceParentId]
            )->addFieldToFilter(
                'woocommerce_cat_id',
                ['neq' => $woocommerceParentId]
            );
        return $woocommerceExistCatColl;
    }
}
