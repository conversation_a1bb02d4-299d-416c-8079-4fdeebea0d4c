<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model;

use Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Categoriesspecification;
use Coditron\Mpmultistorewoocommerce\Api\CategoriesspecificationRepositoryInterface;

/**
 * CategoriesspecificationRepository Class for Shopify Category.
 */
class CategoriesspecificationRepository implements CategoriesspecificationRepositoryInterface
{
    
    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Categoriesspecification
     */
    private $resourceModel;
    
    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Categoriesspecification\CollectionFactory
     */
    private $collectionFactory;

    /**
     * Construct function
     *
     * @param CollectionFactory       $cf
     * @param Categoriesspecification $resourceModel
     */
    public function __construct(
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Categoriesspecification\CollectionFactory $cf,
        Categoriesspecification $resourceModel
    ){
        $this->resourceModel = $resourceModel;
        $this->collectionFactory = $cf;
    }
    
    /**
     * Get collection by shopify category id
     *
     * @param  int $shopifyCateId
     * @return object
     */
    public function getCollectionByWoocommerceCatId($woocommerceCateId)
    {
        $cateSpecification = $this->collectionFactory
                                ->create()->addFieldToFilter(
                                    'woocommerce_category_id',
                                    ['eq'=>$woocommerceCateId]
                                );
        return $cateSpecification;
    }
}
