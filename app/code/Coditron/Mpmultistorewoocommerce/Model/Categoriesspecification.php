<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model;

use Coditron\Mpmultistorewoocommerce\Api\Data\CategoriesspecificationInterface;
use Magento\Framework\DataObject\IdentityInterface;

class Categoriesspecification extends \Magento\Framework\Model\AbstractModel implements CategoriesspecificationInterface
{
    /**
     * CMS page cache tag.
     */
    public const CACHE_TAG = 'woocommercesynchronize_specification_category';

    /**
     * @var string
     */
    protected $_cacheTag = 'woocommercesynchronize_specification_category';

    /**
     * Prefix of model events names.
     *
     * @var string
     */
    protected $_eventPrefix = 'woocommercesynchronize_specification_category';

    /**
     * Initialize resource model.
     */
    protected function _construct()
    {
        $this->_init(\Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Categoriesspecification::class);
    }
    
    /**
     * Get ID.
     *
     * @return int|null
     */
    public function getId(){
        return $this->getData(self::ID);
    }

    /**
     * Set ID.
     *
     * @param int $entityId
     * @return $this
     */
    public function setId($entityId){
        return $this->setData(self::ID, $entityId);
    }

   /**
    * Get WoocommerceCategoryId.
    *
    * @return string
    */
    public function getWoocommerceCategoryId(){
        return $this->getData(self::WOOCOMMERCE_CATEGORY_ID);
    }

   /**
    * Set WoocommerceCategoryId.
    *
    * @param int $shopifyCategoryId
    * @return $this
    */
    public function setWoocommerceCategoryId($woocommerceCategoryId){
        return $this->setData(self::WOOCOMMERCE_CATEGORY_ID, $woocommerceCategoryId);
    }

   /**
    * Get WoocommerceSpecificationName.
    *
    * @return string
    */
    public function getWoocommerceSpecificationName(){
        return $this->getData(self::WOOCOMMERCE_SPECIFICATION_NAME);
    }

   /**
    * Set WoocommerceSpecificationName.
    *
    * @param string $shopifySpecificationName
    * @return object $this
    */
    public function setWoocommerceSpecificationName($woocommerceSpecificationName){
        return $this->setData(self::WOOCOMMERCE_SPECIFICATION_NAME, $woocommerceSpecificationName);
    }

   /**
    * Get MageProductAttributeCode.
    *
    * @return string
    */
    public function getMageProductAttributeCode(){
        return $this->getData(self::MAGE_PRODUCT_ATTRIBUTE_CODE);
    }

   /**
    * Set MageProductAttributeCode.
    *
    * @param string $mageProductAttributeCode
    * @return object $this
    */
    public function setMageProductAttributeCode($mageProductAttributeCode)
    {
        return $this->setData(self::MAGE_PRODUCT_ATTRIBUTE_CODE, $mageProductAttributeCode);
    }

    /**
     * Get CreatedAt.
     *
     * @return varchar
     */
    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set CreatedAt.
     *
     * @param varchar $createdAt
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }
}
