<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategorymap\Source;

use Magento\Framework\App\RequestInterface;

class RootCat
{
    /**
     * @var \Magento\Catalog\Model\Category
     */
    private $categoryFactory;
    
    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    private $resourceConnection;
    
    /**
     * @var RequestInterface
     */
    private $_request;
    
    /**
     * @var \Coditron\Mpmultistorewoocommerce\Helper\Data
     */
    private $_dataHelper;
    
    /**
     * @param \Magento\Catalog\Model\CategoryFactory $categoryFactory
     * @param \Magento\Framework\App\ResourceConnection $resourceConnection
     * @param RequestInterface $requestInterface
     * @param \Coditron\Mpmultistorewoocommerce\Helper\Data $dataHelper
     */
    public function __construct(
        \Magento\Catalog\Model\CategoryFactory $categoryFactory,
        \Magento\Framework\App\ResourceConnection $resourceConnection,
        RequestInterface $requestInterface,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $dataHelper
    ) {
        $this->categoryFactory = $categoryFactory;
        $this->resourceConnection = $resourceConnection;
        $this->_request = $requestInterface;
        $this->_dataHelper = $dataHelper;
    }

    /**
     * Return options array.
     *
     * @param int $store
     * @return array
     */
    public function tomageCatArray($store = null)
    {
        $categoriesArr[] = ['value' => '','label' => 'Select Store Category'];
        $categories = $this->categoryFactory->create()->getCollection()->addFieldToFilter('parent_id', ['eq' => 2]);

        foreach ($categories as $category) {
            $category = $this->categoryFactory->create()->load($category->getEntityId());
            $categoriesArr[] = ['value' => $category->getEntityId(),'label' => $category->getName()];
        }

        return $categoriesArr;
    }

    /**
     * Return options array.
     *
     * @param int $store
     * @return array
     */
    public function towoocommerceCatArray($store = null)
    {
        $categoriesArr[] = ['value' => '', 'label' => 'Select Woo Commerce Category'];
        $woocommerceCategories = $this->_dataHelper->getTheCustomCollectionFromWoocommerce($this->_request->getParam('id'));
    
        if (isset($woocommerceCategories['status']) && $woocommerceCategories['status'] === true) {
            foreach ($woocommerceCategories['data'] as $category) {
                $categoriesArr[] = ['value' => $category['id'], 'label' => $category['name']];
            }
        }
        return $categoriesArr;
    }
    
    /**
     * Get options in "key-value" format.
     *
     * @return array
     */
    public function toArray()
    {
        $optionList = $this->toOptionArray();
        $optionArray = [];
        foreach ($optionList as $option) {
            $optionArray[$option['value']] = $option['label'];
        }
        return $optionArray;
    }
}
