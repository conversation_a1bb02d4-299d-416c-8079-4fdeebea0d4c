<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategorymap;

use Magento\Framework\Session\SessionManagerInterface;

/**
 * Class CategoryMappingDataProvider for Customer Groups
 */
class CategoryMappingDataProvider extends \Magento\Ui\DataProvider\AbstractDataProvider
{
    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategorymap\CollectionFactory
     */
    private $cf;
    
    /**
     * @var \Coditron\Mpmultistorewoocommerce\Helper\Data
     */
    private $helper;
    
    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    private $_storeManager;
    
    /**
     * @var \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory
     */
    private $_categoryCollection;

    private $Fhelper;

    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\WoocommercecategorymapFactory $collection,
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Woocommercecategorymap\CollectionFactory $cf,
        SessionManagerInterface $session,
        \Coditron\Mpmultistorewoocommerce\Helper\Data $helper,
        \Coditron\Mpmultistorewoocommerce\Helper\FData $Fhelper,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $categoryCollection,
        array $meta = [],
        array $data = []
    ) {
        $this->cf = $cf;
        $this->helper = $helper;
        $this->Fhelper = $Fhelper;
        $ruleId = $this->Fhelper->getAccountDetailsBySellerId()->getFirstItem()->getEntityId();
        $this->collection = $this->cf->create()
        ->addFieldToFilter('rule_id', ['eq' => $ruleId])
        ->addFieldToSelect('body_html')
        ->addFieldToSelect('rule_id')
        ->addFieldToSelect('published_at_woocommerce')
        ->addFieldToSelect('mage_cat_id')
        ->addFieldToSelect('woocommerce_cat_id')
        ->addFieldToSelect('woocommerce_cat_name');
        $this->_storeManager = $storeManager;
        $this->_categoryCollection = $categoryCollection;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }
 
    /**
     * @inheritdoc
     */
    public function getData()
    {
        $categories = $this->getCategories();
        $arrItems['items'] = [];
        foreach ($this->getCollection() as $item) {
            $itemData = $item->toArray([]);
            $itemData["mage_cat_id"] = $categories[$itemData["mage_cat_id"]];
            $arrItems['items'][] = $itemData;
        }
        $arrItems['totalRecords'] = $this->collection->getSize();
        return $arrItems;
    }

    /**
     * @inheritdoc
     */
    public function getCategories()
    {
        $categoriesData = [];
        $categories = $this->_categoryCollection->create()
            ->addAttributeToSelect('*')
            ->setStore($this->_storeManager->getStore()); //categories from current store will be fetched
   
        foreach ($categories as $category) {
            $categoriesData[$category->getId()] = $category->getName();
        }
        return $categoriesData;
    }
}
