<?php
namespace Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Productmap;

use Magento\Framework\Session\SessionManagerInterface;


class ProductMappingDataProvider extends \Magento\Ui\DataProvider\AbstractDataProvider
{
    private $helper;
    private $coll;

    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        \Coditron\Mpmultistorewoocommerce\Model\Woocommercecategorymap $collection,
        \Coditron\Mpmultistorewoocommerce\Api\ProductmapRepositoryInterface $coll,
        \Coditron\Mpmultistorewoocommerce\Helper\FData $helper,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $categoryCollection,
        array $meta = [],
        array $data = []
    ) {
        $this->helper = $helper;
        $this->coll = $coll;
        $ruleId = $this->helper->getAccountDetailsBySellerId()->getFirstItem()->getEntityId();
        $this->collection = $this->coll->getCollectionByRuleId($ruleId)
        ->addFieldToSelect('woocommerce_pro_id')
        ->addFieldToSelect('name')
        ->addFieldToSelect('product_type')
        ->addFieldToSelect('magento_pro_id');
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }
}
