<?php
/**
 * @category  Coditron
 * @package   Coditron_Mpmultistorewoocommerce
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Mpmultistorewoocommerce\Model;

use Coditron\Mpmultistorewoocommerce\Api\Data\ProductmapInterface;

/**
 * ProductmapRepository Class for Shopify Product.
 */
class ProductmapRepository implements \Coditron\Mpmultistorewoocommerce\Api\ProductmapRepositoryInterface
{
    /**
     * Get Resource model
     *
     * @var \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Productmap
     */
    protected $_resourceModel;
    
    /**
     * @var ProductmapFactory
     */
    protected $_productmapFactory;
    
    /**
     * @var \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Productmap\CollectionFactory
     */
    protected $_collectionFactory;

    /**
     * Construct function
     *
     * @param ProductmapFactory $productmapFactory
     * @param \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Productmap\CollectionFactory $collectionFactory
     * @param \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Productmap $resourceModel
     */
    public function __construct(
        ProductmapFactory $productmapFactory,
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Productmap\CollectionFactory $collectionFactory,
        \Coditron\Mpmultistorewoocommerce\Model\ResourceModel\Productmap $resourceModel
    ) {
        $this->_resourceModel = $resourceModel;
        $this->_productmapFactory = $productmapFactory;
        $this->_collectionFactory = $collectionFactory;
    }

    /**
     * Get wwoocommerce sync collection of product by shopify rule id
     *
     * @param int $ruleId
     * @return object
     */
    public function getCollectionByRuleId($ruleId)
    {
        $synProductCollection =  $this->_productmapFactory->create()
            ->getCollection()
            ->addFieldToFilter(
                'rule_id',
                [
                    'eq'=>$ruleId
                ]
            );
        return $synProductCollection;
    }

    /**
     * Get collection by woocommerce product id
     *
     * @param  int $woocommerceProductId
     * @return object
     */
    public function getRecordByWoocommerceProductId($woocommerceProductId)
    {
        $synProductCollection =  $this->_productmapFactory->create()
            ->getCollection()
            ->addFieldToFilter(
                'woocommerce_pro_id',
                [
                    'eq'=>$woocommerceProductId
                ]
            );
        return $synProductCollection;
    }

    /**
     * Get record by magento product id
     *
     * @param  int $mageProductId
     * @return object
     */
    public function getRecordByMageProductId($mageProductId)
    {
        $synProductCollection =  $this->_productmapFactory->create()
            ->getCollection()
            ->addFieldToFilter(
                'magento_pro_id',
                [
                    'eq'=>$mageProductId
                ]
            );
        return $synProductCollection;
    }

    /**
     * Get record by magento product id and account id
     *
     * @param  int $mageProductId
     * @param  int $ruleId
     * @return object
     */
    public function getRecordByMageProductIdAndAccountID($mageProductId, $ruleId)
    {
        $synProductCollection =  $this->_productmapFactory->create()
            ->getCollection()
            ->addFieldToFilter(
                'magento_pro_id',
                [
                    'eq'=>$mageProductId
                ]
            )
            ->addFieldToFilter(
                'rule_id',
                [
                    'eq'=>$ruleId
                ]
            );
        return $synProductCollection;
    }

    /**
     * Get collection by entity ids
     *
     * @param  array $entityIds
     * @return object
     */
    public function getCollectionByIds(array $entityIds)
    {
        $synProductCollection =  $this->_productmapFactory->create()
            ->getCollection()
            ->addFieldToFilter(
                'entity_id',
                [
                    'in'=>$entityIds
                ]
            );
        return $synProductCollection;
    }
}
