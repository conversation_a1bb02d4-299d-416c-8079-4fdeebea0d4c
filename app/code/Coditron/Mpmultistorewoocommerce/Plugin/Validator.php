<?php

namespace Coditron\Mpmultistorewoocommerce\Plugin;

class Validator
{
    /**
     * After Validate
     *
     * @param \Magento\Framework\Data\Form\FormKey\Validator $subject
     * @param object $result
     * @param object $request
     * @return object $result
     */
    public function afterValidate(\Magento\Framework\Data\Form\FormKey\Validator $subject, $result, $request)
    {
        if (strpos($request->getPathInfo(), "mpmultistorewoocommerce") !== false) {
            return true;
        }
        return $result;
    }
}
