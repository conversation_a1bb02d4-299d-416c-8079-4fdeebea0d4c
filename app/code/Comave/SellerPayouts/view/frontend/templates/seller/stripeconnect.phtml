<?php /** @var $block \Comave\SellerPayouts\Block\StripeDetails */ ?>
<?php /** @var $adminImpersonation \Comave\Marketplace\ViewModel\AdminImpersonation */ ?>
<?php
$escaper = $block->escaper;
$adminImpersonation = $block->getData('adminImpersonation');
$customer = $block->getCustomerDetails();
$signInlink = '';
$verificationlink = '';
$isAdminImpersonating = $adminImpersonation->isAdminImpersonating();

if ($customer) {
    if (!empty($customer->getStripeClientId())) {
        $StripeID = $customer->getStripeClientId();
        $verified = $block->getAccountStatus($StripeID);
        if ($verified) {
            $signIn = $block->getAccountSigninlink($StripeID);
            $signInlink = $signIn;
        } else {
            $verificationlink = $block->getAccountverificationlink($StripeID);
        }
    }

    $StripeClientId = $customer->getStripeClientId();
    $emailId = $customer->getStripeAccountEmail();
    $name = $customer->getStripeAccountName();
    $sellerstripecountry = $customer->getSellerStripeCountry();
    $countries = $block->getCountryOptions();
} else {
    $StripeClientId = '';
    $emailId = '';
    $name = '';
    $sellerstripecountry = '';
    $countries = $block->getCountryOptions();
}
?>
<div class="stripe-connect<?= $isAdminImpersonating ? ' disabled' : '' ?>">
        <?php if ($isAdminImpersonating): ?>
            <div class="message message-warning warning">
                <span><?= $escaper->escapeHtml(__('(ADMIN MODE) Stripe settings are disabled while you are logged in as this customer.')) ?></span>
            </div>
        <?php endif; ?>
        <div id="stripe_icon">
            <img src="<?= $escaper->escapeUrl($block->getViewFileUrl('Comave_SellerPayouts::images/stripe_icon.png')) ?>" alt="icon...">
            <p><?= $escaper->escapeHtml(__('Connect with Stripe')) ?></p>
        </div>
        <form id="stripe-connect-form-seller" action="<?= $escaper->escapeUrl($block->getUrl('seller_payouts/seller/verify')) ?>" method="post"<?= $isAdminImpersonating ? ' class="disabled" data-admin-impersonating="true"' : ' data-admin-impersonating="false"' ?>>
            <div>
                <label for="stripe_account_email"><?= $escaper->escapeHtml(__('Email ID')) ?></label>
                <input type="email" id="stripe_account_email" name="stripe_account_email" value="<?= $escaper->escapeHtmlAttr($emailId) ?>" required placeholder="<?= $escaper->escapeHtmlAttr(__('Enter Your Email ID')) ?>"<?= $isAdminImpersonating ? ' disabled' : '' ?> />
            </div>
            <div>
                <label for="stripe_account_name"><?= $escaper->escapeHtml(__('Name')) ?></label>
                <input type="text" id="stripe_account_name" name="stripe_account_name" value="<?= $escaper->escapeHtmlAttr($name) ?>" required placeholder="<?= $escaper->escapeHtmlAttr(__('Enter Your Name')) ?>"<?= $isAdminImpersonating ? ' disabled' : '' ?> />
            </div>
            <div>
                <label for="seller_stripe_country"><?= $escaper->escapeHtml(__('Country')) ?></label>
                <select id="seller_stripe_country" name="seller_stripe_country" required<?= $isAdminImpersonating ? ' disabled' : '' ?>>
                    <option value=""><?= $escaper->escapeHtml(__('Select Your Country')) ?></option>
                    <?php foreach ($countries as $countryCode => $countryName) : ?>
                        <option value="<?= $escaper->escapeHtmlAttr($countryCode) ?>" <?= ($countryCode == $sellerstripecountry) ? 'selected' : '' ?>><?= $escaper->escapeHtml($countryName) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php if (empty($StripeClientId)) : ?>
                <?= $block->getLayout()->createBlock(
                    \Comave\SellerPayouts\Block\StripeButton::class,
                    '',
                    [
                        'data' => [
                            'label' => 'Create Account',
                            'type' => 'submit',
                            'css_class' => 'stripe_create_button',
                            'adminImpersonation' => $adminImpersonation
                        ]
                    ]
                )->toHtml() ?>
            <?php endif; ?>
            <?php if (!empty($verificationlink)) : ?>
                <?= $block->getLayout()->createBlock(
                    \Comave\SellerPayouts\Block\StripeButton::class,
                    '',
                    [
                        'data' => [
                            'label' => 'Verify Account',
                            'url' => $verificationlink,
                            'css_class' => 'stripe_verify_button',
                            'adminImpersonation' => $adminImpersonation
                        ]
                    ]
                )->toHtml() ?>
            <?php endif; ?>
            <?php if (!empty($signInlink)) : ?>
                <?= $block->getLayout()->createBlock(
                    \Comave\SellerPayouts\Block\StripeButton::class,
                    '',
                    [
                        'data' => [
                            'label' => 'Sign In',
                            'url' => $signInlink,
                            'css_class' => 'stripe_signin_button',
                            'adminImpersonation' => $adminImpersonation
                        ]
                    ]
                )->toHtml() ?>
            <?php endif; ?>
        </form>
        <div id="stripe-loader" style="display:none;">
            <img src="<?= $escaper->escapeUrl($block->getViewFileUrl('Comave_SellerPayouts::images/loader.webp')) ?>" alt="Loading...">
            <p><?= $escaper->escapeHtml(__('Verifying... Please wait.')) ?></p>
        </div>
</div>
<script type="text/javascript">
    require(['jquery'], function($) {
        $('#stripe-connect-form-seller').on('submit', function(e) {
            var $form = $(this);
            var isAdminImpersonating = $form.data('admin-impersonating');

            if (isAdminImpersonating) {
                e.preventDefault();
                return false;
            }

            $('.stripe-connect').find('#stripe-loader').show();
        });
    });
</script>

<style>
.stripe-connect.disabled {
    opacity: 0.5;
    pointer-events: none;
}
</style>
