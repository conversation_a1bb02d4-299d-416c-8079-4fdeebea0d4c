<?php /** @var $block \Comave\SellerPayouts\Block\StripeDetails */ ?>
<?php /** @var $adminImpersonation \Comave\Marketplace\ViewModel\AdminImpersonation */ ?>
<?php /** @var $escaper \Magento\Framework\Escaper */ ?>
<?php
$adminImpersonation = $block->getData('adminImpersonation');
$isAdminImpersonating = $adminImpersonation->isAdminImpersonating();
$customer = $block->getCustomerDetails();
$signInlink = '';
$verificationlink = '';

echo "<!-- DEBUG: Customer object: " . ($customer ? 'EXISTS' : 'NULL') . " -->\n";

if ($customer) {
    echo "<!-- DEBUG: Customer exists, checking Stripe Client ID -->\n";
    if (!empty($customer->getStripeClientId())) {
        echo "<!-- DEBUG: Customer has Stripe Client ID: " . $customer->getStripeClientId() . " -->\n";
        $StripeID = $customer->getStripeClientId();
        $verified = $block->getAccountStatus($StripeID);
        echo "<!-- DEBUG: Account verified: " . ($verified ? 'TRUE' : 'FALSE') . " -->\n";
        if ($verified) {
            $signIn = $block->getAccountSigninlink($StripeID);
            $signInlink = $signIn;
            echo "<!-- DEBUG: Sign in link: " . $signInlink . " -->\n";
        } else {
            $verificationlink = $block->getAccountverificationlink($StripeID);
            echo "<!-- DEBUG: Verification link: " . $verificationlink . " -->\n";
        }
    } else {
        echo "<!-- DEBUG: Customer has NO Stripe Client ID -->\n";
    }

    $StripeClientId = $customer->getStripeClientId();
    $emailId = $customer->getStripeAccountEmail();
    $name = $customer->getStripeAccountName();
    $sellerstripecountry = $customer->getSellerStripeCountry();
    $countries = $block->getCountryOptions();
} else {
    echo "<!-- DEBUG: No customer found -->\n";
    $StripeClientId = '';
    $emailId = '';
    $name = '';
    $sellerstripecountry = '';
    $countries = $block->getCountryOptions();
}

echo "<!-- DEBUG: Final StripeClientId: '" . $StripeClientId . "' -->\n";
echo "<!-- DEBUG: Final verificationlink: '" . $verificationlink . "' -->\n";
echo "<!-- DEBUG: Final signInlink: '" . $signInlink . "' -->\n";
?>
<div class="stripe-connect<?= $isAdminImpersonating ? ' disabled' : '' ?>">
        <?php if ($isAdminImpersonating): ?>
            <div class="message message-warning warning">
                <span><?= $escaper->escapeHtml(__('(ADMIN MODE) Stripe settings are disabled while you are logged in as this customer.')) ?></span>
            </div>
        <?php endif; ?>
        <div id="stripe_icon">
            <img src="<?= $escaper->escapeUrl($block->getViewFileUrl('Comave_SellerPayouts::images/stripe_icon.png')) ?>" alt="icon...">
            <p><?= $escaper->escapeHtml(__('Connect with Stripe')) ?></p>
        </div>
        <form id="stripe-connect-form-seller" action="<?= $escaper->escapeUrl($block->getUrl('seller_payouts/seller/verify')) ?>" method="post"<?= $isAdminImpersonating ? ' class="disabled" data-admin-impersonating="true"' : ' data-admin-impersonating="false"' ?>>
            <div>
                <label for="stripe_account_email"><?= $escaper->escapeHtml(__('Email ID')) ?></label>
                <input type="email" id="stripe_account_email" name="stripe_account_email" value="<?= $escaper->escapeHtmlAttr($emailId) ?>" required placeholder="<?= $escaper->escapeHtmlAttr(__('Enter Your Email ID')) ?>"<?= $isAdminImpersonating ? ' disabled' : '' ?> />
            </div>
            <div>
                <label for="stripe_account_name"><?= $escaper->escapeHtml(__('Name')) ?></label>
                <input type="text" id="stripe_account_name" name="stripe_account_name" value="<?= $escaper->escapeHtmlAttr($name) ?>" required placeholder="<?= $escaper->escapeHtmlAttr(__('Enter Your Name')) ?>"<?= $isAdminImpersonating ? ' disabled' : '' ?> />
            </div>
            <div>
                <label for="seller_stripe_country"><?= $escaper->escapeHtml(__('Country')) ?></label>
                <select id="seller_stripe_country" name="seller_stripe_country" required<?= $isAdminImpersonating ? ' disabled' : '' ?>>
                    <option value=""><?= $escaper->escapeHtml(__('Select Your Country')) ?></option>
                    <?php foreach ($countries as $countryCode => $countryName) : ?>
                        <option value="<?= $escaper->escapeHtmlAttr($countryCode) ?>" <?= ($countryCode == $sellerstripecountry) ? 'selected' : '' ?>><?= $escaper->escapeHtml($countryName) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php if (empty($StripeClientId)) : ?>
                <?php echo "<!-- DEBUG: Creating 'Create Account' button -->\n"; ?>
                <?php
                $createButton = $block->getLayout()->createBlock(
                    \Comave\SellerPayouts\Block\StripeButton::class,
                    '',
                    [
                        'data' => [
                            'label' => 'Create Account',
                            'type' => 'submit',
                            'css_class' => 'stripe_create_button'
                        ]
                    ]
                );
                $createButton->setData('adminImpersonation', $adminImpersonation);
                echo "<!-- DEBUG: About to render Create Account button -->\n";
                echo $createButton->toHtml();
                echo "<!-- DEBUG: Create Account button rendered -->\n";
                ?>
            <?php else: ?>
                <?php echo "<!-- DEBUG: NOT creating 'Create Account' button - StripeClientId exists -->\n"; ?>
            <?php endif; ?>
            <?php if (!empty($verificationlink)) : ?>
                <?php echo "<!-- DEBUG: Creating 'Verify Account' button -->\n"; ?>
                <?php
                $verifyButton = $block->getLayout()->createBlock(
                    \Comave\SellerPayouts\Block\StripeButton::class,
                    '',
                    [
                        'data' => [
                            'label' => 'Verify Account',
                            'url' => $verificationlink,
                            'css_class' => 'stripe_verify_button'
                        ]
                    ]
                );
                $verifyButton->setData('adminImpersonation', $adminImpersonation);
                echo "<!-- DEBUG: About to render Verify Account button -->\n";
                echo $verifyButton->toHtml();
                echo "<!-- DEBUG: Verify Account button rendered -->\n";
                ?>
            <?php else: ?>
                <?php echo "<!-- DEBUG: NOT creating 'Verify Account' button - no verification link -->\n"; ?>
            <?php endif; ?>
            <?php if (!empty($signInlink)) : ?>
                <?php echo "<!-- DEBUG: Creating 'Sign In' button -->\n"; ?>
                <?php
                $signInButton = $block->getLayout()->createBlock(
                    \Comave\SellerPayouts\Block\StripeButton::class,
                    '',
                    [
                        'data' => [
                            'label' => 'Sign In',
                            'url' => $signInlink,
                            'css_class' => 'stripe_signin_button'
                        ]
                    ]
                );
                $signInButton->setData('adminImpersonation', $adminImpersonation);
                echo "<!-- DEBUG: About to render Sign In button -->\n";
                echo $signInButton->toHtml();
                echo "<!-- DEBUG: Sign In button rendered -->\n";
                ?>
            <?php else: ?>
                <?php echo "<!-- DEBUG: NOT creating 'Sign In' button - no sign in link -->\n"; ?>
            <?php endif; ?>
        </form>
        <div id="stripe-loader" style="display:none;">
            <img src="<?= $escaper->escapeUrl($block->getViewFileUrl('Comave_SellerPayouts::images/loader.webp')) ?>" alt="Loading...">
            <p><?= $escaper->escapeHtml(__('Verifying... Please wait.')) ?></p>
        </div>
</div>
<script type="text/javascript">
    require(['jquery'], function($) {
        $('#stripe-connect-form-seller').on('submit', function(e) {
            var $form = $(this);
            var isAdminImpersonating = $form.data('admin-impersonating');

            if (isAdminImpersonating) {
                e.preventDefault();
                return false;
            }

            $('.stripe-connect').find('#stripe-loader').show();
        });
    });
</script>

<style>
.stripe-connect.disabled {
    opacity: 0.5;
    pointer-events: none;
}
</style>
