<?php /** @var $block \Comave\SellerPayouts\Block\StripeButton */ ?>
<?php /** @var $adminImpersonation \Comave\Marketplace\ViewModel\AdminImpersonation */ ?>
<?php /** @var $escaper \Magento\Framework\Escaper */ ?>
<?php

$buttonState = $block->getButtonState();
$label = $block->getLabel();
$url = $block->getButtonUrl();
$type = $block->getType();
$cssClass = $block->getCssClass();
?>

<div class="<?= $escaper->escapeHtmlAttr($cssClass) ?>">
    <?php if ($buttonState === 'disabled'): ?>
        <button type="button" disabled><?= $escaper->escapeHtml(__($label)) ?></button>
    <?php else: ?>
        <a href="<?= $escaper->escapeUrl($url) ?>">
            <button type="<?= $escaper->escapeHtmlAttr($type) ?>"><?= $escaper->escapeHtml(__($label)) ?></button>
        </a>
    <?php endif; ?>
</div>
