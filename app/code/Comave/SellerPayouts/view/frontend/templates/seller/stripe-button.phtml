<?php /** @var $block \Comave\SellerPayouts\Block\StripeButton */ ?>
<?php /** @var $adminImpersonation \Comave\Marketplace\ViewModel\AdminImpersonation */ ?>
<?php
$escaper = $block->escaper;
$adminImpersonation = $block->getData('adminImpersonation');
$isAdminImpersonating = $adminImpersonation ? $adminImpersonation->isAdminImpersonating() : false;
$label = $block->getLabel();
$url = $block->getButtonUrl();
$type = $block->getType();
$cssClass = $block->getCssClass();
?>

<div class="<?= $escaper->escapeHtmlAttr($cssClass) ?>">
    <?php if ($isAdminImpersonating): ?>
        <button type="button" disabled><?= $escaper->escapeHtml(__($label)) ?></button>
    <?php elseif ($url): ?>
        <a href="<?= $escaper->escapeUrl($url) ?>">
            <button type="<?= $escaper->escapeHtmlAttr($type) ?>"><?= $escaper->escapeHtml(__($label)) ?></button>
        </a>
    <?php else: ?>
        <button type="<?= $escaper->escapeHtmlAttr($type) ?>"<?= $isAdminImpersonating ? ' disabled' : '' ?>><?= $escaper->escapeHtml(__($label)) ?></button>
    <?php endif; ?>
</div>
