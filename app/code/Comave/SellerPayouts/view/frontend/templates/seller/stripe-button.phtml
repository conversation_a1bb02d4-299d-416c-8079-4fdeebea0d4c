<?php /** @var $block \Comave\SellerPayouts\Block\StripeButton */ ?>
<?php /** @var $adminImpersonation \Comave\Marketplace\ViewModel\AdminImpersonation */ ?>
<?php /** @var $escaper \Magento\Framework\Escaper */ ?>
<?php
echo "<!-- DEBUG: StripeButton template started -->\n";
$adminImpersonation = $block->getData('adminImpersonation');
echo "<!-- DEBUG: AdminImpersonation object: " . ($adminImpersonation ? 'EXISTS' : 'NULL') . " -->\n";
$isAdminImpersonating = $adminImpersonation ? $adminImpersonation->isAdminImpersonating() : false;
echo "<!-- DEBUG: isAdminImpersonating: " . ($isAdminImpersonating ? 'TRUE' : 'FALSE') . " -->\n";
$label = $block->getLabel();
echo "<!-- DEBUG: Label: '" . $label . "' -->\n";
$url = $block->getButtonUrl();
echo "<!-- DEBUG: URL: '" . $url . "' -->\n";
$type = $block->getType();
echo "<!-- DEBUG: Type: '" . $type . "' -->\n";
$cssClass = $block->getCssClass();
echo "<!-- DEBUG: CSS Class: '" . $cssClass . "' -->\n";
?>

<div class="<?= $escaper->escapeHtmlAttr($cssClass) ?>">
    <?php if ($isAdminImpersonating): ?>
        <button type="button" disabled><?= $escaper->escapeHtml(__($label)) ?></button>
    <?php elseif ($url): ?>
        <a href="<?= $escaper->escapeUrl($url) ?>">
            <button type="<?= $escaper->escapeHtmlAttr($type) ?>"><?= $escaper->escapeHtml(__($label)) ?></button>
        </a>
    <?php else: ?>
        <button type="<?= $escaper->escapeHtmlAttr($type) ?>"<?= $isAdminImpersonating ? ' disabled' : '' ?>><?= $escaper->escapeHtml(__($label)) ?></button>
    <?php endif; ?>
</div>
