<?php

namespace Comave\CatalogGraphQl\Plugin;

use Magento\CatalogGraphQl\Model\Resolver\Category\Products;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class RemoveDisabledProducts
{
    /**
     * @param Products $subject
     * @param array $result
     * @param Field $field
     * @param ContextInterface $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array
     */
    public function afterResolve(
        Products $subject,
        array $result,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): array {
        if (isset($result['items'])) {
            foreach ($result['items'] as $key => $item) {
                if (isset($item['status']) && $item['status'] == "2") {
                    unset($result['items'][$key]);
                }
            }
            $result['total_count'] = count($result['items']);
        }

        return $result;
    }
}
