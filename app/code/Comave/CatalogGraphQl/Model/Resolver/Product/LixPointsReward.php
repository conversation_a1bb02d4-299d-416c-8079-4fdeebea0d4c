<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Resolver\Product;

use Comave\CatalogGraphQl\Model\Service\PointsFromProductPrice;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class LixPointsReward implements ResolverInterface
{
    /**
     * @param \Comave\CatalogGraphQl\Model\Service\PointsFromProductPrice $pointsFromProductPrice
     */
    public function __construct(
        private readonly PointsFromProductPrice $pointsFromProductPrice
    ) {
    }

    /**
     * @param \Magento\Framework\GraphQl\Config\Element\Field $field
     * @param $context
     * @param \Magento\Framework\GraphQl\Schema\Type\ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return int
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        $lixPoints = 0;
        $product = $value['model'];
        if (!empty($product) && $product->getId()) {
            $lixPoints = $this->pointsFromProductPrice->calculateLixPoints($product);
        }

        return $lixPoints;
    }
}
