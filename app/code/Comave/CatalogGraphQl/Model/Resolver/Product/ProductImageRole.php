<?php
declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Resolver\Product;

use Comave\CatalogGraphQl\Model\Service\ImageRoles;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;


class ProductImageRole implements ResolverInterface
{
    public function __construct(
        private readonly ImageRoles $imageRoles
    ) {
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (!isset($value['model']) || !isset($value['file'])) {
            throw new LocalizedException(__('"model" and "file" value should be specified'));
        }
        $sku = $value['model']->getData('sku');

        return $this->imageRoles->getImageRoles($sku, $value['file']);
    }
}
