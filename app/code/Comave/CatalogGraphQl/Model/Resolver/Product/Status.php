<?php

declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Resolver\Product;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class Status implements ResolverInterface
{
    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        $product = $value['model'];
        return (!empty($product) && $product->getId()) ? $product->getStatus()
            :  \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED;
    }
}
