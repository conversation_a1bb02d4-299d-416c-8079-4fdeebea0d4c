<?php
declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Resolver\Product;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Directory\Helper\Data;
use Magento\Eav\Model\Entity\Attribute;
use Magento\Eav\Model\Entity\TypeFactory;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\CollectionFactory;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class VisibleAttributes implements ResolverInterface
{
    public const string ATTRIBUTE_TYPE_MULTISELECT = 'multiselect';

    public const string ATTRIBUTE_TYPE_SELECT = 'select';

    public const array ATTRIBUTES_WITH_OPTIONS = [
        self::ATTRIBUTE_TYPE_SELECT,
        self::ATTRIBUTE_TYPE_MULTISELECT
    ];

    public function __construct(
        protected CollectionFactory $attributeCollectionFactory,
        protected ProductRepositoryInterface $productRepository,
        protected Data $directoryHelper,
        protected TypeFactory $typeFactory
    ) {
    }

    /**
     * @inheritDoc
     */
    final public function resolve
    (
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): array
    {
        $data = [];
        $product = $this->productRepository->getById($value['entity_id']);
        $attributes = $this->getAttributes();

        foreach ($attributes as $attribute) {
            $value = $this->getAttributeValue($attribute->getAttributeCode(), $product);

            if (empty($value) || empty($attribute)) {
                continue;
            }

            $values = [$value];
            if (in_array($attribute->getFrontendInput(), self::ATTRIBUTES_WITH_OPTIONS)) {
                $values = $this->getOptionLabels($attribute, $value);
            }

            $data[] = [
                'label' => $attribute->getStoreLabel(),
                'values' => $values,
                'type' => $attribute->getFrontendInput(),
            ];
        }

        return $data;
    }

    /**
     * @return Attribute[]
     */
    final protected function getAttributes(): array
    {
        $entityType = $this->typeFactory->create()->loadByCode(Product::ENTITY);
        $attributeCollection = $this->attributeCollectionFactory->create();
        $attributeCollection
            ->setEntityTypeFilter($entityType)
            ->join('catalog_eav_attribute', 'main_table.attribute_id = catalog_eav_attribute.attribute_id')
            ->addFieldToFilter('catalog_eav_attribute.is_visible_on_front', ['eq' => 1]);

        return $attributeCollection->getItems();
    }

    /**
     * @param string $attributeCode
     * @param ProductInterface $product
     * @return null|string
     */
    protected function getAttributeValue(string $attributeCode, ProductInterface $product): ?string
    {
        if ($attributeCode === ProductInterface::WEIGHT) {
            $weight = $product->getData($attributeCode);

            if (empty($weight)) {
                return null;
            }

            $weight = rtrim($weight, '.0');
            $unit = $this->directoryHelper->getWeightUnit();

            return sprintf('%s %s', $weight, $unit);
        }

        if (in_array($attributeCode, ProductInterface::ATTRIBUTES)) {
            return $product->getData($attributeCode);
        }

        $attributeValue = $product->getCustomAttribute($attributeCode);

        return $attributeValue?->getValue();
    }

    /**
     * @param Attribute $attribute
     * @param mixed $value
     * @return array
     */
    protected function getOptionLabels(Attribute $attribute, $value): array
    {
        $labels = [];
        $options = $attribute->getOptions();

        if ($attribute->getFrontendInput() === self::ATTRIBUTE_TYPE_SELECT) {
            $value = [$value];
        }

        if ($attribute->getFrontendInput() === self::ATTRIBUTE_TYPE_MULTISELECT) {
            $value = explode(',', $value);
        }

        foreach ($value as $selectedOption) {
            foreach ($options as $option) {
                if ($option->getValue() == $selectedOption) {
                    $labels[] = $option->getLabel();

                    continue 2;
                }
            }
        }

        return $labels;
    }
}
