<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Resolver\Product;

use Comave\CatalogGraphQl\Model\Service\Slugger;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\OptionValueProvider;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class Brand implements ResolverInterface
{
    /**
     * @param \Magento\Eav\Model\ResourceModel\Entity\Attribute\OptionValueProvider $attributeOptionValueProvider
     * @param \Comave\CatalogGraphQl\Model\Service\Slugger $slugger
     */
    public function __construct(
        private readonly OptionValueProvider $attributeOptionValueProvider,
        private readonly Slugger $slugger,
    ) {
    }

    /**
     * @param \Magento\Framework\GraphQl\Config\Element\Field $field
     * @param $context
     * @param \Magento\Framework\GraphQl\Schema\Type\ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array|null
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): ?array {
        $product = $value['model'];
        if (!empty($product) && $product->getId()) {
            $brand = $this->attributeOptionValueProvider->get((int)$product->getBrand());
            if (!empty($brand)) {
                return [
                    'value' => $product->getBrand(),
                    'name' => $brand,
                    'slug' => $this->slugger->sluggify($brand),
                ];
            }
        }

        return null;
    }
}
