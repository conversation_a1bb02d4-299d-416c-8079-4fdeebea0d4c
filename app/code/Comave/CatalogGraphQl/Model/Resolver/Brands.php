<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Resolver;

use Comave\CatalogGraphQl\Model\Service\Slugger;
use Magento\Catalog\Model\Product;
use Magento\Eav\Api\AttributeOptionManagementInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

/**
 * Brands field resolver, used for GraphQL request processing.
 */
class Brands implements ResolverInterface
{
    /**
     * @param \Magento\Eav\Api\AttributeOptionManagementInterface $attributeOptionManagement
     * @param \Comave\CatalogGraphQl\Model\Service\Slugger $slugger
     */
    public function __construct(
        private readonly AttributeOptionManagementInterface $attributeOptionManagement,
        private readonly Slugger $slugger,
    ) {
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        $this->validateInput($args);
        $currentPage = $args['currentPage'];
        $pageSize = $args['pageSize'];
        $options = $this->attributeOptionManagement->getItems(Product::ENTITY, 'brand');

        $brands = [];
        foreach ($options as $brand) {
            if (!empty($brand->getValue())) {
                $item = [];
                $item['value'] = $brand->getValue();
                $item['name'] = $brand->getLabel();
                $item['slug'] = $this->slugger->sluggify(strtolower($brand->getLabel()));
                $brands[] = $item;
            }
        }

        $offset = ($currentPage - 1) * $pageSize;
        $result = array_slice($brands, $offset, $pageSize);
        $totalPages = (int)ceil(count($brands) / $pageSize);

        if ($currentPage > $totalPages) {
            throw new GraphQlInputException(
                __(
                    'currentPage value %1 specified is greater than the %2 page(s) available.',
                    [$currentPage, $totalPages]
                )
            );
        }

        return [
            'pageSize' => count($result),
            'currentPage' => $currentPage,
            'totalPages' => $totalPages,
            'totalCount' => count($brands),
            'items' => $result,
        ];
    }

    /**
     * Validate input arguments
     *
     * @param array $args
     * @return void
     * @throws \Magento\Framework\GraphQl\Exception\GraphQlInputException
     */
    private function validateInput(array $args): void
    {
        if ($args['currentPage'] < 1) {
            throw new GraphQlInputException(__('currentPage value must be greater than 0.'));
        }
        if ($args['pageSize'] < 1) {
            throw new GraphQlInputException(__('pageSize value must be greater than 0.'));
        }
    }
}
