<?php

declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Resolver;

use Magento\Catalog\Model\Product;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Webkul\Marketplace\Helper\Data as WebkulMarketplaceHelper;
use Comave\Customer\Service\DefaultShippingAddress;
use Magento\Directory\Model\CountryFactory;
use Magento\Framework\Api\DataObjectHelper;
use Comave\SellerApi\Model\SellerDetailsFactory;
use Comave\SellerApi\Api\SellerDetailsInterface;
use Comave\Customer\Service\AddressFormatter;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

class Seller implements ResolverInterface
{
    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly WebkulMarketplaceHelper $webkulMarketplaceHelper,
        private readonly DefaultShippingAddress $defaultShippingAddress,
        private readonly CountryFactory $countryFactory,
        private readonly DataObjectHelper $dataObjectHelper,
        private readonly SellerDetailsFactory $sellerDetailsFactory,
        private readonly AddressFormatter $addressFormatter,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array[]|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null): ?array
    {
        if (!isset($value['sku']) || !($value['model'] instanceof Product)) {
            return null;
        }

        $product = $value['model'];
        $sellerId = $this->webkulMarketplaceHelper->getSellerIdByProductId($product->getId());
        if (!$sellerId) {
            return $this->sellerDetailsFactory->create()->toArray();
        }

        try {
            $customer = $this->customerRepository->getById($sellerId);

            $defaultShippingAddress = null;
            $defaultShippingAddressId = $customer->getDefaultShipping();
            if ($defaultShippingAddressId) {
                $defaultShippingAddress = $this->defaultShippingAddress->getDefaultShippingAddress(
                    (int)$defaultShippingAddressId
                );
            }

            $address = $this->addressFormatter->format($defaultShippingAddress);

            $sellerDetails = $this->sellerDetailsFactory->create();
            $this->dataObjectHelper->populateWithArray(
                $sellerDetails,
                [
                    SellerDetailsInterface::SELLER_NAME => implode(' ', [$customer->getFirstname(), $customer->getLastname()]),
                    SellerDetailsInterface::SELLER_ADDRESS => $address,
                    SellerDetailsInterface::SELLER_PHONE => $customer->getCustomAttribute('phone_no')?->getValue() ?? '',
                    SellerDetailsInterface::SELLER_EMAIL_INFO => $customer->getCustomAttribute('email_info')?->getValue() ?? ''
                ],
                SellerDetailsInterface::class
            );

            return $sellerDetails->toArray();
        } catch (NoSuchEntityException $e) {
            $this->logger->error('Unable to load seller by id: ' . $sellerId, [$e->getMessage()]);
            return $this->sellerDetailsFactory->create()->toArray();
        } catch (\Throwable $e) {
            $this->logger->error('Seller Resolver Error: ', [$e->getMessage()]);
            return $this->sellerDetailsFactory->create()->toArray();
        }
    }
}

