<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Service;

use Symfony\Component\String\AbstractUnicodeString;
use Symfony\Component\String\Slugger\AsciiSlugger;
use Symfony\Component\String\Slugger\AsciiSluggerFactory;

class Slugger
{
    /**
     * @param \Symfony\Component\String\Slugger\AsciiSluggerFactory $sluggerFactory
     */
    public function __construct(private readonly AsciiSluggerFactory $sluggerFactory
    ) {
    }

    /**
     * @param string $string
     * @return \Symfony\Component\String\AbstractUnicodeString
     */
    public function sluggify(string $string): AbstractUnicodeString
    {
        /** @var AsciiSlugger $slugger */
        $slugger = $this->sluggerFactory->create();

        return $slugger->slug(strtolower($string), '_');
    }
}
