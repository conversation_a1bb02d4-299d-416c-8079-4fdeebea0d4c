<?php
declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Service;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Setup\CategorySetup;
use Magento\Framework\App\ResourceConnection;
use Magento\Catalog\Model\Product\Attribute\Frontend\Image as FrontendImage;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;

class ImageRoles
{
    private AdapterInterface $connection;

    public function __construct(
        ResourceConnection $resource,
        private readonly ProductRepositoryInterface $productRepository
    ) {
        $this->connection = $resource->getConnection();
    }

    final public function getImageRoles(string $sku, string $fileName): array
    {
        try {
            $product = $this->productRepository->get($sku);
        } catch (NoSuchEntityException) {
            throw new GraphQlNoSuchEntityException(__('No such product with sku %1', $sku));
        }

        $imageRoles = [];
        $imageRolesKeys = $this->getImageRolesAttributes();
        foreach ($imageRolesKeys as $imageRole) {
            if ($product->getCustomAttribute($imageRole['attribute_code'])?->getValue() === $fileName) {
                $imageRoles[] = $imageRole['attribute_code'];
            }
        }

        return $imageRoles;
    }

    private function getImageRolesAttributes(): array
    {
        $select = $this->connection->select()
            ->from('eav_attribute', ['attribute_code'])
            ->where("entity_type_id =?", CategorySetup::CATALOG_PRODUCT_ENTITY_TYPE_ID)
            ->where("frontend_model =?", FrontendImage::class)
            ->where("frontend_input =?", 'media_image');

        return $this->connection->fetchAll($select);
    }
}
