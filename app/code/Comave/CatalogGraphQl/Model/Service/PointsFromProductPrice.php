<?php
declare(strict_types=1);


namespace Comave\CatalogGraphQl\Model\Service;

use Comave\Sales\Api\RewardTypeInterface;
use Comave\Sales\Service\Reward\Points\Calculator;
use Exception;
use Magento\Bundle\Pricing\Price\TaxPrice;
use Magento\Catalog\Model\Product;
use Magento\Directory\Model\CurrencyFactory;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

readonly class PointsFromProductPrice
{
    public function __construct(
        private LoggerInterface $logger,
        private TaxPrice $taxPrice,
        private StoreManagerInterface $storeManager,
        private CurrencyFactory $currencyFactory,
        private Calculator $rewardPointsCalculator,
    ) {
    }

    /**
     * Get product price without a tax
     * !!! ($1 spent = 10 points)    ($1 spent = 10 LIXX) !!!
     * As described in the requirements file [ Reward Points - Raffle Mechanics ]
     */
    public function getPriceWithoutTax(Product $product): float
    {
        try {
            $priceWithoutTax = (float)$this->taxPrice->getTaxPrice(
                product: $product,
                price: $product->getFinalPrice(),
                includingTax: false
            );
        } catch (Exception $e) {
            $this->logger->warning(
                "Could not calculate price without tax for a product",
                [
                    'product_id' => $product->getName(),
                    'product_name' => $product->getName(),
                    'exception' => $e->getMessage(),
                ]
            );

            return 0;
        }

        return $priceWithoutTax;
    }

    /**
     * Calculate LIX points based on price without a tax in USD (1 USD = 1 point)
     */
    public function calculateLixPoints(Product $product): int
    {
        $priceWithoutTax = $this->getPriceWithoutTax(product: $product);
        $currentCurrency = $this->storeManager->getStore()->getCurrentCurrencyCode();
        $baseCurrency = 'USD';
        if ($currentCurrency !== $baseCurrency) {
            $currency = $this->currencyFactory->create();
            $rate = $currency->load($baseCurrency)->getRate($currentCurrency);
            if (!$rate) {
                $rate = 1; // @TODO: this should be handled better by a currency rate convertor
            }
            $priceWithoutTax = $priceWithoutTax / $rate;
        }

        return $this->rewardPointsCalculator->calculate(floor($priceWithoutTax), RewardTypeInterface::TYPE_LIX);
    }
}