### comave/module-catalog-graph-ql

# Comave Catalog Graph Ql Module

- Should handle any customization necessary for core Magento Catalog Graph Ql Module and extension of it

### Description

Graph Ql is a query language for your API, and a runtime for executing those queries by using a type system you define for your data. It is a powerful tool for building web APIs that can be used to query data from the database.

- How to get LIX points reward for a product if you buy it
`product {
    lix_points
  }`
