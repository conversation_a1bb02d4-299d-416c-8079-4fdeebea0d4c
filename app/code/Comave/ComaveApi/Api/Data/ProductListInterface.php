<?php

namespace Comave\ComaveApi\Api\Data;

interface ProductListInterface
{
    /**
     * Get product list
     *
     * @return \Magento\Catalog\Api\Data\ProductInterface[]
     */
    public function getProducts();

    /**
     * Set product list
     *
     * @param \Magento\Catalog\Api\Data\ProductInterface[] $products
     * @return $this
     */
    public function setProducts(array $products);
}