<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Comave\ComaveApi\Api;

interface VATRefundManagementInterface
{
     /**
    * Return Traveller Forms filters
    *
    * @param int $customerId
    * @return mixed[]
    */
    public function getTravellerFormFilters($customerId);

     /**
    * Return All Forms
    *
    * @param int $customerId
    * @return mixed[]
    */
    public function getAllForms($customerId);

    /**
    * Return InProgress Forms
    *
    * @param int $customerId
    * @return mixed[]
    */
    public function getInProgressForms($customerId);

    /**
    * Return Refunded Forms
    *
    * @param int $customerId
    * @return mixed[]
    */
    public function getRefundedForms($customerId);

    /**
    * Return Rejected Forms
    *
    * @param int $customerId
    * @return mixed[]
    */
    public function getRejectedForms($customerId);
}

