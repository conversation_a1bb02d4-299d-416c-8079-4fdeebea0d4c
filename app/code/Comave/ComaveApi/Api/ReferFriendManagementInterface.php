<?php
namespace Comave\ComaveApi\Api;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

interface ReferFriendManagementInterface
{
    /**
     * Send invite from customer to request data passed email with message
     *
     * @param int $customerId
     * @return mixed[]
     */
    public function referFriend($customerId);

    /**
     * Return referFriendList
     *
     * @param int $customerId
     * @return mixed[]
     */
    public function referFriendList($customerId);

    /**
     * Return referral code
     * @return mixed
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function referralCode($customerId);
}
