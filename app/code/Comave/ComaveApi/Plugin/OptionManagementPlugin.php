<?php

namespace Comave\ComaveApi\Plugin;

use Magento\Catalog\Model\Product\Attribute\OptionManagement as Optmanagement;
use Magento\Swatches\Model\ResourceModel\Swatch\CollectionFactory;

class OptionManagementPlugin
{  
    protected $swatchCollectionFactory;

    public function __construct(
        CollectionFactory $swatchCollectionFactory
    ) {
        $this->swatchCollectionFactory = $swatchCollectionFactory;
    }

    public function afterGetItems(Optmanagement $subject, array $result, $attributeCode)
    {
        foreach ($result as $attribute) {
            $optionId = $attribute['value'];            
            $itemData = [];             
            $itemData['label'] = $attribute['label'];
            $itemData['value'] = $attribute['value'];            
            $swatchCollection = $this->swatchCollectionFactory->create();
            $swatchCollections = $swatchCollection->addFieldToFilter('option_id', ['eq' => $optionId]);            
            foreach ($swatchCollections as $swatch) {
                $itemData['swatch_value'] = $swatch->getValue();
            }
            $resultData[] = $itemData;
        }        
        return $resultData;
        }
}
