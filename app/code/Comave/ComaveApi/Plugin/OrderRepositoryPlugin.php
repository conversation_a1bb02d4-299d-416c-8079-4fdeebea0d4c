<?php
namespace Comave\ComaveApi\Plugin;

use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Api\Data\OrderExtensionFactory;
use Magento\Framework\App\ResourceConnection;

class OrderRepositoryPlugin
{
    /**
     * @var OrderExtensionFactory
     */
    private $orderExtensionFactory;

    /**
     * @var ResourceConnection
     */
    private $resourceConnection;

    public function __construct(
        OrderExtensionFactory $orderExtensionFactory,
        ResourceConnection $resourceConnection
    ) {
        $this->orderExtensionFactory = $orderExtensionFactory;
        $this->resourceConnection = $resourceConnection;
    }

    public function afterGet(
        OrderRepositoryInterface $subject,
        OrderInterface $order
    ) {
        $extensionAttributes = $order->getExtensionAttributes();
        if ($extensionAttributes === null) {
            $extensionAttributes = $this->orderExtensionFactory->create();
        }

        $websiteName = $this->getWebsiteName($order->getStoreId());
        $extensionAttributes->setWebsiteName($websiteName);
        $order->setExtensionAttributes($extensionAttributes);

        return $order;
    }

    public function afterGetList(
        OrderRepositoryInterface $subject,
        \Magento\Sales\Api\Data\OrderSearchResultInterface $searchResult
    ) {
        foreach ($searchResult->getItems() as $order) {
            $this->afterGet($subject, $order);
        }
        return $searchResult;
    }

    private function getWebsiteName($storeId)
    {
        $connection = $this->resourceConnection->getConnection();
        $select = $connection->select()
            ->from($this->resourceConnection->getTableName('store'), 'website_id')
            ->where('store_id = :store_id');
        $websiteId = $connection->fetchOne($select, ['store_id' => $storeId]);

        $select = $connection->select()
            ->from($this->resourceConnection->getTableName('store_website'), 'name')
            ->where('website_id = :website_id');
        return $connection->fetchOne($select, ['website_id' => $websiteId]);
    }
}
