<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Comave\ComaveApi\Model;

use Comave\TravellerInfo\Model\TravellerInfoFactory as TravFormModel;
use Magento\Framework\App\RequestInterface;

class VATRefundManagement implements \Comave\ComaveApi\Api\VATRefundManagementInterface
{
    /**
     * @var TravFormModel
     */
    protected $travFormModel;

    protected $request;
    
    public function __construct( 
        TravFormModel $travFormModel,
        RequestInterface $request
    ){        
        $this->travFormModel = $travFormModel;
        $this->request = $request;
    }

    public function getTravellerFormFilters($customerId){
        $requestBody = $this->request->getContent();
        $data = json_decode($requestBody, true);
        $country = $data['country'] ?? '';
        $filterDateFrom = $data['trip-start'] ?? '';
        $filterDateTo = $data['trip-end'] ?? '';
        $status1 = $data['in_progress'] ?? '';
        $status2 = $data['refunded'] ?? '';
        $store = $data['store'] ?? '';
        $collection = $this->travFormModel->create()->getCollection()
        ->addFieldToFilter(
            'traveller_id',
            ['eq' => $customerId]
        );

        if(!empty($status1) || !empty($status2) || !empty($status3)){
            $collection->addFieldToFilter(
                'status',
                [['eq' => $status1],['eq' => $status2]]
            );
        }

        if($country){
            if($country == "All"){
                $collection->addFieldToFilter(
                    'store_country',
                    ['neq' => NULL]
                );
            }else{
                $collection->addFieldToFilter(
                    'store_country',
                    ['eq' => $country]
                );
            }
        }

        if($filterDateFrom != NULL && $filterDateTo!= NULL){
            $collection->addFieldToFilter(
                'date_of_sales',
                ['datetime' => true, 'from' => $filterDateFrom, 'to' => $filterDateTo]
            );
        }

        if($store){
            $collection = $this->travFormModel->create()->getCollection()
                        ->addFieldToFilter(
                            'traveller_id',
                            ['eq' => $customerId]
                        )->addFieldToFilter(
                            ['store_name','barcode'],
                            [['like' => '%'.$store.'%'],['like' => '%'.$store.'%']]
                        );
            return $collection->getData();
        }
        if($collection->count() == 0){
            return null;
        }
        else{
            return $collection->getData();
        }       
    }

     public function getAllForms($customerId){
        $collection = $this->travFormModel->create()->getCollection()
        ->addFieldToFilter(
            'traveller_id',
            ['eq' => $customerId]
        );

        $alldata = $collection->getData();
        $count = $collection->count();

        return ['alldata' => $alldata, 'count' => $count];
    }
    public function getInProgressForms($customerId)
    {
        $collection = $this->travFormModel->create()->getCollection()
        ->addFieldToFilter(
            'traveller_id',
            ['eq' => $customerId]
        )->addFieldToFilter(
            'status',
            [['eq' => 'FORM_SUBMITTED'],['eq' => 'FORM_COLLECTED']]
        )->addFieldToFilter(
            'form_status',
            ['neq' => 'rejected']
        );
        $inprogressdata = $collection->getData();
        $count = $collection->count();

        return ['inprogressdata' => $inprogressdata, 'count' => $count];
    }

    public function getRefundedForms($customerId)
    {
        $collection = $this->travFormModel->create()->getCollection()
        ->addFieldToFilter(
            'traveller_id',
            ['eq' => $customerId]
        )->addFieldToFilter(
            'status',
            ['eq' => 'FORM_REFUNDED']
        );

        $refundeddata = $collection->getData();
        $count = $collection->count();

        return ['refundeddata' => $refundeddata, 'count' => $count];
    }

    public function getRejectedForms($customerId)
    {
        $collection = $this->travFormModel->create()->getCollection()
        ->addFieldToFilter(
            'traveller_id',
            ['eq' => $customerId]
        )->addFieldToFilter(
            'form_status',
            ['eq' => 'rejected']
        );

        $rejecteddata = $collection->getData();
        $count = $collection->count();

        return ['rejecteddata' => $rejecteddata, 'count' => $count];
    }
}