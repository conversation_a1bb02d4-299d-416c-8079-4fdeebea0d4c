<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Comave\ComaveApi\Model;

use AllowDynamicProperties;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\Search\FilterGroupBuilder;
use Magento\Framework\Api\Search\FilterGroupBuilderFactory;
use Magento\Framework\App\RequestInterface;
use Magento\Store\Model\StoreManagerInterface;

#[AllowDynamicProperties]
class ProfilePicManagement implements \Comave\ComaveApi\Api\ProfilePicManagementInterface
{
    /**
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;

    /**
     * @var FilterBuilder
     */
    protected $filterBuilder;

    /**
     * @var FilterGroupBuilderFactory
     */
    protected $filterGroupBuilderFactory;

    /**
     * File Uploader variable
     *
     * @var [\Magento\MediaStorage\Model\File\UploaderFactory]
     */
    protected $_fileUploaderFactory;

    /**
     * Media Config variable
     *
     * @var \Magento\Catalog\Model\Product\Media\Config
     */
    private $mediaConfig;

    /**
     * Return data variable
     *
     * @var array
     */
    private $returnArrayData;

    protected $request;

    /**
     * File System variable
     *
     * @var [Filesystem]
     */
    protected $filesystem;

     /**
     * @var \Magento\Framework\Json\Helper\Data $jsonHelper
     */
    protected $jsonHelper;

    /**
     * @var \Magento\Framework\Filesystem\Driver\File
     */
    protected $driverFile;

    /**
     * Initialization
     *
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     * @param CustomerRepositoryInterface $customerRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param FilterBuilder $filterBuilder
     * @param FilterGroupBuilderFactory $filterGroupBuilderFactory
     *
     * @return void
     */
    public function __construct(
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        CustomerRepositoryInterface $customerRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterBuilder $filterBuilder,
        FilterGroupBuilderFactory $filterGroupBuilderFactory,
        RequestInterface $request,
        \Magento\MediaStorage\Model\File\UploaderFactory $fileUploaderFactory,
        Filesystem $filesystem,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Magento\Framework\Filesystem\Driver\File $driverFile,
        StoreManagerInterface $storeManager
    ) {
        $this->customerFactory = $customerFactory;
        $this->customerRepository = $customerRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->filterBuilder = $filterBuilder;
        $this->filterGroupBuilderFactory = $filterGroupBuilderFactory;
        $this->_mediaDirectory = $filesystem->getDirectoryWrite(DirectoryList::MEDIA);
        $this->request = $request;
        $this->_fileUploaderFactory = $fileUploaderFactory;
        $this->filesystem = $filesystem;
        $this->jsonHelper = $jsonHelper;
        $this->driverFile = $driverFile;
        $this->storeManager = $storeManager;
        $header = $this->request->getHeader('content-type');
        $postValues = $this->request->getPostValue();
        if ($header == 'application/json') {
            $postValues = $this->driverFile->fileGetContents('php://input');
            if ($postValues) {
                $postValues = $this->jsonHelper->jsonDecode($postValues);
            }
        }
        $this->request->setPostValue($postValues);
        $this->returnArrayData["success"] = false;
        $this->returnArrayData["message"] = "";
    }

    /**
     * Upload Customer Profile Picture
     *
     * @param int $customerId
     * @return void
     */
    public function uploadCustomerProfilePicture($customerId)
    {
        try {
            $customer = $this->customerRepository->getById($customerId);
            $files = (array) $this->request->getFiles('profile_picture');
            if ($this->request->getMethod() == "POST" && !empty($files)) {
                $target = $this->_mediaDirectory->getAbsolutePath('customer');
                $fileUploader = $this->_fileUploaderFactory->create(['fileId' => 'profile_picture']);
                $fileUploader->setAllowedExtensions(['gif', 'jpg', 'png', 'jpeg']);
                $fileUploader->setFilesDispersion(true);
                $fileUploader->setAllowRenameFiles(true);
                $this->returnArrayData = $fileUploader->save($target);
                unset($this->returnArrayData['tmp_name']);
                unset($this->returnArrayData['path']);
                $this->returnArrayData['file'] = $this->returnArrayData['file'];
                $encodeurl =  base64_encode($this->returnArrayData['file']);
                $imageurl = $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_LINK). 'viewfile/avatar/view/image/'. $encodeurl;
                $this->returnArrayData['url'] = $imageurl;
                $this->returnArrayData["message"] = __('Profile picture has been successfully uploaded');
                $this->returnArrayData["success"] = true;
                $anotherArray[] = $this->returnArrayData;
                 return $anotherArray;
            } else {
                $this->returnArrayData["message"] = __("Invalid Request.");
                return $this->returnArrayData;
            }
        } catch (\Exception $e) {
            $this->returnArrayData["message"] = $e->getMessage();
            return $e->getMessage();
        }
    }
}
