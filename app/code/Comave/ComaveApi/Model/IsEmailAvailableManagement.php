<?php
namespace Comave\ComaveApi\Model;

use Comave\ComaveApi\Api\IsEmailAvailableManagementInterface;
use Magento\Customer\Model\Customer;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory as CustomerCollectionFactory;

class IsEmailAvailableManagement implements IsEmailAvailableManagementInterface
{
    /**
     * @var CustomerCollectionFactory
     */
    protected $customerCollectionFactory;

    /**
     * @var Customer
     */
    protected $customerModel;

    public function __construct(
        Customer $customerModel,
        CustomerCollectionFactory $customerCollectionFactory
    ) {
        $this->customerModel = $customerModel;
        $this->customerCollectionFactory = $customerCollectionFactory;
    }

    public function isEmailAvailable(string $customerEmail): bool
    {
        $customerCollection = $this->customerCollectionFactory->create();
        $customerCollection->addFieldToFilter('email', $customerEmail);

        if ($customerCollection->getSize() > 0) {
            return true;
        }

        return false;
    }
}
