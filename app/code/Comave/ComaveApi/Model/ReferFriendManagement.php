<?php
declare(strict_types=1);

namespace Comave\ComaveApi\Model;

use AllowDynamicProperties;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Escaper;
use Magento\Invitation\Model\Config as InvitationConfig;
use Comave\Referfriend\Model\Invitation;
use Comave\Referfriend\Model\InvitationFactory;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Invitation\Model\Invitation\HistoryFactory;
use Magento\Framework\App\ResourceConnection;
use Comave\Logger\Model\ComaveLogger;

/**
 * Terms clarification
 *
 * Referral: The act or process of transferring something to another, of sending by reference, or referring.
 * Referrer (Brand Advocate/ Ambassador): a person who makes a referral/refers another (their friends, family and acquaintances).
 * Referee (Friend): a person who is invited to a referral/referred by another.
 */
#[AllowDynamicProperties]
class ReferFriendManagement implements \Comave\ComaveApi\Api\ReferFriendManagementInterface
{
    protected $storeManagerInterface;
    private $_objectManager;
    private $invitation;

    private $comaveLogger;

    public const string DEFAULT_INVITE_MESSAGE = 'Your friend sent you an invite to join our store.';

    public function __construct(
        StoreManagerInterface $storeManagerInterface,
        RequestInterface $request,
        InvitationConfig $config,
        InvitationFactory $invitationFactory,
        Invitation $invitation,
        EncryptorInterface $encryptor,
        CustomerFactory $customerFactory,
        CustomerRepositoryInterface $customerRepository,
        \Comave\Referfriend\Model\Invitation\Status $status,
        \Magento\Framework\Stdlib\DateTime $dateTime,
        HistoryFactory $historyFactory,
        ResourceConnection $resourceConnection,
        Escaper $escaper,
        ComaveLogger $comaveLogger
    ) {
        $this->storeManagerInterface = $storeManagerInterface;
        $this->request = $request;
        $this->config = $config;
        $this->invitationFactory = $invitationFactory;
        $this->invitation = $invitation;
        $this->encryptor = $encryptor;
        $this->customerFactory = $customerFactory;
        $this->customerRepository = $customerRepository;
        $this->status = $status;
        $this->dateTime = $dateTime;
        $this->historyFactory = $historyFactory;
        $this->resourceConnection = $resourceConnection;
        $this->escaper = $escaper;
        $this->comaveLogger = $comaveLogger;
    }

    /**
     * @deprecated Used from REST API webapi.xml
     * @param $customerId
     * @return array[]|void
     * @throws \Zend_Validate_Exception
     */
    public function referFriend($customerId)
    {
       $data = json_decode($this->request->getContent(), true);
        if ($data) {
                $customer = $this->customerFactory->create()->load($customerId);
                $message = isset($data['message']) ? $data['message'] : '';
                if (!$this->config->isInvitationMessageAllowed()) {
                    $message = '';
                }
                $invPerSend = $this->config->getMaxInvitationsPerSend();
                $attempts = 0;
                $customerExists = 0;
                foreach ($data['email'] as $email) {
                    $attempts++;
                    if (!\Zend_Validate::is($email, 'EmailAddress')) {
                        continue;
                    }
                    if ($attempts > $invPerSend) {
                        continue;
                    }
                    try {
                        $this->referFriendSend($customer, $email, $message);
                        $responseData[$email] = 'Invitation sent';
                    } catch (\Magento\Framework\Exception\AlreadyExistsException $e) {
                        $customerExists++;
                        $message = "We did not sent invitation due to email address being already invited.";
                        $responseData['failure'] = $message;
                    } catch (\Magento\Framework\Exception\LocalizedException|\Exception $e) {
                        $message = "Something went wrong while sending an email.";
                        $responseData['failure'] = $message;
                        return ['data' => $responseData];
                        //return $e->getMessage();
                    }
                }
                if ($customerExists) {
                    $message = "We did not sent invitation to some emails, due to email address being already invited.";
                    $responseData['failure'] = $message;
                    return ['data' => $responseData];
                    //return $message;
                }
                $responseData['success'] = "Email send successfully";
                return ['data' => $responseData];
        }
    }

    /**
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function referFriendSend($customer, string $email, string $message = ''): void
    {
        if (is_numeric($customer)) {
            $customer = $this->customerRepository->getById($customer);
        }

        /** @var \Comave\Referfriend\Model\Invitation $invitation */
        $invitation = $this->invitationFactory->create();
        $invitation->setData(
            [
                'email' => $email,
                'customer' => $customer,
                'message' => $message ?? __(self::DEFAULT_INVITE_MESSAGE),
            ]
        )->save();

        if (!$invitation->sendInvitationEmailForHeadlessFrontend()) {
            $this->comaveLogger->error('sendInvitationEmailForHeadlessFrontend() returned false');
            // not \Magento\Framework\Exception\LocalizedException intentionally
            throw new \Exception('');
        }
    }

    public function referFriendList($customerId)
    {
        $friendList = $this->invitationFactory->create()->getCollection()
                ->addOrder('invitation_id', \Magento\Framework\Data\Collection::SORT_ORDER_DESC)
                ->loadByCustomerId($customerId);
        return $friendList->getData();
    }

    /**
     * @param $websiteId
     * @param $referralId int The newly created customer id
     * @param $referralEmail string The newly created customer email
     * @param string|null $referralCode The referral code
     * @return void
     */
    public function acceptInvitation($websiteId, $referralId, $referralEmail, ?string $referralCode)
    {
        // Load invitation or create new
        $invitation = $this->invitationFactory->create()->getCollection()
        ->addFieldToFilter('email', $referralEmail)
        ->getFirstItem();

        $this->comaveLogger->info('Loaded/created invitation', $invitation->getData());
        $this->comaveLogger->info('Referral code received: ' . $referralCode);

        // In case new invitation via referralCode we have no email and no referrer customerId
        if (empty($invitation->getEmail()) && !empty($referralCode)) {

            $this->comaveLogger->info('Invitation by referral code only, referral code: ' . $referralCode);

            // set the new customer (referee) email on invitation
            $invitation->setEmail($referralEmail);
            // determine inviter customerId
            //TODO: set customerId but we might also need to setCustomer on invitation, see \Comave\Referfriend\Model\Invitation::beforeSave, for groupId, or setGroupId on invitation
            $customerId = Invitation::getCustomerIdFromReferralCode($referralCode);
            $customer = $this->customerRepository->getById($customerId);
            $invitation->setCustomer($customer);

            //TODO: if needed, we can add a flag field to Invitation model, so we know this invitation came via referralCode or a new status ACCEPTED_VIA_CODE
            //  otherwise, we can determine this by checking invitation history, as there will be a single status of ACCEPTED directly
        }

        $this->comaveLogger->info('Updating invitation status for ' . $invitation->getEmail());

        // Update invitation status
        $invitation->setReferralId(
                $referralId
            )->setStatus(
                Invitation\Status::STATUS_ACCEPTED
            )->setSignupDate(
                $this->dateTime->formatDate(time())
            )->setJustAccepted(
                true
            )->save();

        // Update invitation status history - this is not needed, it will be automatically done by \Comave\Referfriend\Model\Invitation::afterSave
        //        $invitationHistory = $this->historyFactory->create()->getCollection()
        //            ->addFieldToFilter('invitation_id', $invitation->getInvitationId())
        //            ->setOrder('history_id', 'DESC')
        //            ->getFirstItem();
        //        $invitationHistory->setStatus(
        //                Invitation\Status::STATUS_ACCEPTED
        //            )->save();

        // Insert invitation track
        if ($invitation->getCustomerId()) {
             $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('magento_invitation_track');
            // Insert data
            $connection->insert(
                $tableName,
                [
                    'inviter_id' => $invitation->getCustomerId(),
                    'referral_id' => $referralId
                ]
            );
        }
    }

    /**
     * @param $customerId
     * @return string
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function referralCode($customerId): string
    {
        $customer = $this->customerRepository->getById($customerId);
        return Invitation::generateReferralCode($customer->getId(), $customer->getEmail());
    }
}
