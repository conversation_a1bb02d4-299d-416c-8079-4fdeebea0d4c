<?php
declare(strict_types=1);

namespace Comave\ComaveApi\Model;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;

class ConfigProvider extends AbstractHelper
{
    public function __construct(
        Context $context,
        protected \Magento\Store\Model\StoreManagerInterface $storeManager
    ) {
        parent::__construct($context);
    }

    public function getStoreUrl(): string
    {
        return $this->storeManager->getStore()->getBaseUrl();
    }

    /**
     * Check if 'pub' directory should be removed from media URLs.
     *
     * @return bool
     */
    public function isRemovePub()
    {
        return (bool)$this->scopeConfig->getValue(
            'comave/general/remove_pub',
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get the base media URL.
     *
     * @return string
     */
    public function getMediaUrl()
    {
        $storeUrl = $this->getStoreUrl();
        $mediaUrl = $storeUrl . 'pub/media/';

        if ($this->isRemovePub()) {
            $mediaUrl = $storeUrl . 'media/';
        }

        return $mediaUrl;
    }
}
