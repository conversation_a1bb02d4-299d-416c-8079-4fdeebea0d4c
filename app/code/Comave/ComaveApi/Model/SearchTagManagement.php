<?php

namespace Comave\ComaveApi\Model;

use Comave\ComaveApi\Api\SearchTagManagementInterface;
use Magento\Search\Model\ResourceModel\Query\CollectionFactory as QueryCollectionFactory;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Store\Model\ResourceModel\Website\CollectionFactory as WebsiteCollectionFactory;

class SearchTagManagement implements SearchTagManagementInterface
{
    protected $queryCollectionFactory;

    protected $storeManager;

    protected $websiteCollectionFactory;

    public function __construct(
        QueryCollectionFactory $queryCollectionFactory,
        StoreManagerInterface $storeManager,
        WebsiteCollectionFactory $websiteCollectionFactory
    )
    {
        $this->queryCollectionFactory = $queryCollectionFactory;
        $this->storeManager = $storeManager;
        $this->websiteCollectionFactory = $websiteCollectionFactory;
    }

    /**
     * {@inheritdoc}
     */
    public function getSearchTagList()
    {
        $searchTag = [];
        $searchTagData = [];
        $storeId = $this->storeManager->getStore()->getId();
        $queryCollection = $this->queryCollectionFactory->create();
        $queryCollection->addFieldToFilter('store_id', $storeId);
        $queryCollection->setOrder('popularity', 'DESC');
        $queryCollection->setPageSize(10);
        foreach ($queryCollection as $queryCollections) {
            $searchTag = $queryCollections->getQueryText();
            $searchTagData[] = $searchTag;
        }
        return $searchTagData;
    }
}
