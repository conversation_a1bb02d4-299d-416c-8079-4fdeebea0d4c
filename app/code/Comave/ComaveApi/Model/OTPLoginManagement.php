<?php
namespace Comave\ComaveApi\Model;

use AllowDynamicProperties;
use Magento\Framework\App\RequestInterface;
use Comave\ComaveApi\Api\OTPLoginManagementInterface;
use Webkul\FirebaseOTPLogin\Api\Data\OtpInterface;
use Webkul\FirebaseOTPLogin\Api\OtpRepositoryInterface;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Integration\Model\Oauth\Token\RequestThrottler;
use Magento\Framework\App\ObjectManager;
use Magento\Integration\Model\UserToken\UserTokenParametersFactory;
use Magento\Integration\Api\UserTokenIssuerInterface;
use Magento\Integration\Api\UserTokenRevokerInterface;
use Magento\Integration\Model\ResourceModel\Oauth\Token\CollectionFactory as TokenCollectionFactory;
use Magento\Framework\Event\ManagerInterface;

#[AllowDynamicProperties]
class OTPLoginManagement implements OTPLoginManagementInterface
{
    public const XML_PATH_OTP_EMAIL_LOGIN = 'firebase_otp/emailsettings/otp_notification_at_login';

    public function __construct(
        RequestInterface $request,
        \Magento\Framework\Translate\Inline\StateInterface $inlineTranslation,
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Webkul\FirebaseOTPLogin\Helper\FormKey\Validator $formKeyValidator,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Framework\Controller\Result\JsonFactory $resultJson,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Magento\Customer\Model\Session $customerSession,
        \Webkul\FirebaseOTPLogin\Helper\Data $helper,
        OtpRepositoryInterface $otpRepositoryInterface,
        OtpInterface $otpInterface,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        \Magento\Framework\Serialize\SerializerInterface $serialize,
        \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory $customerCollectionFactory,
        AccountManagementInterface $accountManagement,
        RequestThrottler $requestThrottler,
        ManagerInterface $eventManager = null,
        UserTokenParametersFactory $tokenParamsFactory = null,
        UserTokenIssuerInterface $tokenIssuer = null,
        UserTokenRevokerInterface $tokenRevoker = null
    ) {
        $this->request = $request;
        $this->inlineTranslation = $inlineTranslation;
        $this->transportBuilder = $transportBuilder;
        $this->formKeyValidator = $formKeyValidator;
        $this->scopeConfig = $scopeConfig;
        $this->resultJson = $resultJson;
        $this->storeManager = $storeManager;
        $this->customerFactory = $customerFactory;
        $this->customerSession = $customerSession;
        $this->helper = $helper;
        $this->otpRepositoryInterface = $otpRepositoryInterface;
        $this->otpInterface = $otpInterface;
        $this->date = $date;
        $this->serialize = $serialize;
        $this->customerCollectionFactory = $customerCollectionFactory;
        $this->accountManagement = $accountManagement;
        $this->requestThrottler = $requestThrottler;
        $this->eventManager = $eventManager ?: ObjectManager::getInstance()->get(ManagerInterface::class);
        $this->tokenParametersFactory = $tokenParamsFactory ?: ObjectManager::getInstance()->get(UserTokenParametersFactory::class);
        $this->tokenIssuer = $tokenIssuer ?: ObjectManager::getInstance()->get(UserTokenIssuerInterface::class);
        $this->tokenRevoker = $tokenRevoker ?: ObjectManager::getInstance()->get(UserTokenRevokerInterface::class);
    }

    public function sendLoginOTP()
    {
        $requestData = json_decode($this->request->getContent(), true);
        $username = $requestData['username'] ?? null;
        $password = $requestData['password'] ?? null;
        $resend = $requestData['resend'] ?? null;

        if (filter_var($username, FILTER_VALIDATE_EMAIL)) {
            $email = $username;
            $mobile = null;
        } else {
            $email = null;
            $mobile = $username;
        }

        if ($email) {
            $accountExists = $this->checkExistingAccount($email);
            if (!$accountExists) {
                $respData['status'] = false;
                $respData['message'] = 'No account exists with this email.';
                return ['data' => $respData];
            }
        }

        if ($mobile) {
            $checkIfPhoneNumberExist = $this->customerCollectionFactory->create()
                ->addAttributeToFilter('default_phone_number', ['eq' => $mobile]);

            if (empty($checkIfPhoneNumberExist->getData())) {
                $respData['status'] = false;
                $respData['message'] = 'Phone number does not exist';
                return ['data' => $respData];
            }
        }

         try {
            $this->accountManagement->authenticate($username, $password);
        } catch (\Exception $e) {
           $respData['status'] = false;
                $respData['message'] = 'The account sign-in was incorrect or your account is disabled temporarily. Please wait and try again later.';
                return ['data' => $respData];
        }
        $password = rand(100000, 999999);
        $collection = $this->otpRepositoryInterface->getByEmail($email);
        $date = $this->date->gmtDate();

        if ($email) {
            if (is_array($collection->getData())) {
                $collection->setEmail($email);
                $collection->setOtp($password);
                $collection->setCreatedAt($date);
                $collection->save($collection);
            } else {
                $this->otpInterface->setEmail($email);
                $this->otpInterface->setOtp($password);
                $this->otpRepositoryInterface->save($this->otpInterface);
            }

            $response = $this->sendOTPToUser($email, $password);
            $otpMedium = 'Email ID';
        } else {
            $response = $this->sendOTPToMobile($mobile, $password);
            $otpMedium = 'Mobile Number';
        }

        if ($response['error']) {
            $errorMessage = $response['message'] ?? "";

            if ($otpMedium == "Email ID" && $errorMessage == 'Unable to send mail') {
                $respData['status'] = false;
                $respData['message'] = 'Unable to send mail.';
                return ['data' => $respData];
            }

            if (strpos($errorMessage, 'Unable to send mail') === false) {
                $respData['status'] = false;
                $respData['message'] = 'Unable to send OTP. Please try again later.';
                return ['data' => $respData];
            } elseif (strpos($errorMessage, 'unverified numbers') === false) {
                $respData['status'] = false;
                $respData['message'] = 'The phone number is not verified.';
                return ['data' => $respData];
            } else {
                $respData['status'] = false;
                $respData['message'] = 'Unable to send OTP. Please try again later.';
                return ['data' => $respData];
            }
        } else {
            $successMessage = $resend
                ? __("A new OTP has been sent to your registered %1. Please enter the OTP.", $otpMedium)
                : __("Please Enter the OTP sent to your registered %1", $otpMedium);
            $respData['status'] = true;
            $respData['message'] = $successMessage;
            return ['data' => $respData];
        }
    }

    private function sendOTPToUser($username, $password)
    {
        $emailTempVariables = [];
        $senderInfo = [];
        $receiverInfo = [];
        $storeScope = \Magento\Store\Model\ScopeInterface::SCOPE_STORE;
        $senderEmail = $this->scopeConfig->getValue('trans_email/ident_support/email', $storeScope);
        $senderName = $this->scopeConfig->getValue('trans_email/ident_support/name', $storeScope);
        $emailTempVariables['password'] = $password;
        $senderInfo['email'] = $senderEmail;
        $senderInfo['name'] = $senderName;
        $receiverInfo['email'] = $username;
        $receiverInfo['name'] = "User";

        $emailTempVariables['time_to_expire'] = $this->helper->getOtpTimeToExpireString();

        try {
            $this->template = $this->getTemplateId(self::XML_PATH_OTP_EMAIL_LOGIN);
            $this->inlineTranslation->suspend();
            $this->generateTemplate($emailTempVariables, $senderInfo, $receiverInfo);
            $transport = $this->transportBuilder->getTransport();
            $transport->sendMessage();
            $this->inlineTranslation->resume();
            $result = ['error' => false, 'message' => 'Successfully sent'];
        } catch (\Exception $e) {
            $message = $e->getMessage();
            $result = ['error' => true, 'message' => "Unable to send mail"];
        }
        return $result;
    }

    private function checkExistingAccount($email)
    {
        $accountExists = $this->customerFactory->create()->getCollection()
            ->addFieldToFilter('email', ['eq' => $email])
            ->getSize();
        return $accountExists > 0;
    }

    private function getTemplateId($xmlPath)
    {
        return $this->getConfigValue($xmlPath, $this->getStore()->getStoreId());
    }

    private function getConfigValue($path, $storeId)
    {
        return $this->scopeConfig->getValue(
            $path,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    private function getStore()
    {
        return $this->storeManager->getStore();
    }

    private function generateTemplate($emailTemplateVariables, $senderInfo, $receiverInfo)
    {
        $this->transportBuilder
            ->setTemplateIdentifier($this->template)
            ->setTemplateOptions(
                [
                    'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
                    'store' => $this->storeManager->getStore()->getId(),
                ]
            )
            ->setTemplateVars($emailTemplateVariables)
            ->setFrom($senderInfo)
            ->addTo($receiverInfo['email'], $receiverInfo['name']);
        return $this;
    }

 public function verifyLoginOTP()
{
    $requestData = json_decode($this->request->getContent(), true);
    $username = $requestData['username'];
    $otp = $requestData['user_otp'];

    if (!isset($requestData['username']) || !isset($requestData['user_otp'])) {
        $respData['status'] = false;
        $respData['message'] = 'Username or OTP not provided.';
        return ['data' => $respData];
    }


    $otpData = $this->otpRepositoryInterface->getByEmail($username);

    if (is_array($otpData->getData())) {
        $createdAt = $otpData->getCreatedAt();

        if (!$createdAt) {
            $respData['status'] = false;
            $respData['message'] = 'Invalid OTP data. Please resend OTP and try again.';
            return ['data' => $respData];
        }

        $otpCreatedTimestamp = strtotime($createdAt);

        if ($otpCreatedTimestamp === false) {
            $respData['status'] = false;
            $respData['message'] = 'Invalid OTP data. Please resend OTP and try again.';
            return ['data' => $respData];
        }

        $currentTimestamp = time();
        $timeDiff = $currentTimestamp - $otpCreatedTimestamp;

        $otpExpiryTime = $this->helper->otpExpiry();
      if ($otpExpiryTime >= 60 && $otpExpiryTime <= 300) {
                $otpExpiryTime = $otpExpiryTime;
            } else {
                $otpExpiryTime = 60;
            }

        if ($timeDiff >= $otpExpiryTime) {
            $respData['status'] = false;
            $respData['message'] = 'OTP expired. Please resend OTP and try again.';
            return ['data' => $respData];
        }

        if ($otpData->getOtp() == $otp) {
            $this->otpRepositoryInterface->deleteByEmail($username);
            $respData['status'] = true;
            $respData['message'] = 'OTP verified.';
            return ['data' => $respData];
        } else {
            $respData['status'] = false;
            $respData['message'] = 'You have entered a wrong OTP. Please try again.';
            return ['data' => $respData];
        }
    }

    $respData['status'] = false;
    $respData['message'] = 'Something Went Wrong.';
    return ['data' => $respData];
}

}

