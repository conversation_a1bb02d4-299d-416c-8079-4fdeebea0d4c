<?php
namespace Comave\ComaveApi\Model;

use AllowDynamicProperties;
use Magento\Framework\App\RequestInterface;
use Comave\ComaveApi\Api\OTPRegistrationManagementInterface;
use Webkul\FirebaseOTPLogin\Api\Data\OtpInterface;
use Webkul\FirebaseOTPLogin\Api\OtpRepositoryInterface;

#[AllowDynamicProperties]
class OTPRegistrationManagement implements OTPRegistrationManagementInterface
{
    public const XML_PATH_OTP_EMAIL = 'firebase_otp/emailsettings/otp_notification';
    public const XML_PATH_OTP_EMAIL_LOGIN = 'firebase_otp/emailsettings/otp_notification_at_login';
    public const XML_PATH_OTP_EMAIL_FORGET_PASSWORD = 'firebase_otp/emailsettings/otp_notification_at_forget_password';

    public function __construct(
        RequestInterface $request,
        \Magento\Framework\Translate\Inline\StateInterface $inlineTranslation,
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Framework\Controller\Result\JsonFactory $resultJson,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Magento\Customer\Model\Session $customerSession,
        \Webkul\FirebaseOTPLogin\Helper\Data $helper,
        OtpRepositoryInterface $otpRepositoryInterface,
        OtpInterface $otpInterface,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        \Magento\Framework\Serialize\SerializerInterface $serialize,
        \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory $customerCollectionFactory
    ) {
        $this->request = $request;
        $this->inlineTranslation = $inlineTranslation;
        $this->transportBuilder = $transportBuilder;
        $this->scopeConfig = $scopeConfig;
        $this->resultJson = $resultJson;
        $this->storeManager = $storeManager;
        $this->customerFactory = $customerFactory;
        $this->customerSession = $customerSession;
        $this->helper = $helper;
        $this->otpRepositoryInterface = $otpRepositoryInterface;
        $this->otpInterface = $otpInterface;
        $this->date = $date;
        $this->serialize = $serialize;
        $this->customerCollectionFactory = $customerCollectionFactory;
    }

    public function sendOTP()
    {
        //if ($this->helper->isModuleEnable()) {
            $requestData = json_decode($this->request->getContent(), true);
            $name = $requestData['name'] ?? null;
            $email = $requestData['email'] ?? null;
            $resend = $requestData['resend'] ?? null;
            //$mobile = $requestData['mobile'] ?? null;
            $registration = $requestData['registration'] ?? null;
            //$regionId = $requestData['region'] ?? null;
            // $shouldCheckExistingAccount = isset($requestData['shouldCheckExistingAccount'])
            // ? (int) $requestData['shouldCheckExistingAccount'] : 1;
            $forgetPassword = false;
            if (isset($requestData['forgetpassword'])) {
                $forgetPassword = $requestData['forgetpassword'];
            }
            //if (!$this->isCheckout() && $shouldCheckExistingAccount) {
                $accountExists = $this->checkExistingAccount($email);
                if ($accountExists) {
                    $respData['status'] = false;
                    $respData['message'] = 'An account already exits with this email.';
                    return ['data' => $respData];
                    // $result = [
                    //     'error' => true,
                    //     'message' => __("An account already exits with this email."),
                    //     'errorCode' => "account_exist",
                    // ];
                    // return $this->resultJson->create()->setData($result);
                }

                // $checkIfPhoneNumberExist = $this->customerCollectionFactory->create()
                //     ->addAttributeToFilter('default_phone_number', ['eq' => $mobile]);

                // if (!empty($checkIfPhoneNumberExist->getData())) {
                //     $respData['status'] = false;
                //     $respData['message'] = 'Phone number already exist';
                //     return ['data' => $respData];
                //     // $result = [
                //     //     'error' => true,
                //     //     'message' => __("Phone number already exist"),

                //     // ];
                //     // return $this->resultJson->create()->setData($result);
                // }
            //}
            $password = rand(100000, 999999);
            $collection = $this->otpRepositoryInterface->getByEmail($email);
            $date = $this->date->gmtDate();
            // if ($mobile!=null) {
            //     $mobile = str_replace(" ", "", $mobile);
            // }
            // $callingCode = empty($regionId)
            // ? ''
            // : ('+' . $this->helper->getCallingCodeByCountryCode($regionId));
            // $mobile = !empty($mobile) && !empty($callingCode) &&
            // substr($mobile, 0, 1) !== '+'
            // ? $callingCode . $mobile
            // : $mobile;
            // if (!$this->customerSession->getCustomer()->getGroupId()) {
            //     if (empty($email)) {
            //         $email = $this->customerSession->getCustomer()->getEmail();
            //     }
            //     if (empty($mobile)) {
            //         $regionId = $this->customerSession->getCustomer()->getPrimaryBillingAddress()->getCountryId();
            //         $callingCode = '+' . $this->helper->getCallingCodeByCountryCode($regionId);
            //         $mobile = $callingCode . $this->customerSession
            //             ->getCustomer()->getPrimaryBillingAddress()
            //             ->getTelephone();
            //     }
            // }
            if (is_array($collection->getData())) {
                $collection->setEmail($email);
                $collection->setOtp($password);
                $collection->setCreatedAt($date);
                $collection->save($collection);
            } else {
                $this->otpInterface->setEmail($email);
                $this->otpInterface->setOtp($password);
                $this->otpRepositoryInterface->save($this->otpInterface);
            }

            $sendOtpVia = $this->helper->sendOtpVia();
            $forOthersSendOtpVia = $this->helper->forOthersSendOtpVia();

            if ($forOthersSendOtpVia == 'both' || $forOthersSendOtpVia == 'email' || $sendOtpVia == 'email') {
                if (!empty($email)) {
                    $response = $this->sendOTPToEmail($email, $name, $password, $registration, $forgetPassword);
                    //return $response;
                    $otpMedium = 'Email ID';
                }
            }

            if ($response['error']) {
                $errorMessage = $response['message'] ?? "";

                if ($otpMedium == "Email ID") {
                    if ($errorMessage == 'Unable to send mail') {
                        $respData['status'] = false;
                        $respData['message'] = 'Unable to send mail.';
                        return ['data' => $respData];
                        // $result = ['error' => true, 'message' =>
                        // __("Unable to send mail"), 'errorCode' => "exception"];
                        // return $this->resultJson->create()->setData($result);
                    }
                }

                if (strpos($errorMessage, 'Unable to send mail') === false) {
                    $respData['status'] = false;
                    $respData['message'] = 'Unable to send OTP. Please try again later.';
                    return ['data' => $respData];
                    // $result = ['otpMedium'=>$otpMedium,
                    // 'rerrorMessage'=>$response['message'],
                    // 'error' => true,
                    // 'message' => __("Unable to send mail or verify your twilio sender number"),
                    // 'errorCode' => "exception"];
                    // return $this->resultJson->create()->setData($result);
                } elseif (strpos($errorMessage, 'unverified numbers') === false) {
                    $respData['status'] = false;
                    $respData['message'] = 'The phone number is not verified.';
                    return ['data' => $respData];
                    // $result = ['error' => true, 'message' => __("The phone number %1 is not verified.", $mobile)
                    // , 'errorCode' => "exception"];
                    // return $this->resultJson->create()->setData($result);
                } else {
                    $respData['status'] = false;
                    $respData['message'] = 'Unable to send OTP. Please try again later.';
                    return ['data' => $respData];
                    // $result = ['error' => true, 'message' => __("Unable to send OTP.
                    // Please try again later."), 'errorCode' => "exception"];
                    // return $this->resultJson->create()->setData($result);
                }
            } else {
                $successMessage = $resend
                ? __("A new OTP has been sent to your registered %1. Please enter the OTP.", $otpMedium)
                : __("Please Enter the OTP sent to your registered %1", $otpMedium);
                $respData['status'] = true;
                $respData['message'] = $successMessage;
                return ['data' => $respData];
                // $result = ['error' => false, 'message' => $successMessage];
                // return $this->resultJson->create()->setData($result);
            }
        // } else {
        //     $respData['status'] = false;
        //     $respData['message'] = 'Something went wrong.';
        //     return ['data' => $respData];
        //     // $this->messageManager->addError(__("Something Went Wrong."));
        //     // $result = ['error' => true, 'message' => __("Something Went Wrong."), 'errorCode' => "exception"];
        //     // return $this->resultJson->create()->setData($result);
        // }
    }

    /**
     * Function to send One time password on email
     *
     * @param string $email
     * @param string $name
     * @param integer $password
     * @param bool $forgetPassword
     * @return array
     */
    private function sendOTPToEmail($email, $name, $password, $registration, $forgetPassword = false)
    {
        $emailTempVariables = [];
        $senderInfo = [];
        $receiverInfo = [];
        $storeScope = \Magento\Store\Model\ScopeInterface::SCOPE_STORE;
        $senderEmail = $this->scopeConfig->getValue('trans_email/ident_support/email', $storeScope);

        $senderName = $this->scopeConfig->getValue('trans_email/ident_support/name', $storeScope);
        $emailTempVariables['password'] = $password;
        $senderInfo['email'] = $senderEmail;
        $senderInfo['name'] = $senderName;
        $receiverInfo['email'] = $email;
        if (!empty($name)) {
            $receiverInfo['name'] = $name;
            $emailTempVariables['name'] = $name;
        } else {
            $receiverInfo['name'] = "Buyer";
            $emailTempVariables['name'] = "Buyer";
        }
        $emailTempVariables['time_to_expire'] = $this->helper->getOtpTimeToExpireString();
         try {
            $this->template = $this->getTemplateId(self::XML_PATH_OTP_EMAIL_LOGIN);
            if ($registration) {
                $this->template = $this->getTemplateId(self::XML_PATH_OTP_EMAIL);
            } elseif ($forgetPassword) {
                $this->template = $this->getTemplateId(self::XML_PATH_OTP_EMAIL_FORGET_PASSWORD);
            }
            $this->inlineTranslation->suspend();
            $this->generateTemplate($emailTempVariables, $senderInfo, $receiverInfo);
            $transport = $this->transportBuilder->getTransport();
            $transport->sendMessage();
            $this->inlineTranslation->resume();
            $result = ['error' => false, 'message' => 'Successfully sent'];
        } catch (\Exception $e) {
            $message = $e->getMessage();
            return $message;
            $message = substr($message, (strpos($message, ":") ?: -2) + 2);
            $result = ['error' => true, 'message' => "Unable to send mail"];
        }

        return $result;
    }

    /**
     * Function to check if an already exists with email provided by customer
     *
     * @param string $email email
     *
     * @return bool
     */
    private function checkExistingAccount($email)
    {
        $accountExists = $this->customerFactory->create()->getCollection()
            ->addFieldToFilter('email', ['eq' => $email])
            ->getSize();
        if ($accountExists > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Return template id.
     *
     * @param string $xmlPath
     *
     * @return mixed
     */
    private function getTemplateId($xmlPath)
    {
        return $this->getConfigValue($xmlPath, $this->getStore()->getStoreId());
    }

    /**
     * Return store configuration value.
     *
     * @param string $path
     * @param int $storeId
     *
     * @return mixed
     */
    private function getConfigValue($path, $storeId)
    {
        return $this->scopeConfig->getValue(
            $path,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Return store.
     *
     * @return \Magento\Store\Api\Data\StoreInterface
     */
    private function getStore()
    {
        return $this->storeManager->getStore();
    }

     /**
     * Generate Template description.
     *
     * @param Mixed $emailTemplateVariables
     * @param Mixed $senderInfo
     * @param Mixed $receiverInfo
     */
    private function generateTemplate($emailTemplateVariables, $senderInfo, $receiverInfo)
    {
        $this->transportBuilder
            ->setTemplateIdentifier($this->template)
            ->setTemplateOptions(
                [
                    'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
                    'store' => $this->storeManager->getStore()->getId(),
                ]
            )
            ->setTemplateVars($emailTemplateVariables)
            ->setFrom($senderInfo)
            ->addTo($receiverInfo['email'], $receiverInfo['name']);
        return $this;
    }

    public function verifyOTP()
    {
        $requestData = json_decode($this->request->getContent(), true);
        $email = $requestData['email'];
        $otp = $requestData['user_otp'];
        $otpData = $this->otpRepositoryInterface->getByEmail($email);
        if (is_array($otpData->getData())) {
            $otpCreatedTimestamp = strtotime($otpData->getCreatedAt());
            $currentTimestamp = time();
            $timeDiff = $currentTimestamp - $otpCreatedTimestamp;
            $otpExpiryTime = $this->helper->otpExpiry();
            if ($otpExpiryTime >= 60 && $otpExpiryTime <= 300) {
                $otpExpiryTime = $otpExpiryTime;
            } else {
                $otpExpiryTime = 60;
            }
            if ($timeDiff >= $otpExpiryTime) {
                $respData['status'] = false;
                $respData['message'] = 'OTP expired.Please resend OTP and try again.';
                return ['data' => $respData];
                // $result = ['error' => true, 'message' => __('OTP expired.Please resend OTP and try again.')];
                // return $this->resultJson->create()->setData($result);
            }
            if ($otpData->getOtp() == $otp) {
                $this->otpRepositoryInterface->deleteByEmail($email);
                $respData['status'] = true;
                $respData['message'] = 'OTP verified.';
                return ['data' => $respData];
                // $result = ['error' => false, 'message' => __('OTP verified.')];
                // return $this->resultJson->create()->setData($result);
            } else {
                $respData['status'] = false;
                $respData['message'] = 'You have entered a wrong OTP. Please try again.';
                return ['data' => $respData];
                // $result = ['error' => true, 'message' => __('You have entered a wrong code. Please try again.')];
                // return $this->resultJson->create()->setData($result);
            }
        }
        $respData['status'] = false;
        $respData['message'] = 'Something Went Wrong.';
        return ['data' => $respData];
        // $result = ['error' => true, 'message' => __('Something Went Wrong.')];
        // return $result;
        //return $this->resultJson->create()->setData($result);
    }
}
