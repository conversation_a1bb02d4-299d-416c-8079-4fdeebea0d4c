<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Comave\ComaveApi\Model;

use AllowDynamicProperties;
use Comave\LixApiConnector\Model\Reward\ConfigProvider;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Pricing\PriceCurrencyInterface;
use Magento\Quote\Model\Quote\Address\TotalFactory;
use Magento\Quote\Model\QuoteFactory;

#[AllowDynamicProperties]
class RedeemRewardManagement implements \Comave\ComaveApi\Api\RedeemRewardManagementInterface
{
    /**
     * @var QuoteFactory
     */
    protected $quoteFactory;

    protected $request;
    protected $totalFactory;
    /**
     * Reward data
     *
     * @var \Magento\Reward\Helper\Data
     */
    protected $_rewardData = null;

    /**
     * Reward factory
     *
     * @var \Magento\Reward\Model\RewardFactory
     */
    protected $_rewardFactory;

    /**
     * @var PriceCurrencyInterface
     */
    protected $priceCurrency;

    protected $configProvider;

    public function __construct(
        CustomerRepositoryInterface $customerRepositoryInterface,
        QuoteFactory $quoteFactory,
        \Comave\LixApiConnector\Helper\Data $dataHelper,
        RequestInterface $request,
        TotalFactory $totalFactory,
        \Magento\Reward\Helper\Data $rewardData,
        \Magento\Reward\Model\RewardFactory $rewardFactory,
        PriceCurrencyInterface $priceCurrency,
        \Comave\LixApiConnector\Helper\Data $lixHelper,
        ConfigProvider $configProvider
    ) {
        $this->_customerRepositoryInterface = $customerRepositoryInterface;
        $this->quoteFactory = $quoteFactory;
        $this->dataHelper = $dataHelper;
        $this->request = $request;
        $this->totalFactory = $totalFactory;
        $this->_rewardData = $rewardData;
        $this->_rewardFactory = $rewardFactory;
        $this->priceCurrency = $priceCurrency;
        $this->_lixHelper = $lixHelper;
        $this->configProvider = $configProvider;
    }
    /**
     * {@inheritdoc}
     */
    public function redeemReward($customerId)
    {
        $customerInfo = $this->_customerRepositoryInterface->getById($customerId);
        $customerEmail = $customerInfo->getEmail();
        $quote = $this->quoteFactory->create()->loadByCustomer($customerId);
        $total = $quote->getBillingAddress();
        $totalShipping = $quote->getShippingAddress();
        $id = $quote->getId();
        $lixWallet = 'lix_wallet';
        $lixCurrResp = $this->dataHelper->getCustLixWallet($customerId,$lixWallet);
        //$lixBalResp = $this->dataHelper->getLixBalanceByEmail($customerEmail,$lixCurrResp);
        // if(array_key_exists("data", $lixBalResp)){
        //     $lixBalance = $lixBalResp['data']['balance'];
        // }
        $lixBalanceArray = $this->configProvider->getBalance($customerId);
        $lixBalance= $lixBalanceArray['points'];
        $lixCurrency = $lixBalanceArray['currency'];
        $requestBody = $this->request->getContent();
        $data = json_decode($requestBody, true);
        $rewardPoints = $data['redeem_point'] ?? '';
        if ($rewardPoints > $lixBalance){
            return false;
        }else{
            $quote->setData('lix_reward', $rewardPoints);
            $quote->setData('use_reward_points', 1);
            $quote->save();
            if (!$this->_rewardData->isEnabledOnFront($quote->getStore()->getWebsiteId())) {
                return false;
            }
            $totalShipping->setData('reward_points_balance',0)->setData('reward_currency_amount',0)->setData('base_reward_currency_amount',0);
            $totalShipping->save();
            if ($totalShipping->getBaseGrandTotal() >= 0 && $quote->getCustomer()->getId() && $quote->getUseRewardPoints()) {
                $reward = $quote->getRewardInstance();
                if (!$reward || !$reward->getId()) {
                    $customer = $quote->getCustomer();
                    $reward = $this->_rewardFactory->create()->setCustomer($customer);
                    $reward->setCustomerId($quote->getCustomer()->getId());
                    $reward->setWebsiteId($quote->getStore()->getWebsiteId());
                    $reward->loadByCustomer();
                }
                $response = $this->dataHelper->updateLixBalance($customerEmail,$rewardPoints,$lixCurrency);
                $pointsLeft = $response['data']['balance'];
                $rewardCurrencyAmountLeft = $this->priceCurrency->convert(
                    $reward->getCurrencyAmount(),
                    $quote->getStore()
                ) - $quote->getRewardCurrencyAmount();
                if($rewardPoints > 0){
                    $baseRewardCurrencyAmountLeft = $rewardPoints * $this->_lixHelper->getCashCurrenciesValues();
                }else{
                    $baseRewardCurrencyAmountLeft = $reward->getCurrencyAmount() - $quote->getBaseRewardCurrencyAmount();
                }
                if ($baseRewardCurrencyAmountLeft >= $totalShipping->getBaseGrandTotal()) {
                    $pointsBalanceUsed = $reward->getPointsEquivalent($totalShipping->getBaseGrandTotal());
                    $pointsCurrencyAmountUsed = $totalShipping->getGrandTotal();
                    $basePointsCurrencyAmountUsed = $totalShipping->getBaseGrandTotal();

                    $totalShipping->setData('grand_total',0);
                    $totalShipping->setData('base_grand_total',0);
                    $totalShipping->save();

                } else {
                    $pointsBalanceUsed = $reward->getPointsEquivalent($baseRewardCurrencyAmountLeft);
                    if ($pointsBalanceUsed > $pointsLeft) {
                        $pointsBalanceUsed = $pointsLeft;
                    }
                    if($rewardPoints > 0) {
                        $pointsCurrencyAmountUsed = $baseRewardCurrencyAmountLeft;
                    }else{
                        $pointsCurrencyAmountUsed = $rewardCurrencyAmountLeft;
                    }
                    $basePointsCurrencyAmountUsed = $baseRewardCurrencyAmountLeft;
                    $totalShipping->setData('grand_total', $totalShipping->getGrandTotal() - $pointsCurrencyAmountUsed);
                    $totalShipping->setData('base_grand_total',$totalShipping->getBaseGrandTotal() - $basePointsCurrencyAmountUsed);
                    $totalShipping->save();
                }
                $quote->setRewardPointsBalance($rewardPoints);
                $quote->setRewardCurrencyAmount($baseRewardCurrencyAmountLeft);
                $quote->setBaseRewardCurrencyAmount($baseRewardCurrencyAmountLeft);
                $quote->save();
                $totalShipping->setData('reward_points_balance', $rewardPoints);
                $totalShipping->setData('reward_currency_amount', $baseRewardCurrencyAmountLeft);
                $totalShipping->setData('base_reward_currency_amount', $baseRewardCurrencyAmountLeft);
                $totalShipping->save();
                return true;
            }
        }
    }
}

