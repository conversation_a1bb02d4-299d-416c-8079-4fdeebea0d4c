<?php
namespace Comave\ComaveApi\Model;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Comave\ComaveApi\Api\JwtVerfiyTokenManagementInterface;
use Magento\Framework\App\RequestInterface;

class JwtVerfiyTokenManagement extends AbstractHelper implements JwtVerfiyTokenManagementInterface
{
    protected $cookieManager;
    protected $scopeConfig;
    protected $request;

    public function __construct(
        Context $context,
        ScopeConfigInterface $scopeConfig,
        RequestInterface $request
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->request = $request;
    }

    public function verifyToken()
    {
        $logger = new \Zend_Log();
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/jwtVerification.log');
        $logger->addWriter($writer);

        try {
            $data = json_decode($this->request->getContent(), true);
            $exp = $data['exp'];
            $logger->info("exp: " . $exp);
            $currentTimestamp = time();
            if ($exp < $currentTimestamp) {
                $logger->err('Token has expired.');
                $responseData['status']= false;
                $responseData['message']='Token is expired';
                return ['data' => $responseData];
            }

           $responseData['status']= true;
           $responseData['message']='Token is valid';
           return ['data' => $responseData];
        } catch (\Exception $e) {
            $logger->err('Token verification failed: ' . $e->getMessage());
            return false;
        }
    }
}
