<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Comave\ComaveApi\Api\VATRefundManagementInterface" type="Comave\ComaveApi\Model\VATRefundManagement"/>
	<preference for="Comave\ComaveApi\Api\ProfilePicManagementInterface" type="Comave\ComaveApi\Model\ProfilePicManagement"/>
	<preference for="Comave\ComaveApi\Api\RedeemRewardManagementInterface" type="Comave\ComaveApi\Model\RedeemRewardManagement"/>
	<preference for="Comave\ComaveApi\Api\SearchTagManagementInterface" type="Comave\ComaveApi\Model\SearchTagManagement"/>
	<preference for="Comave\ComaveApi\Api\ReferFriendManagementInterface" type="Comave\ComaveApi\Model\ReferFriendManagement"/>
    <preference for="Comave\ComaveApi\Api\LixActivityManagementInterface" type="Comave\ComaveApi\Model\LixActivityManagement"/>
	<preference for="Comave\ComaveApi\Api\JwtVerfiyTokenManagementInterface" type="Comave\ComaveApi\Model\JwtVerfiyTokenManagement" />
	<preference for="Comave\ComaveApi\Api\IsEmailAvailableManagementInterface" type="Comave\ComaveApi\Model\IsEmailAvailableManagement" />
	<preference for="Comave\ComaveApi\Api\OTPLoginManagementInterface" type="Comave\ComaveApi\Model\OTPLoginManagement"/>
	<preference for="Comave\ComaveApi\Api\OTPRegistrationManagementInterface" type="Comave\ComaveApi\Model\OTPRegistrationManagement"/>
    <type name="Magento\Catalog\Model\Product\Attribute\OptionManagement">
        <plugin name="custom_option_management" type="Comave\ComaveApi\Plugin\OptionManagementPlugin" />
    </type>
	<type name="Magento\Sales\Api\OrderRepositoryInterface">
        <plugin name="comave_comaveapi_order_repository_plugin" type="Comave\ComaveApi\Plugin\OrderRepositoryPlugin" />
	</type>
    <virtualType name="Comave\Logger\Model\ComaveApi" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">ComaveApi</argument>
            <argument name="loggerPath" xsi:type="string">ComaveApi</argument>
        </arguments>
    </virtualType>
</config>
