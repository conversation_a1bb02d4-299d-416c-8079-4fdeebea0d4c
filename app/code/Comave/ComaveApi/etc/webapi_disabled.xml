<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/comave-comaveapi/new-rma" method="POST">
        <service class="Comave\ComaveApi\Api\RMADetailsManagementInterface" method="createRma"/>
        <resources>
            <resource ref="self"/>
        </resources>
        <data>
            <parameter name="customerId" force="true">%customer_id%</parameter>
        </data>
    </route>
    <route url="/V1/comave-comaveapi/rma/upload-images" method="POST">
        <service class="Comave\ComaveApi\Api\RMADetailsManagementInterface" method="uploadProductImages"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route url="/V1/comave-comaveapi/rma/image-data" method="POST">
        <service class="Comave\ComaveApi\Api\RMADetailsManagementInterface" method="sellerImages"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route url="/V1/comave-comaveapi/rma/allrma" method="GET">
        <service class="Comave\ComaveApi\Api\RMADetailsManagementInterface" method="getAllRmaList"/>
        <resources>
            <resource ref="self"/>
        </resources>
        <data>
            <parameter name="customerId" force="true">%customer_id%</parameter>
        </data>
    </route>
    <route url="/V1/comave-comaveapi/rma/details" method="GET">
        <service class="Comave\ComaveApi\Api\RMADetailsManagementInterface" method="getRmaDetails"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route url="/V1/comave-comaveapi/rma/conversation" method="POST">
        <service class="Comave\ComaveApi\Api\RMADetailsManagementInterface" method="saveConversation"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route url="/V1/comave-comaveapi/rma/close" method="POST">
        <service class="Comave\ComaveApi\Api\RMADetailsManagementInterface" method="closeRMA"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route url="/V1/comave-comaveapi/rma/cancel" method="POST">
        <service class="Comave\ComaveApi\Api\RMADetailsManagementInterface" method="cancelRMA"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route url="/V1/comave-comaveapi/rma/orderDetails" method="GET">
        <service class="Comave\ComaveApi\Api\RMADetailsManagementInterface" method="getOrderDetails"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route url="/V1/comave-comaveapi/refer-friend" method="POST">
        <service class="Comave\ComaveApi\Api\ReferFriendManagementInterface" method="referFriend"/>
        <resources>
            <resource ref="self"/>
        </resources>
        <data>
            <parameter name="customerId" force="true">%customer_id%</parameter>
        </data>
    </route>
    <route url="/V1/comave-comaveapi/get-refer-friendlist" method="GET">
        <service class="Comave\ComaveApi\Api\ReferFriendManagementInterface" method="referFriendList"/>
        <resources>
            <resource ref="self"/>
        </resources>
        <data>
            <parameter name="customerId" force="true">%customer_id%</parameter>
        </data>
    </route>
</routes>
