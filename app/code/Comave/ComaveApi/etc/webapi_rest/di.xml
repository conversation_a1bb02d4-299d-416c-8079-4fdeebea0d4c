<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
     <type name="Magento\Framework\Authorization">
        <plugin name="customerAuthorizationRest" type="Magento\Customer\Model\Plugin\CustomerAuthorization" />
    </type>
    <type name="Magento\Authorization\Model\CompositeUserContext">
        <arguments>
            <argument name="userContexts" xsi:type="array">
                <item name="customerSessionUserContext" xsi:type="array">
                    <item name="type" xsi:type="object">Magento\Customer\Model\Authorization\CustomerSessionUserContext</item>
                    <item name="sortOrder" xsi:type="string">20</item>
                </item>
            </argument>
        </arguments>
    </type>
</config>
