<?php

declare(strict_types=1);

namespace Comave\AttributeCleanup\Services;

use Comave\AttributeCleanup\Helper\Data;
use Magento\Catalog\Model\ResourceModel\Product;
use Magento\Eav\Api\AttributeOptionManagementInterface;
use Magento\Eav\Model\Entity\Attribute;
use Magento\Framework\App\Area;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Logger\Monolog;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Model\Store;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Output\OutputInterface;

class AttributeCleanupService
{
    private const LOG_TYPE_ERROR = 'error';
    private const LOG_TYPE_INFO = 'info';

    /**
     * Construct function
     *
     * @param \Magento\Catalog\Model\ResourceModel\Product $productResource
     * @param \Magento\Framework\Logger\Monolog $logger
     * @param \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder
     * @param \Magento\Framework\Translate\Inline\StateInterface $inlineTranslation
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Framework\Stdlib\DateTime\DateTime $dateTime
     * @param \Magento\Eav\Api\AttributeOptionManagementInterface $optionManagement
     * @param \Comave\AttributeCleanup\Helper\Data $dataHelper
     */
    public function __construct(
        private readonly Product $productResource,
        private readonly Monolog $logger,
        private readonly TransportBuilder $transportBuilder,
        private readonly StateInterface $inlineTranslation,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly DateTime $dateTime,
        private readonly AttributeOptionManagementInterface $optionManagement,
        private readonly Data $dataHelper
    ) {
    }

    /**
     * Execute function
     *
     * @param string $attributeCode
     * @param ?\Symfony\Component\Console\Output\OutputInterface $output
     * @param bool $sendEmail
     * @return int
     */
    public function execute(string $attributeCode, ?OutputInterface $output = null, bool $sendEmail = true): int
    {
        $attribute = $this->validateAttribute($attributeCode, $output);

        if (!$attribute) {
            return Command::FAILURE;
        }

        $optionsCleared = $this->clearAttributeOptions($attribute, $output);

        if ($sendEmail) {
            $this->sendEmailNotification($attributeCode, $optionsCleared, $output);
        }

        $this->logAndWriteln(
            sprintf(
                "Successfully cleared options for attribute '%s'. Total options cleared: %s",
                $attributeCode,
                $optionsCleared
            ),
            self::LOG_TYPE_INFO,
            $output
        );

        return Command::SUCCESS;
    }

    /**
     * Clear the attribute options.
     *
     * @param string $attributeCode
     * @param int $optionId
     * @param ?\Symfony\Component\Console\Output\OutputInterface $output
     * @return int
     */
    public function clearAttributeOption(string $attributeCode, int $optionId, ?OutputInterface $output): void
    {
        try {
            $this->optionManagement->delete(\Magento\Catalog\Model\Product::ENTITY, $attributeCode, $optionId);
        } catch (\Exception $e) {
            $this->logAndWriteln(
                sprintf(
                    "Option ID %s for attribute %s could not be deleted. Error: %s",
                    $optionId,
                    $attributeCode,
                    $e->getMessage()
                ),
                self::LOG_TYPE_ERROR,
                $output
            );
        }
    }

    /**
     * Validate the attribute.
     *
     * @param string $attributeCode
     * @return \Magento\Eav\Model\Entity\Attribute|null
     * @param ?\Symfony\Component\Console\Output\OutputInterface $output
     */
    public function validateAttribute(string $attributeCode, ?OutputInterface $output = null): ?Attribute
    {
        $attribute = $this->productResource->getAttribute($attributeCode);

        if (!$attribute) {
            $this->logAndWriteln(
                sprintf("Attribute '%s' does not exist.", $attributeCode),
                self::LOG_TYPE_ERROR,
                $output
            );

            return null;
        }

        // Check if the attribute is of type dropdown or multiselect
        if (!in_array($attribute->getFrontendInput(), ['select', 'multiselect'])) {
            $this->logAndWriteln(
                sprintf("Attribute '%s' is not a dropdown or multiselect.", $attributeCode),
                self::LOG_TYPE_ERROR,
                $output
            );

            return null;
        }

        return $attribute;
    }

    /**
     * Log And Writeln function
     *
     * @param string $message
     * @param string $type
     * @param ?\Symfony\Component\Console\Output\OutputInterface $output
     * @return void
     */
    public function logAndWriteln(
        string $message,
        string $type = self::LOG_TYPE_INFO,
        ?OutputInterface $output = null
    ): void {
        if ($output instanceof OutputInterface) {
            $output->writeln($type === self::LOG_TYPE_ERROR ? "<error>{$message}</error>" : $message);
        }

        $this->logger->{$type === self::LOG_TYPE_ERROR ? 'error' : 'info'}($message);
    }

    /**
     * Send Email Notification Report function
     *
     * @param string $attributeCode
     * @param int $optionsCleared
     * @param ?\Symfony\Component\Console\Output\OutputInterface $output
     * @return void
     */
    public function sendEmailNotification(
        string $attributeCode,
        int $optionsCleared,
        ?OutputInterface $output = null
    ): void {
        $this->inlineTranslation->suspend();
        $adminEmail = $this->dataHelper->getAdminEmail();

        $transport = $this->transportBuilder
            ->setTemplateIdentifier('comave_attribute_cleanup_template')
            ->setTemplateOptions(['area' => Area::AREA_FRONTEND, 'store' => Store::DEFAULT_STORE_ID])
            ->setTemplateVars([
                'attribute_code' => $attributeCode,
                'date' => $this->dateTime->gmtDate('Y-m-d H:i:s'),
                'options_cleared' => $optionsCleared,
            ])
            ->setFromByScope($this->dataHelper->getAttributeCleanupFromEmail())
            ->addTo($adminEmail, $this->dataHelper->getAdminName())
            ->getTransport();

        $transport->sendMessage();

        $this->inlineTranslation->resume();
        $this->logAndWriteln(
            sprintf("Email notification sent for attribute cleanup: '%s' to %s", $attributeCode, $adminEmail),
            self::LOG_TYPE_INFO,
            $output
        );
    }

    /**
     * Clear the attribute options.
     *
     * @param \Magento\Eav\Model\Entity\Attribute $attribute
     * @param ?\Symfony\Component\Console\Output\OutputInterface $output
     * @return int
     */
    public function clearAttributeOptions(Attribute $attribute, ?OutputInterface $output = null): int
    {
        $attributeOptions = $attribute->getSource()->getAllOptions();
        $optionsCleared = 0;

        foreach ($attributeOptions as $option) {
            $optionId = $option['value'];

            if (!$optionId) {
                continue;
            }

            $this->clearAttributeOption($attribute->getAttributeCode(), (int) $optionId, $output);
            $optionsCleared++;
            $this->logAndWriteln(
                sprintf("Cleared option ID '%s' for attribute '%s'", $optionId, $attribute->getAttributeCode()),
                self::LOG_TYPE_INFO,
                $output
            );
        }

        return $optionsCleared;
    }
}
