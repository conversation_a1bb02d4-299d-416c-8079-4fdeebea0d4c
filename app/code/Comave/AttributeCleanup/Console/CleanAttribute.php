<?php

declare(strict_types=1);

namespace Comave\AttributeCleanup\Console;

use Comave\AttributeCleanup\Services\AttributeCleanupService;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class CleanAttribute extends Command
{
    /**
     * Construct function
     *
     * @param \Comave\AttributeCleanup\Services\AttributeCleanupService $attributeCleanupService
     * @param \Magento\Framework\App\State $appState
     */
    public function __construct(
        private readonly AttributeCleanupService $attributeCleanupService,
        private readonly State $appState
    ) {
        parent::__construct();
    }

    /**
     * Configure function
     *
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('comave:attribute-options:cleanup')
            ->setDescription('Clean all values for a specified product attribute')
            ->addArgument('attribute_code', InputArgument::REQUIRED, 'Product Attribute Code');
    }

    /**
     * Execute function
     *
     * @param \Symfony\Component\Console\Input\InputInterface $input
     * @param \Symfony\Component\Console\Output\OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $attributeCode = $input->getArgument('attribute_code');
        $this->appState->setAreaCode(Area::AREA_FRONTEND);

        return $this->attributeCleanupService->execute($attributeCode, $output);
    }
}
