# Comave AttributeCleanup Module for Magento 2


## Overview

The Comave_AttributeCleanup module provides a CLI command to clean all values for a specified product attribute in Magento 2. This module is designed to streamline the process of managing product attributes by allowing administrators to easily remove unwanted values.

## Features

1. Creates a CLI command to clean all values for a specified product attribute.
2. Takes the product attribute code as an argument.
3. Sends an email notification to the admin email configured in Magento settings upon execution.
4. Logs activity and provides output on the screen.

## Installation

Clone the repository or download the module files.
Place the module in the app/code/Comave/AttributeCleanup directory of your Magento installation.
Run the following commands to enable the module:

```bash
php bin/magento module:enable Comave_AttributeCleanup
php bin/magento setup:upgrade
php bin/magento cache:clean
php bin/magento cache:flush
```

## Usage

To clean all options for dropdown or multiselect attributes, use the following command:

```bash
# Replace ATTRIBUTE_CODE with the code of the attribute you wish to clean.
bin/magento comave:attribute-options:cleanup ATTRIBUTE_CODE
```

You should see something similar to this output:

```
$:/var/www/html$ bin/magento comave:attribute-options:cleanup test
Cleared option ID '33507' for attribute 'test'
Cleared option ID '33508' for attribute 'test'
Email notification sent for attribute cleanup: 'test' to <EMAIL>
Successfully cleared options for attribute 'test'. Total options cleared: 2
```

## Configuration

Ensure that the admin email is configured in Magento settings:

Navigate to `Stores > Configuration > General > Store Email Addresses > Admin Contact`.

## Logging

The module logs all activities related to the cleanup process. Check the logs for details on the execution and any issues encountered.
