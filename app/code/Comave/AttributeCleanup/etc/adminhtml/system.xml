<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="trans_email" translate="label" type="text" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
            <group id="ident_admin" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Admin Contact</label>
                <field id="email" translate="label" type="text" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Admin Email</label>
                    <validate>validate-email</validate>
                    <backend_model>Magento\Config\Model\Config\Backend\Email\Address</backend_model>
                </field>
                <field id="name" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Admin Name</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Email\Sender</backend_model>
                    <validate>validate-emailSender</validate>
                </field>
            </group>
        </section>
    </system>
</config>
