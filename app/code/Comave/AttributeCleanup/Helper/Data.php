<?php

declare(strict_types=1);

namespace Comave\AttributeCleanup\Helper;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;

class Data extends AbstractHelper
{
    private const XML_PATH_EMAIL_IDENT_SUPPORT_NAME = 'trans_email/ident_support/name';
    private const XML_PATH_EMAIL_IDENT_SUPPORT_EMAIL = 'trans_email/ident_support/email';
    private const XML_PATH_EMAIL_IDENT_ADMIN_NAME = 'trans_email/ident_admin/name';
    private const XML_PATH_EMAIL_IDENT_ADMIN_EMAIL = 'trans_email/ident_admin/email';

    /**
     * Get From By Scope function
     *
     * @return mixed[]
     */
    public function getAttributeCleanupFromEmail(): array
    {
        return [
            'email' => $this->scopeConfig->getValue(self::XML_PATH_EMAIL_IDENT_SUPPORT_EMAIL),
            'name' => $this->scopeConfig->getValue(self::XML_PATH_EMAIL_IDENT_SUPPORT_NAME)
        ];
    }

    /**
     * Get Admin Email function
     *
     * @return ?string
     */
    public function getAdminName(): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_IDENT_ADMIN_NAME,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get Admin Email function
     *
     * @return ?string
     */
    public function getAdminEmail(): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_IDENT_ADMIN_EMAIL,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }
}
