<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\PricePermissions\Observer\AdminhtmlBlockHtmlBeforeObserver" type="Comave\ApiConnector\Observer\AdminhtmlBlockHtmlBeforeObserver" />
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="comave:currency-rate:sync" xsi:type="object">CurrencyRateSyncCommand</item>
            </argument>
        </arguments>
    </type>
    <virtualType name="CurrencyRateSyncCommand"
                 type="Comave\ApiConnector\Console\Command\CurrencyRateSync">
        <arguments>
            <argument name="name" xsi:type="string">comave:currency-rate:sync</argument>
        </arguments>
    </virtualType>
</config>
