<?php
declare(strict_types=1);

namespace Comave\ApiConnector\Cron;

use Magento\Directory\Model\Currency;
use Magento\Framework\App\Config\Storage\WriterInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\StoreManagerInterface as StoreManagerInterfaceAlias;
use Comave\ApiConnector\Helper\Data as ApiHelper;
use Comave\LixApiConnector\Helper\Data as LixApiHelper;
use Psr\Log\LoggerInterface;

/**
 * Class for save rates.
 */
class CurrencyRateSync
{
    public function __construct(
        private readonly StoreManagerInterfaceAlias $storeManager,
        private readonly Currency $currency,
        private readonly ApiHelper $dataHelper,
        private readonly LixApiHelper $lixDataHelper,
        private readonly WriterInterface $configWriter,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Save rates action
     *
     * @return void
     * @throws \Zend_Log_Exception
     */
    public function execute(): void
    {
        $this->logger->info("Updating exchange rates");

        try {
            $this->updateMagentoDirectoryCurrencyRates();
        } catch (\Exception $e) {
            $this->logger->error('Exception updating directory fiat currency rates', [$e->getMessage()]);
        }

        try {
            $this->updateCryptoToFiatConfigurationCurrencyRates();
        } catch (\Exception $e) {
            $this->logger->error('Exception updating configuration crypto currency rates', [$e->getMessage()]);
        }
    }

    /**
     * @deprecated The Lix api endpoint that was used in this routine is not existing anymore
     * @TODO: replace with other api if needed / see if FixerIO already has its own update cron
     *
     * Where the directory is used?
     * WHen an order for a product on a store which has base currency different from store view,
     * there is a field base to order rate on the order which will store the currency rate at time of order
     *
     * @throws NoSuchEntityException
     * @throws \Exception
     */
    private function updateMagentoDirectoryCurrencyRates(): void
    {
        // if this endpoint were to be existing, this should be refactored as:
        // 1 - get allowed and base currencies for all stores
        // 2 - call further some existing endpoint to fetch the rates for supported currencies
        // 2.1. if some lix endpoint, maybe also consider call /cash-point/valid-fiats to check which ones are supported

        // 1 - get allowed and base currencies for all stores
        $allowedCurrencies = $this->currency->getConfigAllowCurrencies();
        $baseCurrencies = $this->currency->getConfigBaseCurrencies();

        $rates = [];
        foreach ($baseCurrencies as $baseCurrency) {
            $currencyDetails = $this->dataHelper->getCurrentCurrencyRate(); //deprecated and disabled, will return empty []
            // TODO: call a proper endpoint to fetch exchange rates, like this:
            //$currencyDetails = $this->dataHelper->getCurrentCurrencyRate($baseCurrency, $allowedCurrencies);

            if (empty($currencyDetails) || empty($currencyDetails['data'])) {
                throw new \Exception('No fiat rates found from (deprecated) currencyRates Lix api endpoint');
            }

            foreach ($allowedCurrencies as $currency) {
                // extract rate from custom response - TODO: adjust to used api data format
                foreach ($currencyDetails['data'] as $responseRate) {
                    if($currency == $responseRate['currency']){
                        $rates[$baseCurrency][$currency] = $responseRate['rate'];
                    }
                }
            }
        }

        if (!empty($rates)) {
            $this->currency->saveRates($rates);
        }
    }

    private function updateCryptoToFiatConfigurationCurrencyRates(): void
    {
        // TODO: decide if we:
        //  1. fetch and store rate from LIX to base currency (which is only one for a website: vendor/magento/module-directory/etc/adminhtml/system.xml:16)
        //  OR
        //  2. fetch and store rate from LIX to each website default currency
        //  OR
        //  3. have both
        //  Since one updates for all stores default currency and one updates for base currency
        //
        //  Decision help:
        //  Currently, the usage of the exchange rates in Reward calculation is unclear
        //  Used in:
        //  - app/code/Comave/LixApiConnector/Model/Total/Quote/Reward.php:89 , where it does some conversion to base
        //  - app/code/Comave/Sales/Model/Processor/Type/Reward.php:1009
        //  None of the above fetch the exchange rate for specific storeId, so they implicitly rely on base currency,
        //  but it is not clear if the calculation formula is correct

        //  If we go for 1: fetch base currency rate for each website
        //  then use priceCurrency->convert properly in calculations where used/if needed

        //  If we go for 2: need to iterate all stores currencies
        //  and make an api call for each currency, will need to fit under their rate limit of 120 / ?minute?
        //  then save each currency on its respective store
        //  (iterating all allowed currencies won't shave time, since we still need to save per store config)
        //  and then fetch correct store rate in calculations where used

        // Enable one of the below or both:

        // 1. fetch and store rate from LIX to each website base currency
        $this->getCryptoToBaseFiatCurrencyRates();

        // 2. fetch and store rate from LIX to each store default currency
        // Disabled for now to avoid polluting config table, until we decide we need it
        //$this->getCryptoToDefaultFiatAllStoresDefaultCurrencyRates();

    }

    /**
     * // 1. fetch and store rate from LIX to each website base currency
     * @return void
     */
    private function getCryptoToBaseFiatCurrencyRates(): void
    {
        $baseCurrencies = $this->currency->getConfigBaseCurrencies();
        if (count($baseCurrencies) > 1) {
            // multiple websites
            foreach ($this->storeManager->getWebsites() as $website) {
                $baseCurrency =
                    $this->scopeConfig->getValue(Currency::XML_PATH_CURRENCY_BASE, ScopeInterface::SCOPE_WEBSITE, $website->getId());
                $this->fetchCryptosToFiatRateAndSaveToConfig($baseCurrency, ScopeInterface::SCOPE_WEBSITE, $website->getId());
            }
            return;
        }

        // single base currency, save to default scope
        $baseCurrency = reset($baseCurrencies);
        $this->fetchCryptosToFiatRateAndSaveToConfig($baseCurrency, 'default', null);
    }

    /**
     * // 2. fetch and store rate from LIX to each store default currency
     * @return void
     */
    private function getCryptoToDefaultFiatAllStoresDefaultCurrencyRates(): void
    {
        $stores = $this->storeManager->getStores(true); // including default store
        foreach ($stores as $store) {
            if (!$store->getIsActive()) {
                continue;
            }

            $storeId = $store->getId();
            $defaultStoreFiatCurrency = $this->lixDataHelper->getDefaultStoreCurrencyCode($storeId);

            $this->fetchCryptosToFiatRateAndSaveToConfig($defaultStoreFiatCurrency, ScopeInterface::SCOPE_STORE, $storeId);
        }
    }

    private function fetchCryptosToFiatRateAndSaveToConfig(string $fiatCurrency, ?string $scope, $scopeId): void
    {
        foreach (LixApiHelper::CRYPTO_CURRENCIES_ID_CONFIGS as $cryptoCurrencyLabel => $cryptoCurrencyIdConfig) {
            try {
                $cryptoCurrencyId = $this->scopeConfig->getValue(
                    $cryptoCurrencyIdConfig, ScopeConfigInterface::SCOPE_TYPE_DEFAULT
                );
                $rate = $this->lixDataHelper->getCashPointRate($cryptoCurrencyId, $fiatCurrency);
                $this->configWriter->save(LixApiHelper::URL_LIXPP_CURRENCY, $rate, $scope, $scopeId);
            } catch (\Exception $e) {
                $this->logger->warning("No data found for $cryptoCurrencyLabel crypto currency exchange rate!", [$e->getMessage()]);
            }
        }
    }
}
