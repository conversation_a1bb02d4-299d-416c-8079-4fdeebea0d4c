<?php
declare(strict_types=1);

namespace Comave\ApiConnector\Cron;

use Magento\Sales\Api\OrderManagementInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SortOrderBuilder;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactoryInterface;
use Magento\Framework\Stdlib\DateTime\DateTime;

class CancelOrderPending
{
    protected $orderRepository;
    protected $searchCriteriaBuilder;
    protected $sortOrderBuilder;
    protected $customerRepository;
    protected $storeManager;
    protected $_orderCollectionFactory;
    private $dateTime;

    public function __construct(
        OrderRepositoryInterface $orderRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        SortOrderBuilder $sortOrderBuilder,
        CustomerRepositoryInterface $customerRepository,
        StoreManagerInterface $storeManager,
        CollectionFactoryInterface $orderCollectionFactory,
        DateTime $dateTime,
        private OrderManagementInterface $orderManagement
    ) {
        $this->orderRepository = $orderRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->sortOrderBuilder = $sortOrderBuilder;
        $this->customerRepository = $customerRepository;
        $this->storeManager = $storeManager;
        $this->_orderCollectionFactory = $orderCollectionFactory;
        $this->dateTime = $dateTime;
    }

    public function execute()
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/cancelordercron.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info('order products');

        // Define the time range
        $to = date("Y-m-d H:i:s"); // current date and time
        $from = date("Y-m-d H:i:s", strtotime('-5 minutes', strtotime($to))); // 5 minutes before

        $logger->info($to. ' to');
        $logger->info($from. ' from');

        // Build the search criteria
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('status', 'pending', 'eq')
            ->addFilter('created_at', $from, 'gteq')
            ->addFilter('created_at', $to, 'lteq')
            ->addSortOrder($this->sortOrderBuilder->setField('entity_id')->setDescendingDirection()->create())
            ->setPageSize(100)
            ->setCurrentPage(1)
            ->create();

        // Retrieve orders list based on search criteria
        $ordersList = $this->orderRepository->getList($searchCriteria);

        // Process the orders
        foreach ($ordersList->getItems() as $order) {
             $logger->info($order->getId(). ' order id');
             $logger->info($order->getCustomerId(). ' customer id');
            // Get the current date and time
            $cancellationDateTime = date('Y-m-d H:i:s');
            $logger->info($cancellationDateTime. ' cancellationDateTime id');

            // Check if the order is pending for more than 5 minutes
            $createdAt = $order->getCreatedAt();
            $logger->info($createdAt. ' createdAt id');
            $orderCreatedAt = strtotime($createdAt);
            $currentTime = time();
            $timeDifference = $currentTime - $orderCreatedAt;

            $logger->info($timeDifference. ' timeDifference id');

            if ($order->getStatus() === 'pending' && $timeDifference > 300) {
                // Cancel the order if it's pending for more than 5 minutes
                $this->orderManagement->cancel($order->getId());
                 $logger->info('order canceled');
            }
        }

        $currentTime = $this->dateTime->gmtDate();
        $fiveMinutesAgo = date('Y-m-d H:i:s', strtotime('-5 minutes', strtotime($currentTime)));
        $orderCollection = $this->_orderCollectionFactory->create();
        $orderCollection->addFieldToFilter('status', ['eq' => 'pending']);
        $orderCollection->addFieldToFilter('created_at', ['gteq' => $fiveMinutesAgo]);

        $orderIds = [];
        foreach ($orderCollection as $order) {
            $orderIds[] = $order->getId();
             $logger->info(print_r($orderIds,true).'order ids');
             $logger->info($order->getCustomerId(). ' customer ids');
        }
    }
}
