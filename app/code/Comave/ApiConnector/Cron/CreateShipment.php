<?php
declare(strict_types=1);

namespace Comave\ApiConnector\Cron;

use AllowDynamicProperties;

#[AllowDynamicProperties]
class CreateShipment
{
    public function __construct(
        \Magento\Sales\Model\OrderFactory $orderFactory,
        \Webkul\Marketplace\Model\OrdersFactory $MpOrdersModel,
        \Magento\Customer\Model\CustomerFactory $customerModel,
        \Comave\ApiConnector\Helper\Data $dataHelper
    ) {
        $this->orderFactory = $orderFactory;
        $this->MpOrdersModel = $MpOrdersModel;
        $this->customerModel = $customerModel;
        $this->dataHelper = $dataHelper;
    }

    public function execute()
    {
        $orderDetails = $this->dataHelper->getPickupOrders();
        if(!empty($orderDetails['data'])){
            foreach($orderDetails['data'] as $value){
                $updateData = array();
                $sellerData = array();

                $incrId = $value['magento_order_id'];
                $orderModel = $this->orderFactory->create();
                $orderInfo = $orderModel->loadByIncrementId($incrId);
                $orderId = $orderInfo->getId();
                $websiteid=$orderInfo->getStore()->getWebsiteId();

                foreach ($value['seller'] as $data) {
                    $Data = array();
                    $comaveuId = $data['commave_uuid'];
                    $Data['commave_uuid'] = $comaveuId;
                    //$Data['to_sync'] = 1;
                    if($data['to_sync'] == 1){
                        $customer = $this->customerModel->create()->getCollection()
                                            ->addAttributeToSelect('*')
                                            ->addAttributeToFilter('commave_uuid',$comaveuId)
                                            ->addAttributeToFilter('website_id', array('eq' => $websiteid))
                                            ->load();

                        if($customer->count()) {
                            foreach($customer as $cust){
                                $sellerId = $cust->getId();
                            }

                            if($sellerId) {
                                $orderCollection = $this->MpOrdersModel->create()
                                                    ->getCollection()
                                                    ->addFieldToFilter(
                                                        'order_id',
                                                        $orderId
                                                    )
                                                    ->addFieldToFilter(
                                                        'seller_id',
                                                        $sellerId
                                                    );

                                if($orderCollection->count()>0){

                                    foreach ($orderCollection as $row) {

                                        //$row->setState("complete")->setStatus("in_transit");
                                        $row->setOrderState("In Transit");
                                        $row->save();

                                        if($row->getOrderState() == "In Transit"){
                                            $Data['to_sync'] = 0;
                                        }
                                    }
                                }
                            }
                        }

                        $sellerData[] = $Data;
                        $updateData['magento_order_id'] = $incrId;
                        $updateData['seller'] = $sellerData;
                        $postCustData = json_encode($updateData);
                        $resp = $this->dataHelper->updatePickup($postCustData);
                    }
                }
            }
        }
    }
}
