<?php
declare(strict_types=1);

namespace Comave\ApiConnector\Console\Command;

use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Comave\ApiConnector\Cron\CurrencyRateSync as CurrencyRateSyncCron;

class CurrencyRateSync extends Command
{
    /**
     * @param CurrencyRateSyncCron $currencyRateSync
     * @param State $state
     * @param string|null $name
     */
    public function __construct(
        private readonly CurrencyRateSyncCron $currencyRateSync,
        private readonly State $state,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws \Zend_Log_Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Started CurrencyRateSync command</info>');

        $this->state->setAreaCode(Area::AREA_CRONTAB);

        $this->currencyRateSync->execute();
        $output->writeln('<info>Finished CurrencyRateSync command</info>');

        return Cli::RETURN_SUCCESS;
    }

    /**
     * @return void
     */
    protected function configure(): void
    {
        $this->setDescription('Lix Api Currency Rates Sync');
        parent::configure();
    }
}
