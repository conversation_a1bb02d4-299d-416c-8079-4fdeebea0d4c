<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */

namespace Comave\ApiConnector\Controller\Order;

use AllowDynamicProperties;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\UrlInterface;

/**
 * Webkul Marketplace Order Email Controller.
 */
#[AllowDynamicProperties]
class Accept extends Action
{
    protected $orderRepository;
    protected $jsonFactory;
    protected $resultRedirectFactory;
    protected $urlBuilder;

    public function __construct(
        Context $context,
        \Webkul\Marketplace\Model\SaleslistFactory $saleslistFactory,
        \Magento\Customer\Model\CustomerFactory $customerModel,
        \Magento\Framework\ObjectManagerInterface $objectManager,
        \Webkul\Marketplace\Model\OrdersFactory $MpOrdersModel,
        \Comave\ApiConnector\Helper\Data $dataHelper,
        \Magento\Framework\Registry $registry,
        \Magento\Sales\Model\Order\Email\Sender\InvoiceSender $invoiceSender,
        \Magento\Sales\Api\OrderRepositoryInterface $orderRepository,
        JsonFactory $jsonFactory,
        \Magento\Framework\Controller\Result\RedirectFactory $resultRedirectFactory,
        UrlInterface $urlBuilder,
        \Magento\Sales\Model\Order\Status\HistoryFactory $historyFactory
    ){
        parent::__construct($context);
        $this->saleslistFactory = $saleslistFactory;
        $this->customerModel = $customerModel;
        $this->MpOrdersModel = $MpOrdersModel;
        $this->objectManager = $objectManager;
        $this->_invoiceSender = $invoiceSender;
        $this->_coreRegistry = $registry;
        $this->orderRepository = $orderRepository;
        $this->historyFactory = $historyFactory;
        $this->dataHelper = $dataHelper;
        $this->jsonFactory = $jsonFactory;
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->urlBuilder = $urlBuilder;

    }
    /**
     * Marketplace send order email to buyer controller.
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
            $order_approval_status = 'false';
            $isCpture = 0;
            $comment = '';
            $order_delay_time = 0;
            $order_comment = '';
            $comment = $this->getRequest()->getParam('comment');
            $order_delay_time = $this->getRequest()->getParam('orderdelaytime');

            if(!empty($comment) || !empty($order_delay_time)){
                $order_comment = '<div class="custom_order_comment">
                                    <span class="custom_comment_text"><strong>Comment:</strong> ' . $comment . '</span>
                                    <span class="order_delay_time"><strong>Delay time:</strong> ' . $order_delay_time . '</span>
                                </div>';
            }

            $orderId = $this->getRequest()->getParam('id');
            $order = $this->orderRepository->get($orderId);
            $orderIncrementId = $order->getIncrementId();
            $order_approval_status = $order->getData('order_approval_enabled');
            $autoInv = $this->dataHelper->getAutoInvoice();
            $payment = $order->getPayment();
            $method = $payment->getMethodInstance();
            $paymentMethodCode = $payment->getMethod();

            foreach($order->getAllVisibleItems() as $item){
                $prodid = $item->getProductId();
                $seller = $this->getUserInfo($prodid,$order);
                   if(!empty($seller)){
                      $seller_id[] = $seller['id'];
                   }
            }

            $sellers = array_unique($seller_id);

            foreach($sellers as $sellerId){
                if($order_approval_status == 1){
                    if($autoInv == 1){
                        if($paymentMethodCode == "stripe_payments"){
                            $isCpture = 1;
                            $invoiceID = $this->doInvoiceExecution($order,$sellerId,$isCpture);
                            if($invoiceID){
                                $order->setData('order_approval_enabled', false);
                                $history = $this->historyFactory->create();
                                $history->setParentId($orderId)
                                    ->setComment($order_comment)
                                    ->setIsCustomerNotified(true)
                                    ->setIsVisibleOnFront(true);

                                    $order->addStatusHistory($history);
                                $order->save();
                            }

                            try {
                                $this->messageManager->addSuccessMessage(
                                    __('You have accepted order '. $orderIncrementId . ' .Please start with the order preparation.')
                                );
                            } catch (\Magento\Framework\Exception\LocalizedException $e) {
                                $this->messageManager->addErrorMessage($e->getMessage());
                            }
                            $resultJson = $this->jsonFactory->create();
                            $resultJson->setData([
                                'redirectUrl' =>$this->urlBuilder->getUrl(
                                    'marketplace/order/view',
                                    [
                                        'id' => $order->getEntityId(),
                                        '_secure' => $this->getRequest()->isSecure(),
                                    ]
                                )
                            ]);
                            return $resultJson;
                        }
                    }
                }
            }
    }

    public function getUserInfo($id,$order)
    {
        $sellerId = 0;
        $orderId = $order->getId();

         $marketplaceSalesCollection = $this->saleslistFactory->create()
             ->getCollection()
             ->addFieldToFilter(
                 'mageproduct_id',
                 ['eq' => $id]
             )
             ->addFieldToFilter(
                 'order_id',
                 ['eq' => $orderId]
             );
         if (count($marketplaceSalesCollection)) {

             foreach ($marketplaceSalesCollection as $mpSales) {
                 $sellerId = $mpSales->getSellerId();
             }
         }
         if ($sellerId > 0) {

             $customer = $this->customerModel->create()->load($sellerId);
             if ($customer) {

                 $returnArray = [];
                 $returnArray['name'] = $customer->getName();
                 $returnArray['id'] = $sellerId;
                 $returnArray['uid'] = $customer->getData('commave_uuid');

                 return $returnArray;
             }
        }

    }

    public function doInvoiceExecution($order,$sellerId,$isCpture)
    {

        $orderId = $order->getId();

        $invoiceId = '';

        $paymentCode = '';
        if ($order->getPayment()) {
            $paymentCode = $order->getPayment()->getMethod();
        }

        $itemsData = $this->getCurrentSellerItemsData(
            $orderId,
            $sellerId,
            $paymentCode
        );


         $items = $itemsData['items'];
         $currencyRate = $itemsData['currencyRate'];
         $codCharges = $itemsData['codCharges'];
         $tax = $itemsData['tax'];
         $couponAmount = $itemsData['couponAmount'];

         $shippingAmount = $order->getShippingAmount();

         $invoice = $this->objectManager->create(
                         \Magento\Sales\Model\Service\InvoiceService::class
                     )->prepareInvoice($order, $items);
         if (!$invoice->getTotalQty()) {

             throw new \Magento\Framework\Exception\LocalizedException(
                         __("The invoice can't be created without products. Add products and try again.")
             );
         }

         $currentCouponAmount = $currencyRate * $couponAmount;
         $currentShippingAmount = $currencyRate * $shippingAmount;
         $currentTaxAmount = $currencyRate * $tax;
         $currentCodcharges = $currencyRate * $codCharges;

         $invoice->setBaseDiscountAmount(-$couponAmount);
         $invoice->setDiscountAmount(-$currentCouponAmount);
         $invoice->setShippingAmount($currentShippingAmount);
         $invoice->setBaseShippingInclTax($shippingAmount);
         $invoice->setBaseShippingAmount($shippingAmount);
         if ($paymentCode == 'mpcashondelivery') {
             $invoice->setMpcashondelivery($currentCodcharges);
             $invoice->setBaseMpcashondelivery($codCharges);
         }
         $invoice->setGrandTotal(
             $invoice->getSubtotal() +
             $currentShippingAmount +
             $currentCodcharges +
             $currentTaxAmount -
             $currentCouponAmount
         );
         $invoice->setBaseGrandTotal(
             $invoice->getBaseSubtotal() +
             $shippingAmount +
             $codCharges +
             $tax -
             $couponAmount
         );

         if($this->_coreRegistry->registry('current_invoice')){

             $this->_coreRegistry->unregister('current_invoice');
         }

         $this->_coreRegistry->register('current_invoice', $invoice);
         if (!$invoice) {
             throw new \Magento\Framework\Exception\LocalizedException(
                 __('We can\'t save the invoice right now.')
             );
         }
         if ($isCpture) {
             $invoice->setRequestedCaptureCase(\Magento\Sales\Model\Order\Invoice::CAPTURE_ONLINE);
         }

         $invoice->register();

         //$invoice->getOrder()->setCustomerNoteNotify(!empty($sendEmail));
         $invoice->getOrder()->setIsInProcess(true);

         $transactionSave = $this->objectManager->create(
                             \Magento\Framework\DB\Transaction::class
                         )->addObject(
                             $invoice
                         )->addObject(
                             $invoice->getOrder()
                         );
         $transactionSave->save();

         $invoiceId = $invoice->getId();

         $this->_invoiceSender->send($invoice);

                    /*update mpcod table records*/
                if ($invoiceId != '') {
                    if ($paymentCode == 'mpcashondelivery') {
                        $saleslistColl = $this->saleslistFactory->create()
                        ->getCollection()
                        ->addFieldToFilter(
                            'order_id',
                            $orderId
                        )
                        ->addFieldToFilter(
                            'seller_id',
                            $sellerId
                        );
                        foreach ($saleslistColl as $saleslist) {
                            $saleslist->setCollectCodStatus(1);
                            $saleslist->save();
                        }
                    }

                    $trackingcol1 = $this->MpOrdersModel->create()
                    ->getCollection()
                    ->addFieldToFilter(
                        'order_id',
                        $orderId
                    )
                    ->addFieldToFilter(
                        'seller_id',
                        $sellerId
                    );
                    foreach ($trackingcol1 as $row) {
                        $invoiceIds = explode(',', $row->getInvoiceId());
                        array_push($invoiceIds, $invoiceId);
                        $row->setInvoiceId(implode(',', $invoiceIds));
                        if ($row->getShipmentId()) {
                            $row->setOrderStatus('complete');
                        } else {
                            $row->setOrderStatus('processing');
                        }
                        $row->save();
                    }
                }
        return $invoiceId;
    }

    public function getCurrentSellerItemsData($orderId, $sellerId, $paymentCode)
    {

        // calculate charges for ordered items for current seller
        $codCharges = 0;
        $couponAmount = 0;
        $tax = 0;
        $currencyRate = 1;
        $sellerItemsToInvoice = [];
        $collection = $this->saleslistFactory->create()
                ->getCollection()
                ->addFieldToFilter(
                    'main_table.order_id',
                    ['eq' => $orderId]
                )
                ->addFieldToFilter(
                    'main_table.seller_id',
                    ['eq' => $sellerId]
                )
                ->getSellerOrderCollection();
        foreach ($collection as $saleproduct) {

            $orderItemId = $saleproduct->getOrderItemId();
            $orderedQty = $saleproduct->getQtyOrdered();
            $qtyToInvoice = $orderedQty - $saleproduct->getQtyInvoiced();
            $sellerItemsToInvoice[$orderItemId] = $qtyToInvoice;
            $currencyRate = $saleproduct->getCurrencyRate();
            $appliedTax = $saleproduct->getTotalTax() / $orderedQty;
            $tax = $tax + ($appliedTax * $qtyToInvoice);
            if ($saleproduct->getIsCoupon()) {
                $appliedAmount = $saleproduct->getAppliedCouponAmount() / $orderedQty;
                $couponAmount = $couponAmount + ($appliedAmount * $qtyToInvoice);
            }
        }

        // calculate shipment for the seller order if applied
        $shippingAmount = 0;
        $data = [
            'items' => $sellerItemsToInvoice,
            'currencyRate' => $currencyRate,
            'codCharges' => $codCharges,
            'tax' => $tax,
            'couponAmount' => $couponAmount,
            'shippingAmount' => $shippingAmount
        ];

        return $data;
    }
}
