<?php

namespace Comave\ApiConnector\Controller\Order;

use AllowDynamicProperties;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\UrlInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactoryInterface;


#[AllowDynamicProperties]
class Reject extends Action
{
    protected $orderRepository;
    protected $jsonFactory;
    protected $resultRedirectFactory;
    protected $urlBuilder;
    protected $customerRepository;
    protected $productRepository;
    protected $orderCollectionFactory;

    public function __construct(
        Context $context,
        \Comave\ApiConnector\Helper\Data $dataHelper,
        \Magento\Sales\Api\OrderRepositoryInterface $orderRepository,
        JsonFactory $jsonFactory,
        \Magento\Framework\Controller\Result\RedirectFactory $resultRedirectFactory,
        UrlInterface $urlBuilder,
        \Magento\Sales\Model\Order\Status\HistoryFactory $historyFactory,
        CustomerRepositoryInterface $customerRepository,
        ProductRepositoryInterface $productRepository,
        CollectionFactoryInterface $orderCollectionFactory
    ) {
        parent::__construct($context);
        $this->orderRepository = $orderRepository;
        $this->dataHelper = $dataHelper;
        $this->historyFactory = $historyFactory;
        $this->jsonFactory = $jsonFactory;
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->urlBuilder = $urlBuilder;
        $this->customerRepository = $customerRepository;
        $this->productRepository = $productRepository;
        $this->orderCollectionFactory = $orderCollectionFactory;
    }
    /**
     * Marketplace send order email to buyer controller.
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        $isPartner = 0; // TODO: correct with new logic refactor
        //$autoInv = $this->dataHelper->getAutoInvoice();
        $orderId = $this->getRequest()->getParam("id");
        $comment = $this->getRequest()->getParam("comment");


        $order = $this->orderRepository->get($orderId);
        $orderIncrementId = $order->getIncrementId();
        $order_approval_status = $order->getData("order_approval_enabled");
        if ($isPartner == 1) {
            $statusCode = "rejected";
            $history = $this->historyFactory->create();
            $history
                ->setParentId($orderId)
                ->setStatus($statusCode)
                ->setComment($comment)
                ->setIsCustomerNotified(true)
                ->setIsVisibleOnFront(true);

            $order->addStatusHistory($history);
            $order->setStatus($statusCode);
            $order->setData("order_approval_enabled", false);
            $order->save();

            // Load order collection by order ID
            $orderCollection = $this->orderCollectionFactory->create();
            $orderCollection->addFieldToFilter('entity_id', $orderId);
            $order = $orderCollection->getFirstItem();
            // Set current date and time to customer attribute
            $customerId = $order->getCustomerId();
            if ($customerId) {
                try {
                    $customer = $this->customerRepository->getById($customerId);
                    $customer->setCustomAttribute('wkv_last_order_rej', date('Y-m-d H:i:s'));
                    $this->customerRepository->save($customer);
                } catch (\Exception $e) {
                    // Handle exception
                    $this->messageManager->addError(__('An error occurred while setting rejected order date. Please try again later.'));
                }
            }

            try {
                //$this->_orderManagement->notify($order->getEntityId());
                $this->messageManager->addSuccess(__('You have rejected order.'. $orderIncrementId));
            } catch (\Magento\Framework\Exception\LocalizedException $e) {
                $this->messageManager->addError($e->getMessage());
            }
            $resultJson = $this->jsonFactory->create();
            $resultJson->setData([
                "redirectUrl" => $this->urlBuilder->getUrl(
                    "marketplace/order/view",
                    [
                        "id" => $order->getEntityId(),
                        "_secure" => $this->getRequest()->isSecure(),
                    ]
                ),
            ]);
            return $resultJson;
        }
    }
}
