<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="category_matcher" sortOrder="20" showInWebsite="1" showInStore="0" showInDefault="1" translate="label">
            <label>Category Matcher</label>
            <tab>sellers_general</tab>
            <resource>Comave_CategoryMatcher::config</resource>
            <group id="general" sortOrder="10" showInWebsite="1" showInStore="0" showInDefault="1" translate="label">
                <label>General Settings</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Enable Logging</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="endpoint" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Matcher Endpoint</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
        <section id="comave_logger">
            <group id="category_matcher" translate="label" type="text" sortOrder="600" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Category Matcher Request / Response Logging</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Logging</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <depends>
                    <field id="comave_logger/general/enabled">1</field>
                </depends>
            </group>
        </section>
    </system>
</config>
