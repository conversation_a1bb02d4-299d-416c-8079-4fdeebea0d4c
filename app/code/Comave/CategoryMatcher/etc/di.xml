<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Comave\CategoryMatcher\Api\CategoryMatcherServiceInterface"
                type="Comave\CategoryMatcher\Service\CategoryMatcher"/>

    <virtualType name="CategoryMatcher" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">CategoryMatcher</argument>
            <argument name="loggerPath" xsi:type="string">category_matcher</argument>
        </arguments>
    </virtualType>

    <type name="Comave\CategoryMatcher\Service\CategoryMatcher">
        <arguments>
            <argument xsi:type="object" name="logger">CategoryMatcher</argument>
        </arguments>
    </type>
</config>
