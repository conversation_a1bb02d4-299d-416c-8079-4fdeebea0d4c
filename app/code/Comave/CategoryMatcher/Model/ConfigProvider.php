<?php

declare(strict_types=1);

namespace Comave\CategoryMatcher\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;

class ConfigProvider
{
    private const string XML_PATH_ENABLED = 'category_matcher/general/enabled';
    private const string XML_PATH_ENDPOINT = 'category_matcher/general/endpoint';

    /**
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(private readonly ScopeConfigInterface $scopeConfig)
    {
    }

    /**
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_ENABLED);
    }

    /**
     * @return string|null
     */
    public function getEndpoint(): ?string
    {
        return $this->scopeConfig->getValue(self::XML_PATH_ENDPOINT);
    }
}
