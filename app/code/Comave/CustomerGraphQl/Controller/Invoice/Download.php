<?php
declare(strict_types=1);

namespace Comave\CustomerGraphQl\Controller\Invoice;

use Comave\CustomerGraphQl\Model\Service\InvoicePdfSecurity;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\Raw;
use Magento\Framework\Controller\Result\RawFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem\Io\File;
use Magento\Framework\Exception\NotFoundException;

class Download implements HttpGetActionInterface
{
    public function __construct(
        protected RequestInterface $request,
        protected RawFactory $resultRawFactory,
        protected DirectoryList $directoryList,
        protected File $file,
        protected InvoicePdfSecurity $pdfSecurity
    ) {}

    /**
     * @return Raw
     * @throws NotFoundException
     */
    public function execute(): Raw
    {
        $token = $this->request->getParam('token');
        if (!$token) {
            throw new NotFoundException(__('Page not found.'));
        }

        try {
            $fileName = $this->pdfSecurity->decodeTokenToFilename($token);
            $filePath = $this->pdfSecurity->getInvoicePdfExportFolder() . $fileName;

            if (!$this->file->fileExists($filePath)) {
                throw new NotFoundException(__('File not found.'));
            }

            /** @var Raw $resultRaw */
            $resultRaw = $this->resultRawFactory->create();
            $resultRaw->setHeader('Content-Type', 'application/pdf')
                ->setHeader('Content-Disposition', 'attachment; filename="' . $fileName . '"')
                ->setContents(file_get_contents($filePath));

            return $resultRaw;
        } catch (\Exception $e) {
            //TODO: TBD what would be a proper response here
            $resultRaw = $this->resultRawFactory->create();
            $resultRaw->setHttpResponseCode(404);
            $resultRaw->setContents($e->getMessage());
            return $resultRaw;

            //or throw new NotFoundException(__('Invalid/expired token or file not found.'));
        }
    }
}
