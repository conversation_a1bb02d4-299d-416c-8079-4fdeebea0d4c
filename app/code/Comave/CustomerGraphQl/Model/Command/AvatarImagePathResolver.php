<?php

declare(strict_types=1);

namespace Comave\CustomerGraphQl\Model\Command;

use Magento\Store\Model\StoreManagerInterface;

class AvatarImagePathResolver
{
    /**
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(private readonly StoreManagerInterface $storeManager)
    {
    }

    /**
     * @param string $relativeProfilePath
     * @return string
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function resolve(string $relativeProfilePath): string
    {
        $mediaDir = $this->storeManager->getStore()
            ->getBaseUrl(
                \Magento\Framework\UrlInterface::URL_TYPE_MEDIA
            );

        return sprintf(
            '%s/customer/%s',
            rtrim($mediaDir,'/'),
            ltrim($relativeProfilePath,'/')
        );
    }
}
