<?php
declare(strict_types=1);

namespace Comave\CustomerGraphQl\Model\Resolver;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\GraphQl\Model\Query\ContextInterface;
use Comave\Customer\Model\Source\RefundMethod;

class UpdateRefundMethod implements ResolverInterface
{
    /**
     * @param CustomerRepositoryInterface $customerRepository
     * @param Session $customerSession
     */
    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly Session $customerSession,
        private readonly RefundMethod $refundMethod,
    ) {
    }

    /**
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array|null
     * @throws GraphQlInputException
     * @throws LocalizedException
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): ?array {
        if (!$context->getExtensionAttributes()->getIsCustomer() || !$context->getUserId()) {
            throw new LocalizedException(__('You must be logged in as a customer to proceed.'));
        }

        if (!isset($args['input']['refund_method'])) {
            throw new GraphQlInputException(__('Refund method is required.'));
        }

        if (!$this->isValidRefundMethod($args['input']['refund_method'])) {
            throw new GraphQlInputException(__('Invalid refund method.'));
        }

        try {
            $customerId = $context->getUserId();
            $customer = $this->customerRepository->getById($customerId);
            $customer->setCustomAttribute('refund_method', $args['input']['refund_method']);
            $this->customerRepository->save($customer);

            return [
                'success' => true,
                'message' => __('Refund method updated successfully.')
            ];
        } catch (LocalizedException $e) {
            throw new GraphQlInputException(__($e->getMessage()));
        }
    }

    /**
     * @param string $refundMethod
     * @return bool
     */
    private function isValidRefundMethod(string $refundMethod): bool
    {
        $validMethods = array_column($this->refundMethod->getAllOptions(), 'value');
        return in_array($refundMethod, $validMethods, true);
    }
}
