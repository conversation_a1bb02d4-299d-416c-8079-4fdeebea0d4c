<?php
namespace Comave\CustomerGraphQl\Model\Resolver;

use Comave\ComaveApi\Api\ReferFriendManagementInterface;
use Comave\Logger\Model\ComaveLogger;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class ReferralCode implements ResolverInterface
{
    public function __construct(
        private readonly ReferFriendManagementInterface $referFriendManagement,
        private readonly ComaveLogger $comaveLogger
    ) {}

    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (!isset($value['model'])) {
            throw new LocalizedException(__('"model" value should be specified'));
        }

        $customer = $value['model'];

        if (!$customer instanceof CustomerInterface || !$customer->getId()) {
            return null;
        }

        try {
            return $this->referFriendManagement->referralCode($customer->getId());
        } catch (NoSuchEntityException $e) {
            throw new GraphQlNoSuchEntityException(__('The customer does not exist anymore.'));
        } catch (LocalizedException|\Exception $e) {
            throw new GraphQlInputException(__('Something went wrong while generating the referral code.'));
        }
    }
}
