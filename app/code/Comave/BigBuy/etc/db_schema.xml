<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="comave_bigbuy_order" resource="default" engine="innodb"
           comment="Comave Seller Onboarding Category Mapping">
        <column xsi:type="int" name="link_id" padding="10" unsigned="true" nullable="false" identity="true"
                comment="Link ID"/>
        <column xsi:type="int" name="order_id" padding="10" unsigned="true" nullable="false" identity="false"
                comment="Order ID"/>
        <column xsi:type="varchar" name="bigbuy_order_id" nullable="false" length="255"
                comment="BigBuy Order ID"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="link_id"/>
        </constraint>
        <constraint xsi:type="unique" referenceId="COMAVE_BIGBUY_ORDER_CONSTRAINT">
            <column name="order_id"/>
            <column name="bigbuy_order_id"/>
        </constraint>
        <constraint xsi:type="foreign"
                    referenceId="COMAVE_BIGBUY_ORDER_ID_SALES_ORDER_ENTITY_ID"
                    table="comave_bigbuy_order" column="order_id"
                    referenceTable="sales_order" referenceColumn="entity_id"
                    onDelete="CASCADE"/>
        <index referenceId="COMAVE_BIGBUY_ORDER_LINK_ORDER_ID_INDEX" indexType="btree">
            <column name="order_id"/>
        </index>
        <index referenceId="COMAVE_BIGBUY_ORDER_LINK_BIGBUY_ORDER_ID_INDEX" indexType="btree">
            <column name="bigbuy_order_id"/>
        </index>
    </table>
</schema>
