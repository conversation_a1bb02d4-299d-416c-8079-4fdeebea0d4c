<?php
declare(strict_types=1);

namespace Comave\BigBuy\Observer;

use Comave\BigBuy\Service\Seller\IntegrationTypeHandler;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class SetSellerIntegrationType implements ObserverInterface
{
    /**
     * @param \Comave\BigBuy\Service\Seller\IntegrationTypeHandler $integrationTypeHandler
     */
    public function __construct(
        private readonly IntegrationTypeHandler $integrationTypeHandler
    ) {
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute(Observer $observer): void
    {
        $this->integrationTypeHandler->execute([$observer->getProduct()]);
    }
}