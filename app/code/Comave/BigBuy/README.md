## Connect all BigBuy products to Seller API Integration.

### Run the following CLI command only once in every environment.

- `bin/magento bigbuy:set:integration-type`

### Run the following CLI command to get the correct BigBuy categories

- `bin/magento bigbuy:set-product:category-list`

### <PERSON><PERSON>Job to get the BigBuy categories
- `comave_bigbuy_category_collect` - require analysis to determine when to be scheduled, now it's disabled,  it can be executed only via Cronjob Manager  