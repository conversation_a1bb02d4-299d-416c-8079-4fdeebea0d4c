<?php

declare(strict_types=1);

namespace Comave\BigBuy\Console\Command;

use Comave\BigBuy\Service\Order\SynchroniseService;
use Comave\BigBuy\Service\Seller\IntegrationTypeHandler;
use Comave\SellerApi\Service\SellerIdentity;
use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Webkul\Marketplace\Helper\Data;

class SetSellerIntegrationType extends Command
{
    /**
     * @param \Webkul\Marketplace\Helper\Data $marketplaceHelper
     * @param \Comave\SellerApi\Service\SellerIdentity $sellerIdentity
     * @param \Comave\BigBuy\Service\Seller\IntegrationTypeHandler $integrationTypeHandler
     * @param string|null $name
     */
    public function __construct(
        private readonly Data $marketplaceHelper,
        private readonly SellerIdentity $sellerIdentity,
        private readonly IntegrationTypeHandler $integrationTypeHandler,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    /**
     * @param \Symfony\Component\Console\Input\InputInterface $input
     * @param \Symfony\Component\Console\Output\OutputInterface $output
     * @return int
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $products = $this->collectSellerProducts();
        $this->integrationTypeHandler->execute($products);
        $output->writeln('<info>Finished processing, please review the comave logger file for any errors</info>');

        return Cli::RETURN_SUCCESS;
    }

    /**
     * @return array
     */
    private function collectSellerProducts(): array
    {
        $products = [];
        $sellerId = $this->sellerIdentity->identify(SynchroniseService::BIG_BUY_IDENTIFIER);;
        if (!empty($sellerId)) {
            $products = $this->marketplaceHelper->getSellerProducts($sellerId);
        }

        return $products;
    }

    /**
     * @return void
     */
    protected function configure(): void
    {
        $this->setDescription('Set Product Integration Type');
        parent::configure();
    }
}