<?php
declare(strict_types=1);

namespace Comave\BigBuy\Console\Command;

use Comave\BigBuy\Service\Order\SynchroniseService;
use Comave\CategoryMatcher\Api\CategoryMatcherServiceInterface;
use Comave\SellerApi\Service\SellerIdentity;
use Comave\ShopifyAccounts\Console\ProgressBarFactory;
use Exception;
use Magento\Catalog\Api\CategoryLinkManagementInterface;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\Console\Cli;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Webkul\Marketplace\Helper\Data;


class SetProductCategoryList extends Command
{
    private const int BATCH_SIZE = 10;

    private array $progressBars = [];

    /**
     * @param \Webkul\Marketplace\Helper\Data $marketplaceHelper
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Comave\SellerApi\Service\SellerIdentity $sellerIdentity
     * @param \Symfony\Component\Console\Helper\ProgressBarFactory $progressBarFactory
     * @param \Comave\CategoryMatcher\Api\CategoryMatcherServiceInterface $categoryMatcherService
     * @param \Magento\Catalog\Api\CategoryLinkManagementInterface $categoryLinkManagement
     * @param \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory
     * @param string|null $name
     */
    public function __construct(
        private readonly Data $marketplaceHelper,
        private readonly LoggerInterface $logger,
        private readonly SellerIdentity $sellerIdentity,
        private readonly ProgressBarFactory $progressBarFactory,
        private readonly CategoryMatcherServiceInterface $categoryMatcherService,
        private readonly CategoryLinkManagementInterface $categoryLinkManagement,
        private readonly CollectionFactory $productCollectionFactory,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    /**
     * @return void
     */
    protected function configure(): void
    {
        $this->setDescription('Set Product Category List');
        parent::configure();
    }

    /**
     * @param \Symfony\Component\Console\Input\InputInterface $input
     * @param \Symfony\Component\Console\Output\OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        /** @var $progressBar \Comave\ShopifyAccounts\Console\ProgressBar */
        $progressBar = $this->progressBarFactory->create();
        $progressBar->setOutput($output);

        $batches = $this->getBatches();

        $progressBar->info(
            sprintf(
                "\nProcessing categories for %s batches having %s products per batch",
                count($batches),
                self::BATCH_SIZE
            )
        );
        $progressBar->start('processing', count($batches));
        foreach ($batches as $batch) {
            $this->categoryMatcherService->processCategories($batch);
            $progressBar->advance('processing');
        }
        $progressBar->finish('processing');

        $progressBar->info(
            sprintf(
                "\nSetting categories for %s batches having %s products per batch",
                count($batches),
                self::BATCH_SIZE
            )
        );
        $progressBar->start('setting', count($batches));
        foreach ($batches as $batch) {
            $this->setProductCategoryList($batch);
            $progressBar->advance('setting');
        }
        $progressBar->finish('setting');

        return Cli::RETURN_SUCCESS;
    }

    /**
     * @return array
     */
    private function collectSellerProducts(): array
    {
        $products = [];
        $sellerId = $this->sellerIdentity->identify(SynchroniseService::BIG_BUY_IDENTIFIER);
        if (!empty($sellerId)) {
            $products = $this->marketplaceHelper->getSellerProducts($sellerId);
        }

        return $products;
    }

    /**
     * @param array $products
     * @return void
     */
    private function setProductCategoryList(array $products): void
    {
        $matchResult = $this->categoryMatcherService->matchCategories($products);
        foreach ($matchResult as $sku => $categoryIds) {
            try {
                $this->categoryLinkManagement->assignProductToCategories(
                    $sku,
                    $categoryIds
                );
            } catch (Exception $exception) {
                $this->logger->warning('Cannot assign product to category list', [
                    'sku' => $sku,
                    'category_ids' => $categoryIds,
                    'message' => $exception->getMessage(),
                ]);
            }
        }
    }

    /**
     * @return array
     */
    private function getBatches(): array
    {
        $products = [];
        $sellerProducts = $this->collectSellerProducts();
        $productCollection = $this->productCollectionFactory->create();
        $productCollection->addFieldToFilter('entity_id', ['in' => $sellerProducts]);
        $productCollection->addFieldToSelect(['name']);
        foreach ($productCollection as $item) {
            $products[] = [
                'sku' => $item->getSku(),
                'word' => $item->getName(),
            ];
        }

        return array_chunk($products, self::BATCH_SIZE);
    }
}