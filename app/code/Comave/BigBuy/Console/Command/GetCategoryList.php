<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Console\Command;

use Comave\BigBuy\Service\Category\Collector;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\App\Area;
use Magento\Framework\App\State\Proxy as State;
use Magento\Framework\Console\Cli;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Registry;
use Magento\Store\Model\Store;
use Magento\Store\Model\StoreManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBarFactory;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class GetCategoryList extends Command
{
    /**
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\App\State\Proxy $appState
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Comave\BigBuy\Service\Category\Collector $categoryCollector
     * @param string|null $name
     */
    public function __construct(
        private readonly Registry $registry,
        private readonly State $appState,
        private readonly StoreManagerInterface $storeManager,
        private readonly Collector $categoryCollector,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    /**
     * @return void
     */
    protected function configure(): void
    {
        $this->setDescription('Retrieve BigBuy Category List');
        parent::configure();
    }

    /**
     * @param \Symfony\Component\Console\Input\InputInterface $input
     * @param \Symfony\Component\Console\Output\OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->setupProcess();
            $this->categoryCollector->setOutput($output);
            $this->categoryCollector->execute();

            return Cli::RETURN_SUCCESS;
        } catch (LocalizedException|Exception $e) {
            $output->writeln('Error:'.$e->getMessage());

            return Cli::RETURN_FAILURE;
        }
    }

    /**
     * Set process settings.
     * @throws LocalizedException
     */
    private function setupProcess(): void
    {
        $this->registry->register('isSecureArea', true);
        $this->appState->setAreaCode(Area::AREA_ADMINHTML);
        $this->storeManager->setCurrentStore(Store::DEFAULT_STORE_ID);
    }
}
