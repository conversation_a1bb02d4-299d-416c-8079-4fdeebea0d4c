<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Plugin\Block\Adminhtml\Order\View;

use Magento\Sales\Block\Adminhtml\Order\View\Info;

class InfoPlugin
{
    /**
     * @param \Magento\Sales\Block\Adminhtml\Order\View\Info $subject
     * @return void
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function beforeToHtml(Info $subject): void
    {
        if ($subject->getTemplate() === 'Magento_Sales::order/view/info.phtml') {
            $subject->setTemplate('Comave_BigBuy::order/view/info.phtml');
        }
    }
}