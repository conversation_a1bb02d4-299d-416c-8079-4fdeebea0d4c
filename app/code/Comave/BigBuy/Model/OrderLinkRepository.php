<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Model;

use Comave\BigBuy\Api\Data\OrderLinkInterface;
use Comave\BigBuy\Api\Data\OrderLinkInterfaceFactory;
use Comave\BigBuy\Api\OrderLinkRepositoryInterface;
use Comave\BigBuy\Model\ResourceModel\OrderLink as OrderLinkResourceModel;
use Exception;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

class OrderLinkRepository implements OrderLinkRepositoryInterface
{
    /**
     * @var OrderLinkInterface[]
     */
    private array $cache = [];

    /**
     * @param \Comave\BigBuy\Api\Data\OrderLinkInterfaceFactory $factory
     * @param \Comave\BigBuy\Model\ResourceModel\OrderLink $resource
     */
    public function __construct(
        private readonly OrderLinkInterfaceFactory $factory,
        private readonly OrderLinkResourceModel $resource,
    ) {
    }

    /**
     * @param \Comave\BigBuy\Api\Data\OrderLinkInterface $orderLink
     * @return \Comave\BigBuy\Api\Data\OrderLinkInterface
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    public function save(OrderLinkInterface $orderLink): OrderLinkInterface
    {
        try {
            $this->resource->save($orderLink);
        } catch (Exception $exception) {
            throw new CouldNotSaveException(
                __($exception->getMessage())
            );
        }

        return $orderLink;
    }

    /**
     * @param int $linkId
     * @return \Comave\BigBuy\Api\Data\OrderLinkInterface|null
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $linkId): ?OrderLinkInterface
    {
        if (!isset($this->cache[$linkId])) {
            $orderLink = $this->factory->create();
            $this->resource->load($orderLink, $linkId);
            if (!$orderLink->getId()) {
                throw new NoSuchEntityException(
                    __('The Order Link with the ID "%1" does not exist . ', $linkId)
                );
            }
            $this->cache[$linkId] = $orderLink;
        }

        return $this->cache[$linkId];
    }

    /**
     * @param \Comave\BigBuy\Api\Data\OrderLinkInterface $orderLink
     * @return void
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     */
    public function delete(OrderLinkInterface $orderLink): void
    {
        try {
            $id = $orderLink->getLinkId();
            $this->resource->delete($orderLink);
            unset($this->cache[$id]);
        } catch (Exception $exception) {
            throw new CouldNotDeleteException(
                __($exception->getMessage())
            );
        }
    }

    /**
     * @param int $linkId
     * @return void
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function deleteByLinkId(int $linkId): void
    {
        $this->delete($this->get($linkId));
    }

    /**
     * @return void
     */
    public function clear(): void
    {
        $this->cache = [];
    }

    /**
     * @param int $orderId
     * @return int|null
     */
    public function getByOrderId(int $orderId): ?int
    {
        $orderLink = $this->factory->create();
        try {
            $this->resource->load($orderLink, $orderId, 'order_id');
        } catch (LocalizedException $e) {
            return null;
        }

        return $orderLink->getId();
    }
}
