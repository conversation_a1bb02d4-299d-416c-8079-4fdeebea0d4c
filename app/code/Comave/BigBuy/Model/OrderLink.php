<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Model;

use Comave\BigBuy\Api\Data\OrderLinkInterface;
use Magento\Framework\Api\ExtensionAttributesInterface;
use Magento\Framework\Model\AbstractExtensibleModel;

class OrderLink extends AbstractExtensibleModel implements OrderLinkInterface
{
    /**
     * Cache tag
     *
     * @var string
     */
    public const string CACHE_TAG = 'bigbuy_order_link';
    /**
     * Cache tag
     *
     * @var string
     * phpcs:disable PSR2.Classes.PropertyDeclaration.Underscore,PSR12.Classes.PropertyDeclaration.Underscore
     */
    protected $_cacheTag = self::CACHE_TAG;
    /**
     * Event prefix
     *
     * @var string
     */
    protected $_eventPrefix = 'bigbuy_order_link';
    /**
     * Event object
     *
     * @var string
     */
    protected $_eventObject = 'bigbuy_order_link';
    //phpcs:enable

    /**
     * Get identities
     *
     * @return array
     */
    public function getIdentities(): array
    {
        return [sprintf("%s_%s", self::CACHE_TAG, $this->getId())];
    }

    /**
     * @return \Magento\Framework\Api\ExtensionAttributesInterface|null
     */
    public function getExtensionAttributes(): ?ExtensionAttributesInterface
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * @param \Magento\Framework\Api\ExtensionAttributesInterface $extensionAttributes
     * @return \Comave\BigBuy\Model\OrderLink
     */
    public function setExtensionAttributes(ExtensionAttributesInterface $extensionAttributes): OrderLink
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * @param $id
     * @return self
     */
    public function setLinkId($id): OrderLinkInterface
    {
        return $this->setData(self::LINK_ID, $id);
    }

    /**
     * @return int|null
     */
    public function getLinkId(): ?int
    {
        return (int)$this->getData(self::LINK_ID);
    }

    /**
     * @param $orderId
     * @return self
     */
    public function setOrderId($orderId): OrderLinkInterface
    {
        return $this->setData(self::ORDER_ID, $orderId);
    }

    /**
     * Get Order ID
     *
     * @return int
     */
    public function getOrderId(): int
    {
        return (int)$this->getData(self::ORDER_ID);
    }

    /**
     * @param $bigbuyOrderId
     * @return self
     */
    public function setBigbuyOrderId($bigbuyOrderId): OrderLinkInterface
    {
        return $this->setData(self::BIGBUY_ORDER_ID, $bigbuyOrderId);
    }

    /**
     * Get BigBuy Order ID
     *
     * @return null|string
     */
    public function getBigbuyOrderId(): ?string
    {
        return $this->getData(self::BIGBUY_ORDER_ID);
    }

    /**
     * @return string
     */
    public function getBigBuyOrderViewLink(): string
    {
        return sprintf('https://controlpanel.bigbuy.eu/orders/view/%s', $this->getBigbuyOrderId());
    }

    /**
     * Initialize resource model
     *
     * @return void
     * phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init(ResourceModel\OrderLink::class);
    }
}
