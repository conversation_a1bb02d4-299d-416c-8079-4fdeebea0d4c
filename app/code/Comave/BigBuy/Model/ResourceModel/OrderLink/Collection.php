<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Model\ResourceModel\OrderLink;

use Umc\Crud\Model\ResourceModel\Collection\AbstractCollection;

class Collection extends AbstractCollection
{
    /**
     * @var string
     * @codeCoverageIgnore
     */
    protected $_idFieldName = 'link_id';
    //phpcs: enable

    /**
     * Define resource model
     *
     * @return void
     * @codeCoverageIgnore
     */
    protected function _construct()
    {
        $this->_init(
            \Comave\BigBuy\Model\OrderLink::class,
            \Comave\BigBuy\Model\ResourceModel\OrderLink::class
        );
        //phpcs: enable
    }
}