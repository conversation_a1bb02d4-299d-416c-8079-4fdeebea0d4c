<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Model;

use Comave\BigBuy\Api\OrderLinkListRepositoryInterface;
use Comave\BigBuy\Model\ResourceModel\OrderLink\Collection;
use Comave\BigBuy\Model\ResourceModel\OrderLink\CollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Comave\BigBuy\Api\Data\OrderLinkSearchResultInterfaceFactory;

class OrderLinkListRepository implements OrderLinkListRepositoryInterface
{
    public function __construct(
        private readonly CollectionProcessorInterface $collectionProcessor,
        private readonly OrderLinkSearchResultInterfaceFactory $searchResultFactory,
        private readonly CollectionFactory $collectionFactory
    ) {
    }

    /**
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Comave\BigBuy\Api\Data\OrderLinkSearchResultInterfaceFactory
     */
    public function getList(SearchCriteriaInterface $searchCriteria)
    {
        /** @var Collection $collection */
        $collection = $this->collectionFactory->create();
        $this->collectionProcessor->process($searchCriteria, $collection);

        /** @var OrderLinkSearchResultInterfaceFactory $searchResult */
        $searchResult = $this->searchResultFactory->create();
        $searchResult->setSearchCriteria($searchCriteria);
        $collection->setCurPage($searchCriteria->getCurrentPage());
        $collection->setPageSize($searchCriteria->getPageSize());
        $searchResult->setTotalCount($collection->getSize());
        $searchResult->setItems($collection->getItems());

        return $searchResult;
    }
}