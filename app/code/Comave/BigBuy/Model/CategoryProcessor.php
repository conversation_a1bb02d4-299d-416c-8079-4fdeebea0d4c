<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Model;

use Comave\SellerOnboarding\Model\Category\DataSourceUiManager;
use Magento\Framework\Serialize\SerializerInterface;

class CategoryProcessor
{
    /**
     * @param \Comave\SellerOnboarding\Model\Category\DataSourceUiManager $dataSourceManager
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     */
    public function __construct(
        private readonly DataSourceUiManager $dataSourceManager,
        private readonly SerializerInterface $serializer,
    ) {
    }

    /**
     * @param int $sellerId
     * @param array $data
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function process(int $sellerId, array $data): void
    {
        if (empty($data)) {
            return;
        }

        foreach ($data as $source) {
            $dataSource = $this->dataSourceManager->getBySellerId((int)$source['id'], $sellerId);
            $dataSource->setSellerId($sellerId);
            $dataSource->setSourceCategoryId((int)$source['id']);
            $dataSource->setSourceCategoryName($source['name']);
            $dataSource->setSourceMetadata($this->serializer->serialize($source));
            $this->dataSourceManager->save($dataSource);
        }
    }
}