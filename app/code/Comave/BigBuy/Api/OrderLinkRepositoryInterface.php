<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Api;

use Comave\BigBuy\Api\Data\OrderLinkInterface;

interface OrderLinkRepositoryInterface
{
    /**
     * @param \Comave\BigBuy\Api\Data\OrderLinkInterface $orderLink
     * @return \Comave\BigBuy\Api\Data\OrderLinkInterface
     */
    public function save(OrderLinkInterface $orderLink): OrderLinkInterface;

    /**
     * @param int $linkId
     * @return \Comave\BigBuy\Api\Data\OrderLinkInterface|null
     */
    public function get(int $linkId): ?OrderLinkInterface;

    /**
     * @param \Comave\BigBuy\Api\Data\OrderLinkInterface $orderLink
     * @return void
     */
    public function delete(OrderLinkInterface $orderLink): void;

    /**
     * @param int $linkId
     * @return void
     */
    public function deleteByLinkId(int $linkId): void;

    /**
     * @return void
     */
    public function clear(): void;

    /**
     * @param int $orderId
     * @return int|null
     */
    public function getByOrderId(int $orderId): ?int;
}
