<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Api\Data;

use Magento\Framework\Api\SearchCriteriaInterface;

interface OrderLinkSearchResultInterface
{
    /**
     * Get items
     *
     * @return \Comave\BigBuy\Api\Data\OrderLinkInterface[]
     */
    public function getItems();

    /**
     * Set items
     *
     * @param \Comave\BigBuy\Api\Data\OrderLinkInterface[] $items
     * @return $this
     */
    public function setItems(array $items);

    /**
     * @param SearchCriteriaInterface $searchCriteria
     * @return $this
     */
    public function setSearchCriteria(SearchCriteriaInterface $searchCriteria);

    /**
     * @param int $count
     * @return $this
     */
    public function setTotalCount($count);
}
