<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Api\Data;

use Magento\Framework\Api\ExtensibleDataInterface;

interface OrderLinkInterface extends ExtensibleDataInterface
{
    public const string LINK_ID = 'link_id';
    public const string ORDER_ID = 'order_id';
    public const string BIGBUY_ORDER_ID = 'bigbuy_order_id';

    /**
     * @param $id
     * @return self
     */
    public function setId($id);

    /**
     * @return int
     */
    public function getId();

    /**
     * @param $id
     * @return self
     */
    public function setLinkId($id): self;

    /**
     * @return int|null
     */
    public function getLinkId(): ?int;

    /**
     * @param $orderId
     * @return self
     */
    public function setOrderId($orderId): self;

    /**
     * Get Order ID
     *
     * @return int
     */
    public function getOrderId(): int;

    /**
     * @param $bigbuyOrderId
     * @return self
     */
    public function setBigbuyOrderId($bigbuyOrderId): self;

    /**
     * Get BigBuy Order ID
     *
     * @return null|string
     */
    public function getBigbuyOrderId(): ?string;
}