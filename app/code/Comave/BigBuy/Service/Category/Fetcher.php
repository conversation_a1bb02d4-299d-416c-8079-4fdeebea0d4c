<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Category;

use Comave\BigBuy\Model\ConfigProvider;
use Comave\SellerApi\Api\ConfigurableApiInterfaceFactory;
use Comave\SellerApi\Service\RequestHandler;
use Laminas\Http\Request;
use Magento\Framework\Serialize\SerializerInterface;
use Psr\Http\Client\ClientExceptionInterface;
use Psr\Log\LoggerInterface;

class Fetcher
{
    /**
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Comave\SellerApi\Service\RequestHandler $requestHandler
     * @param \Comave\BigBuy\Model\ConfigProvider $configProvider
     * @param \Comave\SellerApi\Api\ConfigurableApiInterfaceFactory $configurableApiFactory
     */
    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly LoggerInterface $logger,
        private readonly RequestHandler $requestHandler,
        private readonly ConfigProvider $configProvider,
        private readonly ConfigurableApiInterfaceFactory $configurableApiFactory,
    ) {
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function fetch(): array
    {
        if (!$this->configProvider->isCategorySyncEnable()) {
            $this->logger->warning('BigBuy Category Sync process is disabled');

            return [];
        }

        if (empty($this->configProvider->getApiEndpoint())) {
            $this->logger->warning('BigBuy API endpoint is not configured');

            return [];
        }

        $result = [];
        $configurableApi = $this->configurableApiFactory->create([
            'method' => Request::METHOD_GET,
            'endpoint' => $this->configProvider->getCategorySyncEndpoint(),
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'Authorization' => sprintf('Bearer %s', $this->configProvider->getApiKey()),
            ],
        ]);
        try {
            $response = $this->requestHandler->handleRequest($configurableApi);
            if (!$response->hasError()) {
                $result = $this->serializer->unserialize(
                    $response->getResult()->getBody()->getContents()
                );
            }
        } catch (ClientExceptionInterface $exception) {
            $this->logger->critical("Cannot retrieve categories", []);
        }

        return $result;
    }
}