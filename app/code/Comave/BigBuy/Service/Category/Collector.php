<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Category;

use Comave\BigBuy\Model\CategoryProcessor;
use Comave\BigBuy\Service\Order\SynchroniseService;
use Comave\SellerApi\Service\SellerIdentity;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Helper\ProgressBarFactory;
use Symfony\Component\Console\Output\OutputInterface;

class Collector
{
    private const int BATCH_SIZE = 10;

    /**
     * @var \Symfony\Component\Console\Output\OutputInterface|null
     */
    private ?OutputInterface $output = null;

    /**
     * @param \Symfony\Component\Console\Helper\ProgressBarFactory $progressBarFactory
     * @param \Comave\BigBuy\Service\Category\Fetcher $categoryFetcherService
     * @param \Comave\SellerApi\Service\SellerIdentity $sellerIdentity
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Comave\BigBuy\Model\CategoryProcessor $categoryProcessor
     */
    public function __construct(
        private readonly ProgressBarFactory $progressBarFactory,
        private readonly Fetcher $categoryFetcherService,
        private readonly SellerIdentity $sellerIdentity,
        private readonly LoggerInterface $logger,
        private readonly CategoryProcessor $categoryProcessor,
    ) {
    }

    /**
     * @return void
     */
    public function execute(): void
    {
        if ($this->hasOutput()) {
            $this->getOutput()->writeln(
                sprintf(
                    "<info>%s</info>",
                    __("Fetching the category batches in groups of %1", self::BATCH_SIZE)
                )
            );
        }
        $batches = $this->getBatches();
        if ($this->hasOutput()) {
            $this->getOutput()->writeln(
                sprintf(
                    "<info>%s</info>",
                    __("Processing %1 batches having %2 categories per batch", count($batches), self::BATCH_SIZE),
                )
            );
            $this->getOutput()->writeln("");
            $progressBar = $this->getProgressBar(count($batches));
            $progressBar->start();
            $progressBar->display();
        }

        $sellerId = $this->sellerIdentity->identify(SynchroniseService::BIG_BUY_IDENTIFIER);
        foreach ($batches as $batch) {
            try {
                $this->categoryProcessor->process((int)$sellerId, $batch);
            } catch (NoSuchEntityException|LocalizedException $e) {
                $this->logger->warning($e->getMessage());
            }
            if ($this->hasOutput() && !empty($progressBar)) {
                $progressBar->advance();
            }
        }

        if ($this->hasOutput() && !empty($progressBar)) {
            $progressBar->finish();
            $this->getOutput()->writeln("\n");
            $this->getOutput()->writeln(
                sprintf(
                    '<info>%s</info>',
                    __('BigBuy categories have been collected successfully')
                )
            );
        }
    }

    /**
     * @return array
     */
    private function getBatches(): array
    {
        try {
            $dataSource = $this->categoryFetcherService->fetch();
        } catch (\Exception $e) {
            return [];
        }

        return array_chunk($dataSource, self::BATCH_SIZE);
    }

    /**
     * @param null|OutputInterface $output
     */
    public function setOutput(?OutputInterface $output): void
    {
        $this->output = $output;
    }

    /**
     * @return \Symfony\Component\Console\Output\OutputInterface|null
     */
    private function getOutput(): ?OutputInterface
    {
        return $this->output;
    }

    /**
     * @param int $dimension
     * @return \Symfony\Component\Console\Helper\ProgressBar|null
     */
    private function getProgressBar(int $dimension = 0): ?ProgressBar
    {
        $progressBar = null;
        if ($this->hasOutput()) {
            $progressBar = $this->progressBarFactory->create([
                'output' => $this->getOutput(),
                'max' => $dimension,
            ]);
            $progressBar->setBarCharacter('<fg=green>⚬</>');
            $progressBar->setEmptyBarCharacter("<fg=red>⚬</>");
            $progressBar->setProgressCharacter("<fg=green>➤</>");
        }

        return $progressBar;
    }

    /**
     * @return bool
     */
    private function hasOutput(): bool
    {
        return !is_null($this->output);
    }
}
