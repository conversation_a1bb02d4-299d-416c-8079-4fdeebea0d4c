<?php
declare(strict_types=1);

namespace Comave\BigBuy\Service\Order;

use Comave\BigBuy\Model\ConfigProvider;
use Comave\SellerApi\Api\ConfigurableApiInterface;
use Laminas\Http\Request;

class ApiConfiguration implements ConfigurableApiInterface
{
    private ?string $endpoint = null;
    private array|string|null $params = null;

    /**
     * @param ConfigProvider $configProvider
     */
    public function __construct(
        private readonly ConfigProvider $configProvider,
    ) {
    }

    /**
     * @inheritDoc
     */
    public function getEndpoint(): string
    {
        return $this->endpoint ?: $this->configProvider->getOrderSyncEndpoint();
    }

    /**
     * @inheritDoc
     * @throws \Exception
     */
    public function getHeaders(): array
    {
        return [
            'Accept' => '*/*',
            'Content-Type' => 'application/json',
            'Authorization' => sprintf('Bearer %s', $this->configProvider->getApiKey()),
        ];
    }

    /**
     * @inheritDoc
     */
    public function getMethod(): string
    {
        return Request::METHOD_POST;
    }

    /**
     * @inheritDoc
     */
    public function getParams(): array|string|null
    {
        return $this->params;
    }

    /**
     * @inheritDoc
     */
    public function setParams(array|string|null $params): ConfigurableApiInterface
    {
        $this->params = $params;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function setEndpoint(string $endpoint): ConfigurableApiInterface
    {
        $this->endpoint = $endpoint;

        return $this;
    }
}
