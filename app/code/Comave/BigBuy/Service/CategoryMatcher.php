<?php

declare(strict_types=1);

namespace Comave\BigBuy\Service;

use Comave\CategoryMatcher\Api\CategoryMatcherServiceInterface;
use Comave\CategoryMatcher\Model\ConfigProvider;
use Comave\SellerApi\Api\ConfigurableApiInterfaceFactory;
use Comave\SellerApi\Service\RequestHandler;
use Magento\Framework\Serialize\SerializerInterface;
use Psr\Http\Client\ClientExceptionInterface;
use Psr\Log\LoggerInterface;

class CategoryMatcher implements CategoryMatcherServiceInterface
{
    /**
     * @param SerializerInterface $serializer
     * @param LoggerInterface $logger
     * @param RequestHandler $requestHandler
     * @param ConfigProvider $configProvider
     * @param ConfigurableApiInterfaceFactory $configurableApiFactory
     */
    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly LoggerInterface $logger,
        private readonly RequestHandler $requestHandler,
        private readonly ConfigProvider $configProvider,
        private readonly ConfigurableApiInterfaceFactory $configurableApiFactory,
    ) {
    }

    /**
     * @param array{array{sku: string, word: string}} $categories
     * @param string $lang
     * @return array{sku: string, int[]}
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Psr\Http\Client\ClientExceptionInterface
     */
    public function matchCategories(array $categories, string $lang = 'en'): array
    {
        $result = [];
        if (!$this->configProvider->isEnabled()) {
            $this->logger->info(
                'Not enabled, returning'
            );

            return [];
        }

        if (empty($this->configProvider->getEndpoint())) {
            $this->logger->info(
                'No endpoint specified, returning'
            );

            return [];
        }

        foreach ($categories as $category) {
            $endpoint = sprintf("%s/one/%s", $this->configProvider->getEndpoint(), $category['sku']);
            $configurableApi = $this->configurableApiFactory->create([
                'method' => 'GET',
                'endpoint' => $endpoint,
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]);
            try {
                $this->logger->info(
                    'Performing category match request',
                    [
                        'endpoint' => $endpoint,
                    ]
                );

                $response = $this->requestHandler->handleRequest($configurableApi);
                if (!$response->hasError()) {
                    $decodedResponse = $this->serializer->unserialize(
                        $response->getResult()->getBody()->getContents()
                    );

                    if (!empty($decodedResponse)) {
                        if (!empty($decodedResponse['result'])) {
                            $magentoCategoryIds = current($decodedResponse['result']);
                            $result[$category['sku']] = $magentoCategoryIds['magentoIds'];

                            $this->logger->info(
                                '[Comave.CategoryMatcher] Response received',
                                [
                                    'categoryMatch' => $category,
                                    'matched' => $magentoCategoryIds,
                                    'highest' => $magentoCategoryIds,
                                ]
                            );
                        }
                    }
                }

            } catch (ClientExceptionInterface $exception) {
                $this->logger->critical(sprintf("Cannot get category matching for SKU %s", $category['sku']), [
                    'categoryMatch' => $category,
                ]);
            }
        }

        return $result;
    }

    /**
     * @param array $categories
     * @param string $lang
     * @return void
     */
    public function processCategories(array $categories, string $lang = 'en'): void
    {
        $endpoint = sprintf("%s?language=%s", $this->configProvider->getEndpoint(), $lang);
        $sendParams = $this->serializer->serialize(['items' => $categories]);
        $configurableApi = $this->configurableApiFactory->create([
            'method' => 'POST',
            'endpoint' => $endpoint,
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'params' => $sendParams,
        ]);
        $this->logger->info(
            'Performing category batch matching request',
            [
                'endpoint' => $endpoint,
                'params' => $sendParams,
            ]
        );
        try {
            $this->requestHandler->handleRequest($configurableApi);
        } catch (ClientExceptionInterface $e) {
            $this->logger->warning(
                'Cannot process categories matching',
                [
                    'categoryMatch' => $categories,
                ]
            );
        }
    }
}