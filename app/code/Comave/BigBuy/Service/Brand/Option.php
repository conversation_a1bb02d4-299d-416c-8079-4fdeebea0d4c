<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Brand;

use Magento\Catalog\Model\Product;
use Magento\Eav\Api\AttributeOptionManagementInterface;
use Magento\Eav\Api\Data\AttributeOptionInterfaceFactory;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\StateException;

class Option
{
    private const string BRAND_ATTRIBUTE_CODE = 'brand';

    /**
     * @param \Magento\Eav\Api\Data\AttributeOptionInterfaceFactory $optionFactory
     * @param \Magento\Eav\Api\AttributeOptionManagementInterface $attributeOptionManagement
     */
    public function __construct(
        private readonly AttributeOptionInterfaceFactory $optionFactory,
        private readonly AttributeOptionManagementInterface $attributeOptionManagement
    ) {
    }

    /**
     * @param string $value
     * @return int
     */
    public function create(string $value): int
    {
        $option = $this->optionFactory->create();
        $option->setLabel($value);
        $option->setSortOrder(0);
        $option->setIsDefault(false);

        try {
            $this->attributeOptionManagement->add(
                Product::ENTITY,
                self::BRAND_ATTRIBUTE_CODE,
                $option
            );
        } catch (StateException|InputException $e) {
            return 0;
        }

        return (int)$option->getValue();
    }
}
