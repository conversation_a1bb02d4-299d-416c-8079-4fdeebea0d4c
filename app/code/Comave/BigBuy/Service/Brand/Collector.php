<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Brand;

use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Helper\ProgressBarFactory;
use Symfony\Component\Console\Output\OutputInterface;

class Collector
{
    /**
     * @var \Symfony\Component\Console\Output\OutputInterface|null
     */
    private ?OutputInterface $output = null;

    public function __construct(
        private readonly ProgressBarFactory $progressBarFactory,
        private readonly Fetcher $brandFetcherService,
        private readonly Option $brandOptionService
    ) {
    }

    /**
     * @return void
     */
    public function execute(): void
    {
        $brands = $this->brandFetcherService->fetch();
        if ($this->hasOutput()) {
            $this->output->writeln(
                sprintf(
                    "<info>%s</info>",
                    __('Collecting BigBuy brands ...')
                )
            );
            $this->getOutput()->writeln("");
            $progressBar = $this->getProgressBar(count($brands));
            $progressBar->start();
            $progressBar->display();
        }

        foreach ($brands as $brand) {
            $this->brandOptionService->create($brand['name']);
            if ($this->hasOutput() && !empty($progressBar)) {
                $progressBar->advance();
            }
        }

        if ($this->hasOutput() && !empty($progressBar)) {
            $progressBar->finish();
            $this->getOutput()->writeln("\n");
            $this->getOutput()->writeln(
                sprintf(
                    '<info>%s</info>',
                    __('BigBuy brands have been collected successfully')
                )
            );
        }

    }

    /**
     * @param null|OutputInterface $output
     */
    public function setOutput(?OutputInterface $output): void
    {
        $this->output = $output;
    }

    /**
     * @return \Symfony\Component\Console\Output\OutputInterface|null
     */
    private function getOutput(): ?OutputInterface
    {
        return $this->output;
    }

    /**
     * @param int $dimension
     * @return \Symfony\Component\Console\Helper\ProgressBar|null
     */
    private function getProgressBar(int $dimension = 0): ?ProgressBar
    {
        $progressBar = null;
        if ($this->hasOutput()) {
            $progressBar = $this->progressBarFactory->create([
                'output' => $this->getOutput(),
                'max' => $dimension,
            ]);
            $progressBar->setBarCharacter('<fg=green>⚬</>');
            $progressBar->setEmptyBarCharacter("<fg=red>⚬</>");
            $progressBar->setProgressCharacter("<fg=green>➤</>");
        }

        return $progressBar;
    }

    /**
     * @return bool
     */
    private function hasOutput(): bool
    {
        return !is_null($this->output);
    }
}
