<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Cron;

use Comave\BigBuy\Service\Category\Collector;

class CategoryCollector
{
    /**
     * @param \Comave\BigBuy\Service\Category\Collector $categoryCollector
     */
    public function __construct(
        private readonly Collector $categoryCollector,
    ) {
    }

    /**
     * @return void
     */
    public function execute(): void
    {
        $this->categoryCollector->execute();
    }
}
