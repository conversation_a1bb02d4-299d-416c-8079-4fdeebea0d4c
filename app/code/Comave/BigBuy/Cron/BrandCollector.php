<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Cron;

use Comave\BigBuy\Service\Brand\Collector;

class BrandCollector
{
    /**
     * @param \Comave\BigBuy\Service\Brand\Collector $brandCollector
     */
    public function __construct(
        private readonly Collector $brandCollector,
    ) {
    }

    /**
     * @return void
     */
    public function execute(): void
    {
        $this->brandCollector->execute();
    }
}
