<?php
declare(strict_types=1);

namespace Comave\CustomerAddressType\Model\Config;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;

class AddressTypeOptions extends AbstractSource
{
    const string OPTION_HOME = 'home';
    const string OPTION_WORK = 'work';
    const string OPTION_RETURN = 'return';
    const string OPTION_OTHER = 'other';

    public function getAllOptions(): array
    {
        return [
            ['label' => __('Home'), 'value' => self::OPTION_HOME],
            ['label' => __('Work'), 'value' => self::OPTION_WORK],
            ['label' => __('Return'), 'value' => self::OPTION_RETURN],
            ['label' => __('Other'), 'value' => self::OPTION_OTHER],
        ];
    }
}
