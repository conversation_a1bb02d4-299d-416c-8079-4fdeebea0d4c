<?php

declare(strict_types=1);

namespace Comave\CustomerAddressType\Setup\Patch\Data;

use Magento\Customer\Model\Customer;
use Magento\Customer\Model\ResourceModel\Attribute;
use Magento\Eav\Model\Config;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class AddCustomerAddressType implements DataPatchInterface
{
    public function __construct(
        private readonly Config $eavConfig,
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly Attribute $attributeResource,
    ) {
    }

    /**
     * @return $this
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function apply(): self
    {
        $this->moduleDataSetup->startSetup();
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        try {
            $customAttribute = $this->eavConfig->getAttribute(
                'customer_address',
                'save_as_address_type'
            );
        } catch (\Exception $e) {
            $customAttribute = null;
        }

        if (!empty($customAttribute) && !empty($customAttribute->getAttributeId())) {
            $customAttribute->setData(
                'source_model',
                'Comave\CustomerAddressType\Model\Config\AddressTypeOptions',
            );
        } else {
            $this->eavConfig->clear();
            $eavSetup->addAttribute('customer_address', 'save_as_address_type', [
                'type' => 'varchar',
                'input' => 'select',
                'source' => 'Comave\CustomerAddressType\Model\Config\AddressTypeOptions',
                'label' => 'Save Address Type As',
                'visible' => true,
                'required' => false,
                'user_defined' => true,
                'system' => false,
                'group' => 'General',
                'visible_on_front' => true,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => true,
                'is_filterable_in_grid' => true,
                'is_searchable_in_grid' => true,
            ]);

            $customAttribute = $this->eavConfig->getAttribute(
                'customer_address',
                'save_as_address_type'
            );

            $customAttribute->setData(
                'used_in_forms',
                ['adminhtml_customer_address', 'customer_address_edit', 'customer_register_address', 'adminhtml_checkout', 'checkout_register'],
            );
            $customAttribute->setData('scope_is_visible', 1);
        }

        try {
            $this->attributeResource->save($customAttribute);
        } catch (\Exception $e) {
            throw new \Magento\Framework\Exception\LocalizedException(
                __('Error saving attribute: %1', $e->getMessage())
            );
        }

        return $this;
    }

    /**
     * @return array|string[]
     */
    public static function getDependencies(): array
    {
        return [];
    }

    public function getAliases(): array
    {
        return [];
    }
}
