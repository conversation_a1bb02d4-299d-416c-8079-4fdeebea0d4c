<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="seller_general">
            <group id="general">
                <group id="crystalpalace" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Crystal Palace</label>
                    <field id="enable" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>Enable/Disable</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                        <comment>Enable or disable the feature</comment>
                        <config_path>seller_settings/crystalpalace_general/enable</config_path>
                    </field>
                    <field id="crystalpalace_seller_email" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>Crystal Palace Seller Email</label>
                        <depends>
                            <field id="enable">1</field>
                        </depends>
                        <comment>Enter the crystal palace seller registered Email.</comment>
                        <config_path>seller_settings/crystalpalace_general/seller_email</config_path>
                    </field>
                    <field id="default_attribute_set" type="select" sortOrder="20" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>Attribute Set</label>
                        <source_model>Comave\SellerApi\Model\Option\Source\AttributeSets</source_model>
                        <comment>Default Attribute Set to be assigned to a new product</comment>
                        <config_path>seller_settings/crystalpalace_general/attribute_set_id</config_path>
                    </field>
                    <field id="sports_club" type="select" sortOrder="25" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>Sports Club Assignment</label>
                        <source_model>Comave\SellerApi\Model\Option\Source\SportsClub</source_model>
                        <config_path>seller_settings/crystalpalace_general/sports_club</config_path>
                    </field>
                    <field id="attribute_mapping" sortOrder="30" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>Attribute Mapping</label>
                        <frontend_model>Comave\SellerApi\Block\Adminhtml\Form\Field\AttributeMapping</frontend_model>
                        <backend_model>Magento\Config\Model\Config\Backend\Serialized\ArraySerialized</backend_model>
                        <comment>Mapping will be done based on the path of the data in the import, i.e (magento attribute code) name => (import path in array) product/name</comment>
                        <config_path>seller_settings/crystalpalace_general/attribute_mapping</config_path>
                    </field>
                </group>
            </group>
            <group id="api">
                <group id="crystalpalace" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Crystal Palace</label>
                    <depends>
                        <field id="seller_settings/crystalpalace_general/enable">1</field>
                    </depends>
                    <field id="order_sync" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>Enable Order Sync</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                        <comment>Enable or disable the feature</comment>
                        <config_path>seller_settings/crystalpalace_api/order_sync</config_path>
                    </field>
                    <field id="order_sync_endpoint" type="text" sortOrder="20" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>Order Sync Api Endpoint</label>
                        <comment/>
                        <depends>
                            <field id="order_sync">1</field>
                        </depends>
                        <config_path>seller_settings/crystalpalace_api/order_endpoint</config_path>
                    </field>
                    <field id="product_sync" type="select" sortOrder="30" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>Enable Product Sync</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                        <comment>Enable or disable the feature</comment>
                        <config_path>seller_settings/crystalpalace_api/product_sync</config_path>
                    </field>
                    <field id="product_sync_endpoint" type="text" sortOrder="40" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>Product Sync Api Endpoint</label>
                        <comment/>
                        <config_path>seller_settings/crystalpalace_api/product_endpoint</config_path>
                        <depends>
                            <field id="product_sync">1</field>
                        </depends>
                    </field>
                    <field id="token" type="obscure" sortOrder="50" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>Api Token</label>
                        <comment/>
                        <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                        <config_path>seller_settings/crystalpalace_api/token</config_path>
                        <depends>
                            <field id="product_sync">1</field>
                        </depends>
                    </field>
                    <field id="pull_latest_inventory" type="select" sortOrder="60" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                        <label>Pull Latest Inventory</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                        <comment/>
                        <config_path>seller_settings/crystalpalace_api/pull_latest</config_path>
                        <depends>
                            <field id="product_sync">1</field>
                        </depends>
                    </field>
                    <field id="date" translate="label comment" sortOrder="70" type="date" showInDefault="1" showInStore="1" >
                        <label>Date to pull from</label>
                        <comment>Please select appropriate date to sync the product up to date</comment>
                        <frontend_model>Comave\CrystalPalace\Block\DatePicker</frontend_model>
                        <config_path>seller_settings/crystalpalace_api/sync_date</config_path>
                        <depends>
                            <field id="product_sync">1</field>
                            <field id="pull_latest_inventory">0</field>
                        </depends>
                    </field>
                </group>
            </group>
            <group id="imports">
                <group id="crystalpalace" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Crystal Palace</label>
                    <field id="import_products" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                        <button_label>Import Products</button_label>
                        <label>Trigger Import</label>
                        <button_url>seller/products/sync/identifier/custom_crystalpalace</button_url>
                        <frontend_model>Comave\SellerApi\Block\Adminhtml\Button\ImportProducts</frontend_model>
                    </field>
                </group>
            </group>
        </section>
    </system>
</config>
