<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Beging flat sync -->
    <virtualType name="CrystalPalaceCustomIntegration" type="Comave\SellerApi\Model\BaseIntegration">
        <arguments>
            <argument xsi:type="string" name="integrationType">custom_crystalpalace</argument>
            <argument xsi:type="string" name="mainProductLinkTable">comave_marketplace_products_custom_api</argument>
            <argument xsi:type="string" name="productColumnIdentifier">product_id</argument>
            <argument xsi:type="string" name="sellerColumnIdentifier">seller_id</argument>
        </arguments>
    </virtualType>

    <type name="Comave\SellerApi\Model\IntegrationTypePool">
        <arguments>
            <argument xsi:type="array" name="integrationPool">
                <item xsi:type="object" name="custom_crystalpalace">CrystalPalaceCustomIntegration</item>
            </argument>
        </arguments>
    </type>
    <!-- End flat sync -->

    <type name="Comave\CrystalPalace\Model\ProcessorPool">
        <arguments>
            <argument xsi:type="object" name="logger">SellerImportLogging</argument>
            <argument xsi:type="array" name="processors">
                <item xsi:type="object" name="base">Comave\CrystalPalace\Model\Processors\BaseData\Proxy</item>
                <item xsi:type="object" name="categories">Comave\CrystalPalace\Model\Processors\Categories\Proxy</item>
                <item xsi:type="object" name="attributes">Comave\CrystalPalace\Model\Processors\Attributes\Proxy</item>
                <item xsi:type="object" name="media_gallery">Comave\CrystalPalace\Model\Processors\MediaGallery\Proxy</item>
            </argument>
        </arguments>
    </type>

    <!-- Import products configurations -->
    <!-- queue processing for products -->
    <type name="Comave\SellerApi\Model\QueueProcessorPoolManager">
        <arguments>
            <argument xsi:type="array" name="configurationPools">
                <item xsi:type="string" name="custom_crystalpalace">Comave\CrystalPalace\Model\ProcessorPool</item>
            </argument>
        </arguments>
    </type>
    <!-- end -->
    <type name="Comave\SellerApi\Model\ImportableProductsPoolManager">
        <arguments>
            <argument xsi:type="array" name="configurationPools">
                <item xsi:type="string" name="custom_crystalpalace">Comave\CrystalPalace\Service\ImportService</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="ImportableConfigurationPool">
        <arguments>
            <argument xsi:type="array" name="configurationPools">
                <item xsi:type="string" name="custom_crystalpalace">Comave\CrystalPalace\Service\ApiConfiguration</item>
            </argument>
        </arguments>
    </virtualType>
    <!-- end -->
    <!-- Order synchronise configurations -->
    <type name="Comave\SellerApi\Model\OrderSynchroniserPoolManager">
        <arguments>
            <argument xsi:type="array" name="configurationPools">
                <item xsi:type="string" name="custom_crystalpalace">Comave\CrystalPalace\Service\Order\SynchroniseService</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="OrderSyncConfigurationPool">
        <arguments>
            <argument xsi:type="array" name="configurationPools">
                <item xsi:type="string" name="custom_crystalpalace">Comave\CrystalPalace\Service\Order\ApiConfiguration</item>
            </argument>
        </arguments>
    </virtualType>
    <!-- end -->

    <type name="Comave\CrystalPalace\Service\ImportService">
        <arguments>
            <argument xsi:type="object" name="logger">SellerImportLogging</argument>
        </arguments>
    </type>

    <type name="Comave\CrystalPalace\Cron\SyncProducts">
        <arguments>
            <argument xsi:type="object" name="logger">SellerImportLogging</argument>
            <argument xsi:type="object" name="importableProducts">Comave\CrystalPalace\Service\ImportService</argument>
            <argument xsi:type="object" name="configurableApi">Comave\CrystalPalace\Service\ApiConfiguration</argument>
        </arguments>
    </type>

    <type name="Comave\CrystalPalace\Model\Processors\Attributes">
        <arguments>
            <argument xsi:type="object" name="logger">SellerImportLogging</argument>
        </arguments>
    </type>

    <type name="Comave\CrystalPalace\Service\Order\SynchroniseService">
        <arguments>
            <argument xsi:type="object" name="logger">OrderSyncLogger</argument>
        </arguments>
    </type>
</config>
