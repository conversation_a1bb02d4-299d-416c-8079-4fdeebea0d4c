<?php

declare(strict_types=1);

namespace Comave\CrystalPalace\Cron;

use Comave\CrystalPalace\Model\ConfigProvider;
use Comave\SellerApi\Api\ConfigurableApiInterface;
use Comave\SellerApi\Api\ImportableProductsInterface;
use Psr\Log\LoggerInterface;

class SyncProducts
{
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly ConfigProvider $configProvider,
        private readonly ConfigurableApiInterface $configurableApi,
        private readonly ImportableProductsInterface $importableProducts
    ) {
    }

    public function execute(): void
    {
        if (!$this->configProvider->isEnabled()) {
            $this->logger->info('[CrystalPalaceSeller] The extension is disabled.');
            return;
        }

        try {
            $this->importableProducts->importProducts($this->configurableApi);
            $this->logger->info('[CrystalPalaceSeller] Products were synchronized successfully.');
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }
}
