<?php

declare(strict_types=1);

namespace Comave\CrystalPalace\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Serialize\SerializerInterface;

class ConfigProvider
{
    private const string XML_PATH_SELLER_CP_ENABLED =
        'seller_settings/crystalpalace_general/enable';
    private const string XML_PATH_SELLER_CP_ORDER_SYNC_ENABLED =
        'seller_settings/crystalpalace_api/order_sync';
    private const string XML_PATH_SELLER_CP_ORDER_SYNC_ENDPOINT =
        'seller_settings/crystalpalace_api/order_endpoint';
    private const string XML_PATH_SELLER_CP_EMAIL_ADDRESS=
        'seller_settings/crystalpalace_general/seller_email';
    private const string XML_PATH_SELLER_CP_PRODUCT_SYNC_ENABLED =
        'seller_settings/crystalpalace_api/product_sync';
    private const string XML_PATH_SELLER_CP_PRODUCT_SYNC_MAPPED_ATTRIBUTES =
        'seller_settings/crystalpalace_general/attribute_mapping';
    private const string XML_PATH_SELLER_CP_PRODUCT_SYNC_SPORTS_CLUB =
        'seller_settings/crystalpalace_general/sports_club';

    private const string XML_PATH_SELLER_CP_PRODUCT_SYNC_DEFAULT_ATTRIBUTE_SET =
        'seller_settings/crystalpalace_general/attribute_set_id';
    private const string XML_PATH_SELLER_CP_PRODUCT_SYNC_ENDPOINT =
        'seller_settings/crystalpalace_api/product_endpoint';
    private const string XML_PATH_SELLER_CP_TOKEN =
        'seller_settings/crystalpalace_api/token';
    private const string XML_PATH_SELLER_CP_PRODUCT_SYNC_PULL_LATEST =
        'seller_settings/crystalpalace_api/pull_latest';
    private const string XML_PATH_SELLER_CP_PRODUCT_SYNC_PULL_DATE =
        'seller_settings/crystalpalace_api/sync_date';

    /**
     * @param ScopeConfigInterface $scopeConfig
     * @param SerializerInterface $serializer
     */
    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly SerializerInterface $serializer
    ) {
    }

    /**
     * Check if the extension is enabled
     */
    public function isEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_SELLER_CP_ENABLED,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Check order sync is enabled or not
     *
     * @return bool
     */
    public function isOrderSyncEnable(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_SELLER_CP_ORDER_SYNC_ENABLED,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * Get the crystal palace seller details using the email id.
     *
     * @return null|string
     */
    public function getSellerDetails(): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_SELLER_CP_EMAIL_ADDRESS,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORES
        );
    }

    public function isProductSyncEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_SELLER_CP_PRODUCT_SYNC_ENABLED,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * get the crystal palace product sync api endpoints
     *
     * @return string|null
     */
    public function getProductEndpoint(): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_SELLER_CP_PRODUCT_SYNC_ENDPOINT,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * get the crystal palace order sync api endpoints
     *
     * @return string|null
     */
    public function getOrderEndpoint(): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_SELLER_CP_ORDER_SYNC_ENDPOINT,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * get the crystal palace product sync api token
     *
     * @return string|null
     */
    public function getApiToken(): ?string
    {
        return  $this->scopeConfig->getValue(
            self::XML_PATH_SELLER_CP_TOKEN,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * Check for pull the latest inventory or not
     *
     * @return bool
     */
    public function getPullLatestInv(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_SELLER_CP_PRODUCT_SYNC_PULL_LATEST,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * get the date of sync products
     *
     * @return string|null
     */
    public function getPullDate(): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_SELLER_CP_PRODUCT_SYNC_PULL_DATE,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORES
        );
    }

    /**
     * @return array|null
     */
    public function getMappedAttributes(): ?array
    {
        $value = $this->scopeConfig->getValue(
            self::XML_PATH_SELLER_CP_PRODUCT_SYNC_MAPPED_ATTRIBUTES,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORES
        );

        if (is_string($value)) {
            $value = $this->serializer->unserialize($value);
        }

        return is_array($value) ? $value : null;
    }

    /**
     * @return int
     */
    public function getDefaultAttributeSet(): int
    {
        $value = $this->scopeConfig->getValue(
            self::XML_PATH_SELLER_CP_PRODUCT_SYNC_DEFAULT_ATTRIBUTE_SET,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORES
        );

        return !empty($value) ? (int) $value : 4;
    }

    /**
     * @return string|null
     */
    public function getSportsClub(): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_SELLER_CP_PRODUCT_SYNC_SPORTS_CLUB,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORES
        );
    }
}
