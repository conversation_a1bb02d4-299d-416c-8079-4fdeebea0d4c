<?php

declare(strict_types=1);

namespace Comave\CrystalPalace\Model\Processors;

use Comave\SellerApi\Api\ImportableProductsInterface;
use Comave\SellerApi\Api\ProductProcessorInterface;
use Comave\SellerApi\Service\SellerIdentity;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\DB\Select;
use Webkul\MpSellerCategory\Api\CategoryRepositoryInterface;
use Webkul\MpSellerCategory\Api\Data\CategoryInterface;
use Webkul\MpSellerCategory\Api\Data\CategoryInterfaceFactory;
use Webkul\MpSellerCategory\Helper\Data as MPHelperData;
use Webkul\MpSellerCategory\Model\ResourceModel\Category;
use Webkul\MpSellerCategory\Model\ResourceModel\Category\CollectionFactory;

class Categories implements ProductProcessorInterface
{
    private array $loadedCategories = [];

    /**
     * @param SellerIdentity $sellerIdentity
     * @param MPHelperData $categoryHelper
     * @param CollectionFactory $mpCategoryCollectionFactory
     * @param CategoryInterfaceFactory $categoryFactory
     * @param CategoryRepositoryInterface $categoryRepository
     */
    public function __construct(
        private readonly MPHelperData $categoryHelper,
        private readonly SellerIdentity $sellerIdentity,
        private readonly CollectionFactory $mpCategoryCollectionFactory,
        private readonly CategoryInterfaceFactory $categoryFactory,
        private readonly CategoryRepositoryInterface $categoryRepository,
    ) {
    }

    /**
     * @inheritDoc
     */
    public function process(
        ProductInterface $product,
        string $sellerIdentifier,
        array $productArr,
        ?ProductInterface $parentProduct = null,
    ): void {
        if (!$this->categoryHelper->isAllowedSellerCategories()) {
            return;
        }

        if (empty($productArr['listings'])) {
            return;
        }

        $product->setData(
            ImportableProductsInterface::CATEGORY_ASSIGNMENT_KEY,
            $this->identifySellerCategories(
                $sellerIdentifier,
                $productArr['listings']
            )
        );
    }

    /**
     * @param string $sellerIdentifier
     * @param array $listings
     * @return array
     */
    private function identifySellerCategories(
        string $sellerIdentifier,
        array $listings
    ): array {
        $sellerId = $this->sellerIdentity->identify($sellerIdentifier);
        $categoryIds = [];

        foreach ($listings as $listing) {
            try {
                if (!isset($listing['sub_group']) || !is_array($listing['sub_group']) || !array_key_exists('name', $listing['sub_group'])) {
                    continue;
                }

                $categoryName = $listing['sub_group']['name'];
                $categoryData = [
                    "category_name" => $categoryName,
                    "status" => '1',
                    "position" => '1',
                    "seller_id" => $sellerId,
                ];

                $existingCategory = $this->getExistingCategory($categoryName, $sellerId);
                if ($existingCategory) {
                    $categoryIds[] = $existingCategory;
                } else {
                    /** @var CategoryInterface $sellerCategory */
                    $sellerCategory = $this->categoryFactory->create();
                    $sellerCategory->setData($categoryData);
                    $this->categoryRepository->save($sellerCategory);
                    $categoryIds[] = $sellerCategory->getId();
                }
            } catch (\Exception) {
                continue; // Skip this entry and continue with the next
            }
        }

        return $categoryIds;
    }

    /**
     * Check if a category with the given name exists and return it.
     *
     * @param string $categoryName
     * @param string $sellerId
     * @return string|null
     */
    public function getExistingCategory(string $categoryName, string $sellerId): ?string
    {
        if (!empty($this->loadedCategories)) {
            return $this->loadedCategories[$categoryName]['entity_id'] ?? null;
        }

        /** @var Category\Collection $collection */
        $collection = $this->mpCategoryCollectionFactory->create();
        $collection->getSelect()
            ->reset(Select::COLUMNS)
            ->columns([
                'category_name',
                'entity_id'
            ]);

        $collection->addFieldToFilter("seller_id", ["eq" => $sellerId]);
        $this->loadedCategories = $collection->getConnection()->fetchAssoc(
            $collection->getSelect()
        );

        return $this->loadedCategories[$categoryName]['entity_id'] ?? null;
    }
}
