<?php

declare(strict_types=1);

namespace Comave\CrystalPalace\Model\Processors;

use Comave\SellerApi\Api\ProductProcessorInterface;
use Comave\SellerApi\Model\MediaGalleryRegistry;
use Magento\Catalog\Api\Data\ProductInterface;

class MediaGallery implements ProductProcessorInterface
{
    /**
     * @param MediaGalleryRegistry $mediaGalleryRegistry
     */
    public function __construct(
        private readonly MediaGalleryRegistry $mediaGalleryRegistry,
    ) {
    }

    /**
     * @inheritDoc
     */
    public function process(
        ProductInterface $product,
        string $sellerIdentifier,
        array $productArr,
        ?ProductInterface $parentProduct = null,
    ): void {
        if (empty($productArr['additional_images_url'])) {
            return;
        }

        if (empty($productArr['additional_images_url']['supporting_images'] ?? [])) {
            return;
        }

        $mediaGallery = [];
        // Check and add rollover image if it exists
        if (isset($productArr['additional_images_url']['rollover_image'])) {
            $rolloverImageUrl = $productArr['additional_images_url']['rollover_image'];
            try {
                $rolloverImageContents = file_get_contents($rolloverImageUrl);
                if ($rolloverImageContents !== false) {
                    $mediaGallery[] =  $rolloverImageUrl;
                }
            } catch (\Exception) {}  //nothing to do image cannot be processed
        }

        // Iterate through supporting images and skip "coming soon" images
        foreach ($productArr['additional_images_url']['supporting_images'] as $imageUrl) {
            if (stripos($imageUrl, 'comingsoon.jpg') !== false) {
                continue; // Skip the "coming soon" image
            }

            try {
                $imageFileContents = file_get_contents($imageUrl);
                if ($imageFileContents !== false) {
                    $mediaGallery[] = $imageUrl;
                }
            } catch (\Exception) {} //nothing to do image cannot be processed
        }

        if (empty($mediaGallery)) {
            return;
        }

        $this->mediaGalleryRegistry->add([
            'sku' => $product->getSku(),
            'media_gallery_entries' => $mediaGallery
        ]);
    }
}
