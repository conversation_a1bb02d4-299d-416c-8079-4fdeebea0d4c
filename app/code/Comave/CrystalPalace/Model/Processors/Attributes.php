<?php

declare(strict_types=1);

namespace Comave\CrystalPalace\Model\Processors;

use Comave\CrystalPalace\Model\ConfigProvider;
use Comave\SellerApi\Api\ProductProcessorInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Eav\Api\AttributeOptionManagementInterface;
use Magento\Eav\Api\Data\AttributeOptionInterfaceFactory;
use Psr\Log\LoggerInterface;

class Attributes implements ProductProcessorInterface
{
    /**
     * @param AttributeOptionManagementInterface $attributeOptionManagement
     * @param AttributeOptionInterfaceFactory $attributeOptionFactory
     * @param LoggerInterface $logger
     * @param ProductAttributeRepositoryInterface $productAttributeRepository
     * @param ConfigProvider $configProvider
     */
    public function __construct(
        private readonly AttributeOptionManagementInterface $attributeOptionManagement,
        private readonly AttributeOptionInterfaceFactory $attributeOptionFactory,
        private readonly LoggerInterface $logger,
        private readonly ProductAttributeRepositoryInterface $productAttributeRepository,
        private readonly ConfigProvider $configProvider
    ) {
    }

    /**
     * @inheritDoc
     */
    public function process(
        ProductInterface $product,
        string $sellerIdentifier,
        array $productArr,
        ?ProductInterface $parentProduct = null,
    ): void {
        $mappedAttributes = $this->configProvider->getMappedAttributes();

        if (empty($mappedAttributes)) {
            return;
        }

        $attributeData = $productArr;

        if (isset($productArr['style']) && $product->getTypeId() === 'simple') {
            $attributeData = current($productArr['style']['variations']);
        }

        foreach ($mappedAttributes as $attributeArr) {
            if (!isset($attributeData[$attributeArr['attribute_mapping']])) {
                continue;
            }

            $attributeValue = $attributeData[$attributeArr['attribute_mapping']] ?: false;
            if (empty($attributeValue)) {
                continue;
            }

            try {
                $attributeCode = $attributeArr['attribute_code'];
                $magentoAttribute = $this->productAttributeRepository->get($attributeCode);
                if ($magentoAttribute->usesSource()) {
                    $attributeValue = trim($attributeValue);
                    $optionId = $magentoAttribute->getSource()->getOptionId($attributeValue);
                    if (empty($optionId)) {
                        $optionId = $this->createOption(
                            $attributeCode,
                            $attributeValue
                        );
                    }

                    $attributeValue = $optionId;
                }

                $product->setCustomAttribute($attributeCode, $attributeValue);
            } catch (\Exception $e) {
                $this->logger->warning(
                    '[ProductAttributeMapper] Error trying to process attribute',
                    [
                        'message' => $e->getMessage(),
                        'attribute' => $attributeCode,
                        'product' => $product->getSku(),
                        'productValue' => $attributeValue
                    ]
                );

                continue;
            }
        }
    }

    /**
     * @param string $attributeCode
     * @param string|int $attributeValue
     * @return string
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\StateException
     */
    private function createOption(
        string $attributeCode,
        string|int $attributeValue
    ): string {
        $option = $this->attributeOptionFactory->create();
        $option->setSortOrder(0);
        $option->setIsDefault(false);
        $option->setLabel($attributeValue);

        // Add the option to the attribute
        return $this->attributeOptionManagement->add(
            \Magento\Catalog\Model\Product::ENTITY,
            $attributeCode,
            $option
        );
    }
}
