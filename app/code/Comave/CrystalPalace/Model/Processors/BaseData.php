<?php

declare(strict_types=1);

namespace Comave\CrystalPalace\Model\Processors;

use Comave\CrystalPalace\Model\ConfigProvider;
use Comave\SellerApi\Api\ProductProcessorInterface;
use Comave\SellerApi\Service\SellerIdentity;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Visibility;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\Escaper;

class BaseData implements ProductProcessorInterface
{
    /**
     * @param SellerIdentity $sellerIdentity
     * @param ConfigProvider $configProvider
     * @param Escaper $escaper
     */
    public function __construct(
        private readonly SellerIdentity $sellerIdentity,
        private readonly ConfigProvider $configProvider,
        private readonly Escaper $escaper
    ) {
    }

    /**
     * @inheritDoc
     */
    public function process(
        ProductInterface $product,
        string $sellerIdentifier,
        array $productArr,
        ?ProductInterface $parentProduct = null,
    ): void {
        if (empty($product->getAttributeSetId())) {
            $product->setAttributeSetId(
                $this->configProvider->getDefaultAttributeSet()
            );
        }

        /** @var Product $product */
        if (!$product->getId() || !$product->getTypeId()) {
            $product->setTypeId(
                count($productArr['style']['variations'] ?? []) > 1 ?
                    Configurable::TYPE_CODE :
                    'simple'
            );

            $product->setSku($productArr['product_code']);
        }

        $product->setCustomAttribute(
            'product_club',
            $this->configProvider->getSportsClub()
        );
        $product->setData(
            'assign_seller',
            [
                'seller_id' => $this->sellerIdentity->identify($sellerIdentifier)
            ]
        );

        if (!$product->getVisibility() || $product->getTypeId() === Configurable::TYPE_CODE) {
            $product->setVisibility(Visibility::VISIBILITY_BOTH);
        }

        $itemData = $productArr;

        if (isset($productArr['style'])) { //this is the configurable data
            $productName = !empty($productArr['style']['description_1'] ?? '') ?
                $productArr['style']['description_1'] :
                'Imported Product '. time();

            $product->setStatus($productArr['style']['active'])
                ->setName(
                    $this->escaper->escapeHtml($productName)
                )->setCustomAttribute(
                    'description',
                    $this->escaper->escapeHtml($productArr['style']['web_description'])
                )->setCustomAttribute(
                    'short_description',
                    $this->escaper->escapeHtml($productArr['style']['short_description'])
                );

            if (count($productArr['style']['variations'] ?? []) <= 1) {
                $itemData = current($productArr['style']['variations']);
            }
        }

        $product->setWebsiteIds([1]) //@todo add store scope ? Based on config enabled level ?
            ->setTaxClassId($itemData['vat_code']);

        if ($product->getTypeId() === 'simple') {
            $product->setPrice(
                (float) $itemData['new_sell']
            )->setStockData([
                'use_config_manage_stock' => 1,
                'is_in_stock' => 1,
                'qty' => $itemData['stock_available'] > 0 ?
                    $itemData['stock_available'] : 1
            ]); //@todo move this to different processor / check for MSI
        }

        //@todo find a better way to process children as the data in the variation doesn't contain names or anything else
        if ($parentProduct !== null) {
            $productName = sprintf(
                '%s - %s %s',
                $parentProduct->getName(),
                $productArr['colour_name'],
                $productArr['size_name'],
            );

            $product->setStatus($parentProduct->getStatus())
                ->setName(
                    $this->escaper->escapeHtml($productName)
                )->setCustomAttribute(
                    'description',
                    $this->escaper->escapeHtml(
                        $parentProduct->getCustomAttribute('description')?->getValue()
                    )
                )->setCustomAttribute(
                    'short_description',
                    $this->escaper->escapeHtml(
                        $parentProduct->getCustomAttribute('short_description')?->getValue()
                    )
                );
        }
    }
}
