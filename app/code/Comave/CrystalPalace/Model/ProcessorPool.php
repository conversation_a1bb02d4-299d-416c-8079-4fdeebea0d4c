<?php

declare(strict_types=1);

namespace Comave\CrystalPalace\Model;

use Comave\SellerApi\Api\ImportableProductsInterface;
use Comave\SellerApi\Api\ProductProcessorInterface;
use Comave\SellerApi\Api\QueueProductProcessorInterface;
use Comave\SellerApi\Model\AbstractProcessorPool;
use Comave\SellerApi\Model\MediaGalleryRegistry;
use Comave\SellerApi\Service\SellerCategoriesAssignment;
use Comave\SellerApi\Service\SellerProductSaveHandler;
use Magento\Catalog\Api\Data\EavAttributeInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\Data\ProductInterfaceFactory;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Visibility;
use Magento\ConfigurableProduct\Helper\Product\Options\Factory;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class ProcessorPool extends AbstractProcessorPool
{
    /**
     * @param LoggerInterface $logger
     * @param ProductRepositoryInterface $productRepository
     * @param SerializerInterface $serializer
     * @param SellerCategoriesAssignment $categoryAssignment
     * @param ProductInterfaceFactory $productFactory
     * @param Factory $extensionOptionsFactory
     * @param MediaGalleryRegistry $mediaGalleryRegistry
     * @param SellerProductSaveHandler $sellerProductSaveHandler
     * @param ProductAttributeRepositoryInterface $productAttributeRepository
     * @param StoreManagerInterface $storeManager
     * @param ConfigProvider $configProvider
     * @param ProductProcessorInterface[] $processors
     */
    public function __construct(
        LoggerInterface $logger,
        ProductRepositoryInterface $productRepository,
        SerializerInterface $serializer,
        SellerCategoriesAssignment $categoryAssignment,
        ProductInterfaceFactory $productFactory,
        private readonly Factory $extensionOptionsFactory,
        private readonly MediaGalleryRegistry $mediaGalleryRegistry,
        private readonly SellerProductSaveHandler $sellerProductSaveHandler,
        private readonly ProductAttributeRepositoryInterface $productAttributeRepository,
        private readonly StoreManagerInterface $storeManager,
        private readonly ConfigProvider $configProvider,
        array $processors = []
    ) {
        parent::__construct($logger, $productRepository, $serializer, $categoryAssignment, $productFactory, $processors);
    }

    /**
     * @param QueueProductProcessorInterface $queueProductProcessor
     * @return void
     */
    public function execute(QueueProductProcessorInterface $queueProductProcessor): void
    {
        $productsList = $this->serializer->unserialize(
            $queueProductProcessor->getProductList() ?: '{}'
        );

        if (empty($productsList)) {
            $this->logger->warning(
                '[ProductProcessor] No products found to process',
                [
                    'seller' => $queueProductProcessor->getSellerIdentifier()
                ]
            );

            return;
        }

        $sellerProducts = [];
        $this->storeManager->setCurrentStore(0);

        foreach ($productsList as $productArr) {
            $product = $this->identifyProduct($productArr['product_code']);

            try {
                foreach ($this->processors as $productProcessor) {
                    if (!$productProcessor instanceof ProductProcessorInterface) {
                        continue;
                    }

                    $this->logger->info(
                        '[ProductProcessor] Processing product',
                        [
                            'class' => get_class($productProcessor),
                        ]
                    );

                    $productProcessor->process(
                        $product,
                        $queueProductProcessor->getSellerIdentifier(),
                        $productArr
                    );
                }

                if (empty($product->getAttributeSetId())) {
                    $product->setAttributeSetId(
                        $this->configProvider->getDefaultAttributeSet()
                    );
                }

                $product = $this->productRepository->save($product);
                $sellerProducts[] = [
                    'product_id' => $product->getId(),
                    'row_id' => $product->getData('row_id')
                ];
                if (!empty($product->getData(ImportableProductsInterface::CATEGORY_ASSIGNMENT_KEY))) {
                    $this->categoryAssignment->addProductAssignments(
                        (string) $product->getId(),
                        $product->getData(ImportableProductsInterface::CATEGORY_ASSIGNMENT_KEY)
                    );
                }

                $this->logger->info(
                    '[ProductProcessor] Product processed',
                    [
                        'sku' => $product->getSku(),
                        'type_id' => $product->getTypeId()
                    ]
                );
                if (count($productArr['style']['variations']) <= 1) {
                    continue;
                }

                $this->logger->info(
                    '[ProductProcessor] Processing child products',
                    [
                        'parentProduct' => $product->getSku(),
                    ]
                );
                $sellerProducts = $sellerProducts + $this->processChildren(
                    $product,
                    $queueProductProcessor->getSellerIdentifier(),
                    $productArr['style']['variations']
                );
            } catch (\Exception $exception) {
                $this->logger->error(
                    '[ProductProcessor] Failed to process product',
                    [
                        'exception' => $exception->getMessage(),
                        'trace' => $exception->getTrace()
                    ]
                );

                continue;
            }
        }

        $this->mediaGalleryRegistry->process();
        $this->categoryAssignment->process();
        $this->sellerProductSaveHandler->execute(
            $queueProductProcessor->getSellerIdentifier(),
            $sellerProducts
        );
    }

    /**
     * @param ProductInterface $configurableProduct
     * @param string $sellerIdentifier
     * @param array $variations
     * @return array
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\StateException
     */
    private function processChildren(
        ProductInterface $configurableProduct,
        string $sellerIdentifier,
        array $variations
    ): array {
        $simpleProductIds = [];
        $sellerProducts = [];

        foreach ($variations as $productVariant) {
            $childProduct = $this->identifyProduct($productVariant['product_code']);
            $childProduct->setVisibility(Visibility::VISIBILITY_NOT_VISIBLE);

            foreach ($this->processors as $productProcessor) {
                if (!$productProcessor instanceof ProductProcessorInterface) {
                    continue;
                }

                $productProcessor->process(
                    $childProduct,
                    $sellerIdentifier,
                    $productVariant,
                    $configurableProduct
                );
            }

            $this->logger->info(
                '[ProductProcessor] Child product processed',
                [
                    'parentSku' => $configurableProduct->getSku(),
                    'childSku' => $childProduct->getSku()
                ]
            );
            $childProduct = $this->productRepository->save($childProduct);
            $simpleProductIds[] = $childProduct->getId();
            $sellerProducts[] = [
                'product_id' => $childProduct->getId(),
                'row_id' => $childProduct->getData('row_id')
            ];

            if (!empty($childProduct->getData(ImportableProductsInterface::CATEGORY_ASSIGNMENT_KEY))) {
                $this->categoryAssignment->addProductAssignments(
                    (string) $childProduct->getId(),
                    $childProduct->getData(ImportableProductsInterface::CATEGORY_ASSIGNMENT_KEY)
                );
            }
        }

        $configurableAttributesData = $configurableProduct->getTypeInstance()
            ->getConfigurableAttributesAsArray($configurableProduct);
        if (!$configurableProduct->getNewVariationsAttributeSetId()) {
            $configurableProduct->setNewVariationsAttributeSetId(
                $this->configProvider->getDefaultAttributeSet()
            );
        }

        if (empty($configurableAttributesData)) {
            $configurableAttributesData = $this->populateDefaultConfigurableAttributes();
            $configurableOptions = $this->extensionOptionsFactory->create($configurableAttributesData);
            $configurableProduct
                ->getExtensionAttributes()
                ->setConfigurableProductOptions($configurableOptions);
        }

        $configurableProduct->setAssociatedProductIds($simpleProductIds);
        $configurableProduct->setCanSaveConfigurableAttributes(true);
        $configurableProduct->setConfigurableAttributesData($configurableAttributesData);
        $this->productRepository->save($configurableProduct);

        return $sellerProducts;
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function populateDefaultConfigurableAttributes(): array
    {
        $res = [];
        $size = $this->productAttributeRepository->get('size');
        $color = $this->productAttributeRepository->get('color');

        /** @var EavAttributeInterface $eavAttribute */
        foreach ([$size, $color] as $eavAttribute) {
            $res[] = [
                'attribute_id' => $eavAttribute->getAttributeId(),
                'label' => $eavAttribute->getDefaultFrontendLabel(),
                'values' => [
                    [
                        'value_index' => uniqid(),
                    ]
                ]
            ];
        }

        return $res;
    }
}
