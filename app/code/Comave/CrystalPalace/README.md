# Mage2 Module Comave CrystalPalace

    ``comave/module-crystalpalace``

 - [Main Functionalities](#markdown-header-main-functionalities)
 - [Installation](#markdown-header-installation)
 - [Configuration](#markdown-header-configuration)
 - [Specifications](#markdown-header-specifications)
 - [Attributes](#markdown-header-attributes)


## Main Functionalities
sycn crystal palace products and  send the order details to them...

## Installation
\* = in production please use the `--keep-generated` option

### Type 1: Zip file

 - Unzip the zip file in `app/code/Comave`
 - Enable the module by running `php bin/magento module:enable Comave_CrystalPalace`
 - Apply database updates by running `php bin/magento setup:upgrade`\*
 - Flush the cache by running `php bin/magento cache:flush`

### Type 2: Composer

 - Make the module available in a composer repository for example:
    - private repository `repo.magento.com`
    - public repository `packagist.org`
    - public github repository as vcs
 - Add the composer repository to the configuration by running `composer config repositories.repo.magento.com composer https://repo.magento.com/`
 - Install the module composer by running `composer require comave/module-crystalpalace`
 - enable the module by running `php bin/magento module:enable Comave_CrystalPalace`
 - apply database updates by running `php bin/magento setup:upgrade`\*
 - Flush the cache by running `php bin/magento cache:flush`


## Configuration

 - product_sycn_api (catalog/general/product_sycn_api)


## Specifications

 - Controller
	- frontend > comave_crystalpalace/seller/sycnproducts

 - Cronjob
	- comave_crystalpalace_syncproducts



## Attributes



