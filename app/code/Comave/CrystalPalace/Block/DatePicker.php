<?php

declare(strict_types=1);

namespace Comave\CrystalPalace\Block;

use Magento\Framework\Data\Form\Element\AbstractElement;

class DatePicker extends \Magento\Config\Block\System\Config\Form\Field
{
   public function render(AbstractElement $element)
   {
       $element->setDateFormat(\Magento\Framework\Stdlib\DateTime::DATE_INTERNAL_FORMAT);
       $element->setTimeFormat('HH:mm:ss'); //set date and time as per your need
       $element->setShowsTime(true);

       return parent::render($element);
   }
}
