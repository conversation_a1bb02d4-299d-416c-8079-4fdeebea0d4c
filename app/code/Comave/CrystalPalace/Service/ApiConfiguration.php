<?php

declare(strict_types=1);

namespace Comave\CrystalPalace\Service;

use Comave\CrystalPalace\Model\ConfigProvider;
use Comave\SellerApi\Api\ConfigurableApiInterface;
use Comave\SellerApi\Model\General\ConfigProvider as GeneralConfigProvider;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

class ApiConfiguration implements ConfigurableApiInterface
{
    private ?string $endpoint = null;
    private array|string|null $params = null;

    /**
     * @param GeneralConfigProvider $globalConfigProvider
     * @param SerializerInterface $serializer
     * @param TimezoneInterface $dateTime
     * @param ConfigProvider $configProvider
     */
    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly GeneralConfigProvider $globalConfigProvider,
        private readonly TimezoneInterface $dateTime,
        private readonly ConfigProvider $configProvider,
    ) {
    }

    /**
     * @inheritDoc
     */
    public function getEndpoint(): string
    {
        return $this->endpoint ?: $this->configProvider->getProductEndpoint();
    }

    /**
     * @inheritDoc
     */
    public function getHeaders(): array
    {
        return [
            'Authorization' => sprintf(
                'Bearer %s',
                $this->configProvider->getApiToken()
            )
        ];
    }

    /**
     * @inheritDoc
     */
    public function getMethod(): string
    {
        return 'GET';
    }

    /**
     * @inheritDoc
     */
    public function getParams(): array|string|null
    {
        if ($this->params !== null) {
            $params = $this->params;
            if (is_string($this->params)) {
                $params = $this->serializer->unserialize($this->params);
            }

            if (!isset($params['length'])) {
                $params['length'] = $this->globalConfigProvider->getPerPage();
            }

            $this->params = $params;
            return $this->params;
        }

        $params = [];
        $currentDate = $this->dateTime->date()->format('Y-m-d H:i:s');
        $syncDate = $this->configProvider->getPullLatestInv() ?
            $currentDate :
            ($this->configProvider->getPullDate() ?: $currentDate);

        $params['sinceDate'] = $syncDate;
        if (!isset($params['length'])) {
            $this->params['length'] = $this->globalConfigProvider->getPerPage();
        }

        $this->params = $params;

        return $this->params;
    }

    /**
     * @inheritDoc
     */
    public function setParams(array|string|null $params): ConfigurableApiInterface
    {
        $this->params = $params;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function setEndpoint(string $endpoint): ConfigurableApiInterface
    {
        $this->endpoint = $endpoint;

        return $this;
    }
}
