<?php

declare(strict_types=1);

namespace Comave\CrystalPalace\Service\Order;

use Comave\CrystalPalace\Model\ConfigProvider;
use Comave\SellerApi\Api\ConfigurableApiInterface;

class ApiConfiguration implements ConfigurableApiInterface
{
    private ?string $endpoint = null;
    private array|string|null $params = null;

    /**
     * @param ConfigProvider $configProvider
     */
    public function __construct(
        private readonly ConfigProvider $configProvider,
    ) {
    }

    /**
     * @inheritDoc
     */
    public function getEndpoint(): string
    {
        return $this->endpoint ?: $this->configProvider->getOrderEndpoint();
    }

    /**
     * @inheritDoc
     */
    public function getHeaders(): array
    {
        return [
            'Authorization' => sprintf(
                'Bearer %s',
                $this->configProvider->getApiToken()
            ),
        ];
    }

    /**
     * @inheritDoc
     */
    public function getMethod(): string
    {
        return 'POST';
    }

    /**
     * @inheritDoc
     */
    public function getParams(): array|string|null
    {
        return $this->params;
    }

    /**
     * @inheritDoc
     */
    public function setParams(array|string|null $params): ConfigurableApiInterface
    {
        $this->params = $params;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function setEndpoint(string $endpoint): ConfigurableApiInterface
    {
        $this->endpoint = $endpoint;

        return $this;
    }
}
