<?php

declare(strict_types=1);

namespace Comave\CrystalPalace\Service\Order;

use Comave\CrystalPalace\Model\ConfigProvider;
use Comave\SellerApi\Api\ConfigurableApiInterface;
use Comave\SellerApi\Api\OrderSynchroniseInterface;
use Comave\SellerApi\Service\RequestHandler;
use Comave\SellerApi\Service\SellerIdentity;
use Magento\Directory\Api\CountryInformationAcquirerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderItemInterface;
use Magento\Store\Model\Information;
use Psr\Log\LoggerInterface;

class SynchroniseService implements OrderSynchroniseInterface
{
    public const string CRYSTAL_PALACE_IDENTIFIER = 'custom_crystalpalace';

    /**
     * @param SellerIdentity $sellerIdentity
     * @param SerializerInterface $serializer
     * @param Information $storeInformation
     * @param CountryInformationAcquirerInterface $countryInformationAcquirer
     * @param ConfigProvider $configProvider
     * @param RequestHandler $requestHandler
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly SellerIdentity $sellerIdentity,
        private readonly SerializerInterface $serializer,
        private readonly Information $storeInformation,
        private readonly CountryInformationAcquirerInterface $countryInformationAcquirer,
        private readonly ConfigProvider $configProvider,
        private readonly RequestHandler $requestHandler,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param ConfigurableApiInterface $configurableApi
     * @param OrderInterface $order
     * @param OrderItemInterface[] $orderItems
     * @return void
     * @throws \Psr\Http\Client\ClientExceptionInterface
     */
    public function synchroniseOrder(
        ConfigurableApiInterface $configurableApi,
        OrderInterface $order,
        array $orderItems
    ): void {
        if (!$this->configProvider->isOrderSyncEnable()) {
            $this->logger->info(
                '[OrderSynchronizer] Not enabled, bypassing',
                [
                    'seller' => self::CRYSTAL_PALACE_IDENTIFIER
                ]
            );

            return;
        }

        $seller = $this->sellerIdentity->identify(self::CRYSTAL_PALACE_IDENTIFIER);
        if ($seller === null) {
            $this->logger->warning(
                '[OrderSynchronizer] Unable to identify seller',
                [
                    'seller' => self::CRYSTAL_PALACE_IDENTIFIER
                ]
            );

            return;
        }

        $data = $this->prepareOrderData($order, $orderItems);
        if (empty($data)) {
            $this->logger->warning(
                '[OrderSynchronizer] Unable to identify seller products',
                [
                    'seller' => self::CRYSTAL_PALACE_IDENTIFIER
                ]
            );

            return;
        }

        $configurableApi->setParams($this->serializer->serialize($data));

        try {
            $results = $this->requestHandler->handleRequest($configurableApi);
            $decodedResult = $this->serializer->unserialize(
                $results->getResult()->getBody()->getContents() ?? '{}'
            );
            if ($results->hasError()) {
                throw new \Exception($decodedResult['message'] ?? 'There was an error processing the order');
            }

            $this->logger->info(
                '[OrderSynchronizer] Successfully synchronized order',
                [
                    'order' => $order->getIncrementId(),
                    'response' => $decodedResult
                ]
            );
        } catch (\Exception $e) {
            $this->logger->error(
                '[OrderSynchronizer] Failed synchronising order data',
                [
                    'error' => $e->getMessage(),
                    'order' => $order->getIncrementId()
                ]
            );
        }
    }

    /**
     * @param OrderInterface $order
     * @param OrderItemInterface[] $orderItems
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function prepareOrderData(
        OrderInterface $order,
        array $orderItems
    ): array {
        $supportPhone = $this->storeInformation->getStoreInformationObject(
            $order->getStore()
        )?->getPhone();

        $billingAddress = $order->getBillingAddress();
        $shippingAddress = $order->getShippingAddress();
        $billingRegion = $billingAddress->getRegion() ?: 'London'; //@todo region is required TBD
        $shippingRegion = $billingAddress->getRegion() ?: 'London'; //@todo region is required TBD
        $shippingCountry = $this->countryInformationAcquirer->getCountryInfo(
            $shippingAddress->getCountryId()
        )?->getFullNameEnglish();
        $billingCountry = $this->countryInformationAcquirer->getCountryInfo(
            $billingAddress->getCountryId()
        )?->getFullNameEnglish();
        $orderData = [
            "order_reference" => 'ComAve-' . $order->getIncrementId(),
            "check_stock" => false,
            "customer_email" => $order->getCustomerEmail(),
            "supporter_number" => $order->getCustomerId() ?? uniqid(), //@todo what is this field since it's required?
            "delivery_method" => $order->getShippingDescription(),
            "collection" => false,
            "collection_store" => 101,
            "marketing_opt_in" => false,
            "customer_address" => [
                "title" => $order->getCustomerPrefix(),
                "first_name" => $order->getCustomerFirstname(),
                "last_name" => $order->getCustomerLastname(),
                "company_name" => $billingAddress->getCompany(),
                "line1" => $billingAddress->getStreetLine(1),
                "line2" => $billingAddress->getStreetLine(2) ?? '',
                "line3" => '',
                "city" => $billingAddress->getCity(),
                "county" => $billingRegion,
                "country" => $billingCountry,
                "postcode" => $billingAddress->getPostcode(),
                "home_tel_no" => $billingAddress->getTelephone(),
                "mobile_tel_no" => $billingAddress->getTelephone()
            ],
            "delivery_address" => [
                "title" => $order->getCustomerPrefix(),
                "first_name" => $order->getCustomerFirstname(),
                "last_name" => $order->getCustomerLastname(),
                "company_name" => $shippingAddress->getCompany(),
                "line1" => $shippingAddress->getStreetLine(1),
                "line2" => $shippingAddress->getStreetLine(2) ?? '',
                "line3" => '',
                "city" => $shippingAddress->getCity(),
                "county" => $shippingRegion,
                "country" => $shippingCountry,
                "postcode" => $shippingAddress->getPostcode(),
                "home_tel_no" => $shippingAddress->getTelephone(),
                "mobile_tel_no" => $shippingAddress->getTelephone()
            ],
            "items" => [],
            "payments" => [
                [
                    "amount" => 0, // This will be calculated below
                    "token" => null,
                    "card_number" => null,
                    "payment_type" => 19 // Adjust based on actual payment type
                ]
            ],
            "postage_amount" => $order->getShippingAmount(),
            "postage_discount" => $order->getShippingDiscountAmount()
        ];

        $sellerTotal = 0;

        foreach ($orderItems as $item) {
            $orderData['items'][] = [
                "product_code" => '2300804-11820', //@todo - seems that they have specific SKU's that need to match $item->getSku(),
                "quantity" => (int) $item->getQtyOrdered(),
                "price" => (string) $item->getPrice(),
                "total_line_price" => $item->getRowTotal(),
                "squad" => true,
                "discount" => $item->getDiscountAmount(),
                "promo_discount" => "0.00",
                "sth_discount" => "0.00",
                "mem_discount" => "0.00"
            ];
            $sellerTotal += $item->getRowTotal();
        }

        $sellerTotal += $order->getShippingAmount();
        // Update the payments and order_total fields with the calculated seller total
        $orderData['payments'][0]['amount'] = $sellerTotal;
        $orderData['order_total'] = (string) $sellerTotal;

        return $orderData;
    }
}
