<?php

declare(strict_types=1);

namespace Comave\CrystalPalace\Service;

use Comave\CrystalPalace\Model\ConfigProvider;
use Comave\SellerApi\Api\ConfigurableApiInterface;
use Comave\SellerApi\Api\ImportableProductsInterface;
use Comave\SellerApi\Api\QueueProductProcessorInterface;
use Comave\SellerApi\Api\QueueProductProcessorInterfaceFactory;
use Comave\SellerApi\Api\ResultsInterface;
use Comave\SellerApi\Model\Queue\Consumer\HandleProducts;
use Comave\SellerApi\Service\RequestHandler;
use GuzzleHttp\Exception\RequestException;
use Magento\Framework\MessageQueue\PublisherInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Psr\Http\Client\ClientExceptionInterface;
use Psr\Log\LoggerInterface;

class ImportService implements ImportableProductsInterface
{
    public const string CRYSTAL_PALACE_IDENTIFIER = 'custom_crystalpalace';

    /**
     * @param PublisherInterface $publisher
     * @param QueueProductProcessorInterfaceFactory $queueObjectFactory
     * @param SerializerInterface $serializer
     * @param ConfigProvider $configProvider
     * @param RequestHandler $requestHandler
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly PublisherInterface $publisher,
        private readonly QueueProductProcessorInterfaceFactory $queueObjectFactory,
        private readonly SerializerInterface $serializer,
        private readonly ConfigProvider $configProvider,
        private readonly RequestHandler $requestHandler,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @inheritDoc
     * @throws \Exception
     */
    public function importProducts(ConfigurableApiInterface $configurableApi): ResultsInterface
    {
        if (!$this->configProvider->isProductSyncEnabled()) {
            throw new \Exception('Crystal Palace Product Sync is disabled');
        }

        try {
            $importResults = $this->requestHandler->handleRequest($configurableApi);
            if ($importResults->hasError()) {
                return $importResults;
            }

            $resultContents = $importResults->getResult()->getBody()->getContents();
            $decoded = $this->serializer->unserialize($resultContents ?? '{}');

            if (empty($decoded) || empty($decoded['data'])) {
                $this->logger->info(
                    '[CrystalPalaceSellerProducts] Products response is empty',
                    [
                        'response' => $decoded['data']
                    ]
                );

                return $importResults;
            }

            $this->logger->info(
                '[CrystalPalaceSellerProducts] Products response',
                [
                    'response' => $decoded['data']
                ]
            );

            if (empty($decoded['data'])) {
                return $importResults;
            }

            $this->publishProduct($decoded['data']);

            if (!isset($decoded['links']['next'])) {
                return $importResults;
            }

            $nextUrl = $decoded['links']['next'];

            do {
                $parsedUrl = parse_url($nextUrl);
                $queryParams = [];
                parse_str($parsedUrl['query'] , $queryParams);
                $configurableApi->setEndpoint(
                    current(explode('?', $nextUrl))
                )->setParams($queryParams);
                $nextResults = $this->requestHandler->handleRequest($configurableApi);
                $decoded = $this->serializer->unserialize(
                    $nextResults->getResult()->getBody()->getContents()
                );

                if (!empty($decoded['data'])) {
                    $this->publishProduct($decoded['data']);
                }

                $hasNextPage = isset($decoded['links']['next']);
                $nextUrl = $hasNextPage ? $decoded['links']['next'] : null;
            } while($hasNextPage);

            return $importResults;
        } catch (\Exception|ClientExceptionInterface $e) {
            /** @var RequestException $e */
            $this->logger->error(
                $e instanceof ClientExceptionInterface ?
                    $e->getResponse()->getBody()->getContents() :
                    $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * @param array $productData
     * @return void
     */
    private function publishProduct(array $productData): void
    {
        /** @var QueueProductProcessorInterface $queueObject */
        $queueObject = $this->queueObjectFactory->create();
        $queueObject->setProductList(
            $this->serializer->serialize($productData)
        )->setSellerIdentifier(static::CRYSTAL_PALACE_IDENTIFIER);

        $this->publisher->publish(
            HandleProducts::TOPIC_NAME,
            $queueObject
        );
    }
}
