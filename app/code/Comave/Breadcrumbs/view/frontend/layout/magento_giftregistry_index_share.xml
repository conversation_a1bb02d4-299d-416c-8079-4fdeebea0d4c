<?xml version="1.0"?>
<!--
/**
 * Comave
 *  
 *
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<!--
	Handle from all gift registry pages pages
	-->		
    <update handle="breadcrumbs_giftregistry"/>
	<!--
	layout for account share gift registry page
	-->	
	<body>
		<referenceBlock name="breadcrumbs">
			<action method="addCrumb" ifconfig="design/breadcrumbs/active">
				<argument name="crumbName" xsi:type="string">account.giftregistry.share</argument>
				<argument name="crumbInfo" xsi:type="array">
					<item name="title" xsi:type="string" translate="true">Share Gift Registry</item>
					<item name="label" xsi:type="string" translate="true">Share Gift Registry</item>
					<item name="last" xsi:type="boolean">true</item>
				</argument>
			</action>
		</referenceBlock>
    </body>
</page>
