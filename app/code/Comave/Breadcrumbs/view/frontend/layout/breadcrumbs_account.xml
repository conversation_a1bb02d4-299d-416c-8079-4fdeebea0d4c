<?xml version="1.0"?>
<!--
/**
 * Comave
 *  
 *
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<!--
	Handle from all pages
	-->		
    <update handle="breadcrumbs_home"/>
	<!--
	layout for all account pages
	-->		
	<body>
		<referenceBlock name="breadcrumbs">
			<action method="addCrumb" ifconfig="design/breadcrumbs/active">
				<argument name="crumbName" xsi:type="string">account</argument>
				<argument name="crumbInfo" xsi:type="array">
					<item name="title" xsi:type="string" translate="true">Account</item>
					<item name="label" xsi:type="string" translate="true">Account</item>
					<item name="link" xsi:type="string">/customer/account</item>
				</argument>
			</action>			
		</referenceBlock>
    </body>
</page>
