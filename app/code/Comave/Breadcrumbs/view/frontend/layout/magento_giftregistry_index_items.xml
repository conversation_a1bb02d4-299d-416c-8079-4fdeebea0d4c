<?xml version="1.0"?>
<!--
/**
 * Comave
 *  
 *
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<!--
	Handle from all gift registry pages pages
	-->		
    <update handle="breadcrumbs_giftregistry"/>
	<!--
	layout for account gift registry items page
	-->	
	<body>
		<referenceBlock name="breadcrumbs">
			<action method="addCrumb" ifconfig="design/breadcrumbs/active">
				<argument name="crumbName" xsi:type="string">account.giftregistry.items</argument>
				<argument name="crumbInfo" xsi:type="array">
					<item name="title" xsi:type="string" translate="true">Gift Registry Items</item>
					<item name="label" xsi:type="string" translate="true">Gift Registry Items</item>
					<item name="last" xsi:type="boolean">true</item>
				</argument>
			</action>
		</referenceBlock>
    </body>
</page>
