<?xml version="1.0"?>
<!--
/**
 * Comave
 *  
 *
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<!--
	layout for all pages
	-->	
    <body>
		<referenceBlock name="breadcrumbs">
			<action method="addCrumb" ifconfig="design/breadcrumbs/active">
				<argument name="crumbName" xsi:type="string">home</argument>
				<argument name="crumbInfo" xsi:type="array">
					<item name="title" xsi:type="string" translate="true">Home</item>
					<item name="label" xsi:type="string" translate="true">Home</item>
					<item name="link" xsi:type="string">/</item>
				</argument>
			</action>
		</referenceBlock>
    </body>
</page>
