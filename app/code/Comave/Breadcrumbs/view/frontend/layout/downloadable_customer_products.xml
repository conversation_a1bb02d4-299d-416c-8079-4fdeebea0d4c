<?xml version="1.0"?>
<!--
/**
 * Comave
 *  
 *
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<!--
	Handle from all customer account pages
	-->	     
	<update handle="breadcrumbs_account"/>
	<!--
	layout for account downloadable products page
	-->		
	<body>
		<referenceBlock name="breadcrumbs">
			<action method="addCrumb" ifconfig="design/breadcrumbs/active">
				<argument name="crumbName" xsi:type="string">account.downloadable</argument>
				<argument name="crumbInfo" xsi:type="array">
					<item name="title" xsi:type="string" translate="true">My Downloadable Products</item>
					<item name="label" xsi:type="string" translate="true">My Downloadable Products</item>
					<item name="last" xsi:type="boolean">true</item>
				</argument>
			</action>
		</referenceBlock>
    </body>
</page>
