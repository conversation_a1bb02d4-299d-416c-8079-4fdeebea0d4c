type Query {
    getClubs: ClubList
        @resolver(class: "Comave\\ClubGraphQl\\Resolver\\ClubListResolver")
        @doc(description: "Returns the list of clubs.")
    getClub(
        id: Int
        uniqueId: String
    ): Club
        @resolver(class: "Comave\\ClubGraphQl\\Resolver\\ClubResolver")
        @doc(description: "Returns the club by ID or unique ID.")
}

type ClubList {
    items: [Club] @doc(description: "List of clubs.")
}

type Club {
    club_id: Int! @doc(description: "The ID of the club.")
    name: String! @doc(description: "The name of the club.")
    subtitle: String! @doc(description: "The subtitle of the club.")
    url_key: String! @doc(description: "The URL key of the club.")
    description: String @doc(description: "The description of the club.")
    image: String @doc(description: "The image of the club.")
    clogo: String @doc(description: "The logo of the club.")
    club_banner: String @doc(description: "The banner of the club.")
    club_prefix: String! @doc(description: "The prefix of the club.")
    club_first_color: String @doc(description: "The first color of the club.")
    club_second_color: String @doc(description: "The second color of the club.")
    club_third_color: String @doc(description: "The third color of the club.")
    uniqueid: String! @doc(description: "The unique ID of the club.")
}
