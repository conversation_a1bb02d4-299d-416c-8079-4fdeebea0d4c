<?php
declare(strict_types=1);

namespace Comave\ClubGraphQl\Resolver;

use Comave\Club\Api\ClubRepositoryInterface;
use Comave\ClubGraphQl\Model\ClubDto;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class ClubResolver implements ResolverInterface
{
    public function __construct(
        private readonly ClubRepositoryInterface $clubRepository,
        private readonly ClubDto $clubDto
    ) {
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (empty($args['id']) && empty($args['uniqueId'])) {
            throw new GraphQlInputException(__('You must specify either "id" or "uniqueId".'));
        }

        try {
            if (!empty($args['id'])) {
                $club = $this->clubRepository->get($args['id']);
            } else {
                $club = $this->clubRepository->getByUniqueId($args['uniqueId']);
            }
        } catch (NoSuchEntityException $e) {
            throw new GraphQlNoSuchEntityException(__($e->getMessage()), $e);
        }

        $this->clubDto->setClub($club);
        return $this->clubDto->toArray();
    }
}
