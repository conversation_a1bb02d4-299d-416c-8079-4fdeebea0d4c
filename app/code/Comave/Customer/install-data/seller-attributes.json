[{"attribute_code": "customer_status", "type": "int", "label": "Customer Status", "input": "select", "source": "Comave\\Customer\\Model\\Entity\\Attribute\\Source\\Status", "backend": null, "required": 0, "user_defined": 1, "system": 0, "unique": 0, "default": "1", "visible": 0, "searchable": 0, "filterable": 0, "used_in_grid": 0, "visible_in_grid": 0, "used_for_customer_segment": 0, "used_in_forms": ["adminhtml_customer"]}, {"attribute_code": "passport", "type": "<PERSON><PERSON><PERSON>", "label": "Passport ID", "input": "text", "source": null, "backend": null, "required": 0, "user_defined": 1, "system": 0, "unique": 0, "default": null, "visible": 1, "searchable": 0, "filterable": 0, "used_in_grid": 0, "visible_in_grid": 0, "used_for_customer_segment": 0, "used_in_forms": ["adminhtml_customer", "customer_account_edit"]}, {"attribute_code": "is_traveller", "type": "int", "label": "Is Traveller", "input": "boolean", "source": "Magento\\Eav\\Model\\Entity\\Attribute\\Source\\Boolean", "backend": null, "required": 0, "user_defined": 1, "system": 0, "unique": 0, "default": null, "visible": 0, "searchable": 0, "filterable": 0, "used_in_grid": 0, "visible_in_grid": 0, "used_for_customer_segment": 0, "used_in_forms": ["adminhtml_customer"]}, {"attribute_code": "fan_earn_reward", "type": "int", "label": "<PERSON> <PERSON><PERSON><PERSON>", "input": "boolean", "source": "Magento\\Eav\\Model\\Entity\\Attribute\\Source\\Boolean", "backend": null, "required": 0, "user_defined": 1, "system": 0, "unique": 0, "default": null, "visible": 0, "searchable": 0, "filterable": 0, "used_in_grid": 0, "visible_in_grid": 0, "used_for_customer_segment": 0, "used_in_forms": ["adminhtml_customer"]}, {"attribute_code": "vatom_id", "type": "<PERSON><PERSON><PERSON>", "label": "Vatom Id", "input": "text", "source": null, "backend": null, "required": 0, "user_defined": 1, "system": 0, "unique": 0, "default": null, "visible": 0, "searchable": 0, "filterable": 1, "used_in_grid": 1, "visible_in_grid": 0, "used_for_customer_segment": 0, "used_in_forms": ["adminhtml_customer"]}, {"attribute_code": "is_social", "type": "int", "label": "Is Social", "input": "boolean", "source": "Magento\\Eav\\Model\\Entity\\Attribute\\Source\\Boolean", "backend": null, "required": 0, "user_defined": 1, "system": 0, "unique": 0, "default": null, "visible": 0, "searchable": 0, "filterable": 0, "used_in_grid": 0, "visible_in_grid": 0, "used_for_customer_segment": 0, "used_in_forms": ["adminhtml_customer"]}]