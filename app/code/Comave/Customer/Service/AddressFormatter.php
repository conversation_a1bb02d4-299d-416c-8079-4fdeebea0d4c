<?php

declare(strict_types=1);

namespace Comave\Customer\Service;

use Magento\Directory\Model\CountryFactory;
use Magento\Customer\Api\Data\AddressInterface;

class AddressFormatter
{
    public function __construct(
        private readonly CountryFactory $countryFactory
    ) {}

    /**
     * @param AddressInterface|null $address
     * @return string
     */
    public function format(?AddressInterface $address): string
    {
        if (!$address) {
            return '';
        }

        return implode(",", array_filter([
            $address->getStreet()[0] ?? '',
            $address->getRegion()?->getRegion() ?? '',
            $address->getCountryId()
                ? $this->countryFactory->create()->loadByCode($address->getCountryId())->getName()
                : ''
        ]));
    }
}
