<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Customer\Service\Group;

use Magento\Framework\App\ResourceConnection;

readonly class Attribute
{
    /**
     * @param \Magento\Framework\App\ResourceConnection $resourceConnection
     */
    public function __construct(private ResourceConnection $resourceConnection)
    {}

    /**
     * @param int $entityId
     * @param string $attributeCode
     * @return string|null
     */
    public function getAttributeValue(int $entityId, string $attributeCode): ?string
    {
        $connection = $this->resourceConnection->getConnection();
        $select = $connection->select()
            ->from([$this->resourceConnection->getTableName('customer_group')], [$attributeCode])
            ->where('customer_group_id = ?', $entityId);

        return $connection->fetchOne($select);
    }
}
