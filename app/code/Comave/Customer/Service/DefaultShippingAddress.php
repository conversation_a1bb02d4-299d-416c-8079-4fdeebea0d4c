<?php

declare(strict_types=1);

namespace Comave\Customer\Service;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\AddressRepositoryInterface;
use Psr\Log\LoggerInterface;

class DefaultShippingAddress
{
    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly AddressRepositoryInterface $addressRepository,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @param int $shippingAddressId
     * @return \Magento\Customer\Api\Data\AddressInterface|null
     */
    public function getDefaultShippingAddress(int $shippingAddressId): ?\Magento\Customer\Api\Data\AddressInterface
    {
        if ($shippingAddressId <= 0) {
            return null;
        }

        try {
            return $this->addressRepository->getById($shippingAddressId);
        } catch (\Exception $e) {
            $this->logger->error(
                'Invalid shipping address id',
                [
                    'shipping_address_id' => $shippingAddressId,
                    'error' => $e->getMessage(),
                ]
            );
        }

        return null;
    }
}
