<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Customer\Service;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\Customer;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Framework\EntityManager\MetadataPool;
use Magento\Framework\Model\ResourceModel\Db\AbstractDb;
use Magento\Framework\Model\ResourceModel\Db\Context;

class Attribute extends AbstractDb
{
    /**
     * @var \Magento\Eav\Model\Config|string
     */
    private $eavConfig;
    /**
     * @var \Magento\Framework\EntityManager\EntityMetadata|\Magento\Framework\EntityManager\EntityMetadataInterface
     */
    private $metadata;

    /**
     * @param \Magento\Framework\Model\ResourceModel\Db\Context $context
     * @param \Magento\Eav\Model\Config $eavConfig
     * @param \Magento\Framework\EntityManager\MetadataPool $metadataPool
     * @param string $connectionName
     * @return void
     */
    public function __construct(
        EavConfig $eavConfig,
        MetadataPool $metadataPool,
        Context $context,
        string $connectionName = null
    ) {
        $this->eavConfig = $eavConfig;
        $this->metadata = $metadataPool->getMetadata(CustomerInterface::class);
        parent::__construct($context, $connectionName);
    }

    /**
     * @return void
     */
    protected function _construct()
    {
        $this->_init('customer_entity', 'entity_id');
    }

    /**
     * @param int $entityId
     * @param string $attributeCode
     * @return string|null
     */
    public function getAttributeValue(int $entityId, string $attributeCode): ?string
    {
        $connection = $this->getConnection();
        $attribute = $this->eavConfig->getAttribute(Customer::ENTITY, $attributeCode);
        $select = $connection->select()
            ->from(['e' => $this->getMainTable()], null)
            ->where('e.entity_id = :entity_id');
        $bind = [
            ':entity_id' => $entityId,
        ];

        if ($attribute->isStatic()) {
            $select->columns($attribute->getAttributeCode(), 'e');
        } else {
            $linkField = $this->metadata->getLinkField();
            $select->join(
                ['vd' => $attribute->getBackendTable()],
                sprintf('e.%s = vd.entity_id AND vd.attribute_id = :attribute_id', $linkField),
                []
            );
            $bind[':attribute_id'] = (int)$attribute->getAttributeId();
            $select->columns('value', 'vd');
        }

        return $connection->fetchOne($select, $bind);
    }

    /**
     * @param string $sku
     * @param string $attributeCode
     * @param int|null $storeId
     * @return string|null
     */
    public function getAttributeValueRaw(int $entityId, string $attributeCode): ?string
    {
        $connection = $this->getConnection();
        $attribute = $this->eavConfig->getAttribute(Customer::ENTITY, $attributeCode);

        $select = sprintf('SELECT %%columns%% FROM %s AS e', $this->getMainTable());
        $columns = [];
        $bind = [
            ':entity_id' => $entityId,
        ];

        if ($attribute->isStatic()) {
            $columns[] = 'e.'.$attribute->getAttributeCode();
        } else {
            $linkField = $this->metadata->getLinkField();
            $select .= sprintf(
                ' INNER JOIN %s AS vd ON e.%s = vd.entity_id AND vd.attribute_id = :attribute_id',
                $attribute->getBackendTable(),
                $linkField
            );

            $bind[':attribute_id'] = (int)$attribute->getAttributeId();
            $columns[] = 'vd.value';
        }
        $select .= ' WHERE e.entity_id = :entity_id';
        $select = strtr($select, ['%columns%' => implode(', ', $columns)]);

        return $connection->fetchOne($select, $bind);
    }
}
