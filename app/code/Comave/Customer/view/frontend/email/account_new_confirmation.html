<!--@subject {{trans "Please confirm your %store_name account" store_name=$store.frontend_name}} @-->
<!--@vars {
"var store.frontend_name":"Store Name",
"var this.getUrl($store,'customer/account/confirm/',[_query:[id:$customer.id,key:$customer.confirmation,back_url:$back_url],_nosid:1])":"Account Confirmation URL",
"var this.getUrl($store, 'customer/account/')":"Customer Account URL",
"var customer.email":"Customer Email",
"var customer.name":"Customer Name",
"var customer.id":"Customer ID",
"var customer.confirmation":"Customer Confirmation Key",
"var back_url":"Back URL",
"var is_seller":"Is Seller"
} @-->

{{template config_path="design/email/header_template"}}

<p class="greeting">{{trans "%name," name=$customer.name}}</p>
<p>{{trans "You must confirm your %customer_email email before you can sign in (link is only valid once):" customer_email=$customer.email}}</p>

<table class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
    <tr>
        <td>
            <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center">
                <tr>
                    <td align="center">
                        <a href="{{config path='web/secure/base_url'}}customer/account/confirm?id={{var customer.id}}&key={{var customer.confirmation}}&back_url={{var back_url}}&_nosid=1" target="_blank" style="font-weight: bold">{{trans "Confirm Your Account"}}</a>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer_template"}}
