<?php
declare(strict_types=1);

namespace Comave\Customer\Plugin\Eav;

use Magento\Framework\Exception\LocalizedException;
use Magento\Eav\Model\Attribute\Data\Text;
use Comave\Customer\Plugin\Model\Validator\AlphaWithHyphen;

readonly class ValidateAlphaWithHyphen
{
    public function __construct(
        private AlphaWithHyphen $alphaWithHyphenValidator
    ) {}

    /**
     * "Alpha with Hyphen" validation rule logic
     *
     * @param Text $subject
     * @param mixed $result
     * @param mixed $value
     * @return mixed
     * @throws LocalizedException
     */
    public function afterValidateValue(Text $subject, $result, $value)
    {
        // Skip validation if previous validation already failed
        if ($result !== true) {
            return $result;
        }

        // Skip validation for empty values
        if (empty($value)) {
            return true;
        }

        // Get attribute validation rules
        $validateRules = $subject->getAttribute()->getValidateRules();

        if (!empty($validateRules['input_validation']) && $validateRules['input_validation'] === 'alpha-with-hyphen') {
            $label = $subject->getAttribute()->getStoreLabel();
            $this->alphaWithHyphenValidator->setMessage(
                __('"%1" contains invalid characters. Only letters, hyphen and space are allowed.', $label),
                AlphaWithHyphen::NOT_ALPHAHYPHEN
            );

            if (!$this->alphaWithHyphenValidator->isValid($value)) {
                return $this->alphaWithHyphenValidator->getMessages();
            }
        }

        return $result;
    }
}
