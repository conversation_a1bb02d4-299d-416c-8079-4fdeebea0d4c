<?php
declare(strict_types=1);

namespace Comave\Customer\Plugin\CustomAttributeManagement;

class AddInputValidationRule
{
    /**
     * Add "Alpha with Hyphen" validation rule to appropriate input types
     * Follow \Magento\CustomerCustomAttributes\Helper\Data::getAttributeInputTypes structure
     *
     * @param \Magento\CustomAttributeManagement\Helper\Data $subject
     * @param array $result
     * @return array
     */
    public function afterGetAttributeInputTypes(
        \Magento\CustomAttributeManagement\Helper\Data $subject,
        array $result
    ): array {
        $validInputTypes = ['text', 'multiline'];

        // runs when called for a specific type
        if (isset($result['validate_filters'])) {
            $result['validate_filters'][] = 'alpha-with-hyphen';
        } else {
            foreach ($validInputTypes as $type) {
                if (isset($result[$type]['validate_filters'])) {
                    $result[$type]['validate_filters'][] = 'alpha-with-hyphen';
                }
            }
        }

        return $result;
    }
}
