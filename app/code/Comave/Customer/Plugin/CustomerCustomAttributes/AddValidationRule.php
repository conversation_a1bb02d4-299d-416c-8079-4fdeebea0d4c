<?php
declare(strict_types=1);

namespace Comave\Customer\Plugin\CustomerCustomAttributes;

class AddValidationRule
{
    /**
     * Add "Alpha with Hyphen" validation rule to the attribute validation dropdown
     *
     * @param \Magento\CustomerCustomAttributes\Helper\Data $subject
     * @param array $result
     * @return array
     */
    public function afterGetAttributeValidateFilters(
        \Magento\CustomerCustomAttributes\Helper\Data $subject,
        array $result
    ): array {
        $result['alpha-with-hyphen'] = __('Alpha with Hyphen');
        return $result;
    }
}
