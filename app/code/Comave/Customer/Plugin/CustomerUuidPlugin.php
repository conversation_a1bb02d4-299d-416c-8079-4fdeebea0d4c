<?php

declare(strict_types=1);

namespace Comave\Customer\Plugin;

use Magento\Customer\Model\Customer;
use Magento\Framework\DataObject\IdentityService;

class CustomerUuidPlugin
{
    private const CUSTOMER_UUID_ATTRIBUTE_CODE = 'commave_uuid';

    /**
     * Construct function
     *
     * @param \Magento\Framework\DataObject\IdentityService $identityService
     */
    public function __construct(private readonly IdentityService $identityService)
    {
    }

    /**
     * Before save plugin for customer
     *
     * @param \Magento\Customer\Model\Customer $customer
     * @return void
     */
    public function beforeSave(Customer $customer): void
    {
        if (!$customer->isObjectNew() || $customer->getData(self::CUSTOMER_UUID_ATTRIBUTE_CODE)) {
            return;
        }

        // Generate and set the customer_uuid
        $uuid = (string)$this->identityService->generateId();
        $customer->setData(self::CUSTOMER_UUID_ATTRIBUTE_CODE, $uuid);
    }
}
