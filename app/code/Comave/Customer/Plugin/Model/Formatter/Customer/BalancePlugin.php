<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Customer\Plugin\Model\Formatter\Customer;

use Magento\RewardGraphQl\Model\Formatter\Customer\Balance;

class BalancePlugin
{
    /**
     * @param \Magento\RewardGraphQl\Model\Formatter\Customer\Balance $subject
     * @param array $result
     * @return array
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterFormat(Balance $subject, array $result): array
    {
        $result['points'] = $result['points'] ?? 0;

        return $result;
    }
}
