<?php
declare(strict_types=1);

namespace Comave\Customer\Plugin\Model\Validator;

use Magento\Framework\Locale\ResolverInterface as LocaleResolverInterface;
use Laminas\Validator\AbstractValidator;
use <PERSON>inas\Stdlib\StringUtils;
use Locale;

class AlphaWithHyphen extends AbstractValidator
{
    public const INVALID      = 'alphahyphenInvalid';
    public const NOT_ALPHAHYPHEN    = 'notAlphahyphen';
    public const STRING_EMPTY = 'alphahyphenStringEmpty';

    protected $messageTemplates = [
        self::INVALID      => 'Invalid type given. Only letters, spaces, and hyphens are allowed.',
        self::NOT_ALPHAHYPHEN    => 'The input contains invalid characters. Only letters, spaces, and hyphens are allowed.',
        self::STRING_EMPTY => 'The input is an empty string.',
    ];

    /**
     * @var LocaleResolverInterface
     */
    private $localeResolver;

    /**
     * Constructor.
     *
     * @param LocaleResolverInterface $localeResolver
     */
    public function __construct(LocaleResolverInterface $localeResolver)
    {
        parent::__construct();
        $this->localeResolver = $localeResolver;
    }

    /**
     * Returns true if and only if $value contains only alphabetic chars, spaces or hyphens
     * @see \Laminas\I18n\Filter\Alpha::filter for validation logic used
     * @param mixed $value
     * @return bool
     */
    public function isValid($value): bool
    {
        if (!is_string($value)) {
            $this->error(self::INVALID);
            return false;
        }

        $this->setValue($value);

        if ('' === $value) {
            $this->error(self::STRING_EMPTY);
            return false;
        }

        $whiteSpace = '\s'; // Allow spaces
        $language   = Locale::getPrimaryLanguage($this->localeResolver->getLocale());

        // Our filter implementation, inspired from \Laminas\I18n\Filter\Alpha::filter, but with added hyphen
        if (!StringUtils::hasPcreUnicodeSupport()) {
            // POSIX named classes are not supported, use alternative [a-zA-Z] match
            $pattern = '/[^a-zA-Z' . $whiteSpace . '-]/';
        } elseif (in_array($language, ['ja', 'ko', 'zh'], true)) {
            // Use English alphabet for certain languages
            $pattern = '/[^a-zA-Z' . $whiteSpace . '-]/u';
        } else {
            // Use native language alphabet
            $pattern = '/[^\p{L}' . $whiteSpace . '-]/u';
        }

        if (preg_match($pattern, $value)) {
            $this->error(self::NOT_ALPHAHYPHEN);
            return false;
        }

        return true;
    }
}
