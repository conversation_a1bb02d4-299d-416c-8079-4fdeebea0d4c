<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Customer\Plugin\Model;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\AccountManagement;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Integration\Api\CustomerTokenServiceInterface;
use Psr\Log\LoggerInterface;

class AccountManagementPlugin
{
    /**
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Integration\Api\CustomerTokenServiceInterface $tokenService
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly CustomerTokenServiceInterface $tokenService
    ) {
    }

    /**
     * @param \Magento\Customer\Model\AccountManagement $subject
     * @param bool $result
     * @param $email
     * @return bool
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterResetPassword(AccountManagement $subject, bool $result, $email): bool
    {
        try {
            $customer = $this->customerRepository->get($email);
            $this->tokenService->revokeCustomerAccessToken($customer->getId());
        } catch (NoSuchEntityException|LocalizedException $e) {
            $this->logger->warning($e->getMessage());
        }

        return $result;
    }

    /**
     * @param \Magento\Customer\Model\AccountManagement $subject
     * @param bool $result
     * @param $email
     * @return bool
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterChangePassword(AccountManagement $subject, bool $result, $email): bool
    {
        try {
            $customer = $this->customerRepository->get($email);
            $this->tokenService->revokeCustomerAccessToken($customer->getId());
        } catch (NoSuchEntityException|LocalizedException $e) {
            $this->logger->warning($e->getMessage());
        }

        return $result;
    }

    /**
     * @param \Magento\Customer\Model\AccountManagement $subject
     * @param bool $result
     * @param $customerId
     * @return bool
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterChangePasswordById(AccountManagement $subject, bool $result, $customerId): bool
    {
        try {
            $this->tokenService->revokeCustomerAccessToken($customerId);
        } catch (LocalizedException $e) {
            $this->logger->warning($e->getMessage());
        }

        return $result;
    }
}
