<?php

declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use Magento\Eav\Setup\EavSetup;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class CreateOrUpdateCustomerAttributesV3 implements DataPatchInterface
{
    /**
     * @var mixed[]
     */
    private array $attributesToUpdate = [
        'customer' => [
            'phone_no' => [
                'input_validation' => 'numeric',
                'max_text_length'  => '255',
                'min_text_length'  => '1',
            ],
        ],
        'customer_address' => [
            'city' => [
                'input_validation' => 'alphanum-with-spaces'
            ],
            'fax' => [
                'input_validation' => 'numeric',
                'max_text_length'  => '255',
                'min_text_length'  => '1',
            ],
            'firstname' => [
                'input_validation' => 'alpha-with-hyphen'
            ],
            'lastname' => [
                'input_validation' => 'alpha-with-hyphen'
            ],
            'middlename' => [
                'input_validation' => 'alpha-with-hyphen'
            ],
            'postcode' => [
                'input_validation' => 'numeric',
                'max_text_length'  => '255',
                'min_text_length'  => '1',
            ],
            'street' => [
                'input_validation' => 'alphanum-with-spaces'
            ],
            'telephone' => [
                'input_validation' => 'numeric',
                'max_text_length'  => '255',
                'min_text_length'  => '1',
            ],
        ]
    ];

    /**
     * Construct function
     *
     * @param \Magento\Framework\Setup\ModuleDataSetupInterface $moduleDataSetup
     * @param \Magento\Eav\Setup\EavSetup $eavSetup
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetup $eavSetup,
        private readonly SerializerInterface $serializer
    ) {
    }

    /**
     * Apply function
     *
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->startSetup();

        foreach ($this->attributesToUpdate as $entityType => $attributes) {
            foreach ($attributes as $attributeCode => $validationRule) {
                $this->updateAttribute($entityType, $attributeCode, $validationRule);
            }
        }

        $this->moduleDataSetup->endSetup();
    }

    /**
     * @inheritDoc
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies(): array
    {
        return [
            CustomerSetupV3::class
        ];
    }

    /**
     * Update Attribute function
     *
     * @param string $entityType
     * @param string $attributeCode
     * @param mixed[] $validationRule
     * @return void
     */
    private function updateAttribute(string $entityType, string $attributeCode, array $validationRule): void
    {
        $this->eavSetup->updateAttribute(
            $entityType,
            $attributeCode,
            'validate_rules',
            $this->serializer->serialize($validationRule)
        );
    }
}
