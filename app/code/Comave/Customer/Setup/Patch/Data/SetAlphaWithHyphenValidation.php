<?php
declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use Magento\Customer\Model\Customer;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Serialize\SerializerInterface;

class SetAlphaWithHyphenValidation implements DataPatchInterface
{
    /**
     * List of attributes to update
     */
    private const array CUSTOMER_ATTRIBUTES = ['firstname', 'lastname'];

    /**
     * Constructor
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly SerializerInterface $serializer
    ) {}

    /**
     * Apply data patch to update validation rules
     */
    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        foreach (self::CUSTOMER_ATTRIBUTES as $attributeCode) {
            $validationRules = [
                'min_text_length'  => '1',
                'max_text_length'  => '255',
                'input_validation' => 'alpha-with-hyphen'
            ];

            $eavSetup->updateAttribute(
                Customer::ENTITY,
                $attributeCode,
                'validate_rules',
                $this->serializer->serialize($validationRules)
            );
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * Get dependencies
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * Get aliases
     */
    public function getAliases()
    {
        return [];
    }
}
