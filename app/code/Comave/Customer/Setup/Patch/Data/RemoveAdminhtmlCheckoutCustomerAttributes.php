<?php
declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

class RemoveAdminhtmlCheckoutCustomerAttributes implements DataPatchInterface
{
    /**
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return void
     */
    public function apply()
    {
        $connection = $this->resourceConnection->getConnection();

        $attributeCodes = [
            'wkv_club_prefix',
            'wkv_club_first_color',
            'wkv_club_second_color',
            'wkv_club_third_color',
            'wkv_clubbanner',
            'wkv_club_first_banner',
            'wkv_club_second_banner',
            'wkv_club_third_banner_mob',
            'wkv_club_watermark_image',
            'wkv_club_subtitles',
            'wkv_club_logo',
            'wkv_clubbanner_mobile',
            'wkv_club_first_banner_mob',
            'wkv_club_fan_count',
            'wkv_club_name',
            'wkv_club_unique_identfier',
            'wkv_comave_comm',
            'wkv_comm_inc',
            'wkv_last_order_rej',
            'commave_uuid',
            'wkv_club_organ_id',
            'lix_uid',
            'easypost_api_key',
            'wkv_club_third_banner',
            'wkv_club_sec_banner_mob'

        ];

        $eavTable = $connection->getTableName('eav_attribute');
        $formAttributeTable = $connection->getTableName('customer_form_attribute');

        $attributeIds = $connection->fetchCol(
            $connection->select()
                ->from(['eav' => $eavTable], ['attribute_id'])
                ->where('eav.attribute_code IN (?)', $attributeCodes)
        );

        if (!empty($attributeIds)) {
            try {
                $connection->delete(
                    $formAttributeTable,
                    [
                        'form_code = ?' => 'adminhtml_checkout',
                        'attribute_id IN (?)' => $attributeIds
                    ]
                );
            } catch (\Exception $e) {
                $this->logger->error('Failed to remove adminhtml_checkout attributes', [
                    'exception' => $e,
                    'attribute_ids' => $attributeIds,
                ]);
            }
        }

        return $this;
    }

    /**
     * @return array
     */
    public function getAliases()
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies()
    {
        return [];
    }
}
