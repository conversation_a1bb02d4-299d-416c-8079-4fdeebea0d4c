<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use Magento\Customer\Model\Customer;
use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Eav\Model\Entity\Attribute\Set;
use Magento\Eav\Model\Entity\Attribute\SetFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class UpdateReadWriteCustomerAttributes implements DataPatchInterface
{
    /**
     * @param \Magento\Framework\Setup\ModuleDataSetupInterface $moduleDataSetup
     * @param \Magento\Customer\Setup\CustomerSetupFactory $customerSetupFactory
     * @param \Magento\Eav\Model\Entity\Attribute\SetFactory $attributeSetFactory
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly CustomerSetupFactory $customerSetupFactory,
        private readonly SetFactory $attributeSetFactory
    ) {
    }

    /**
     * @return $this
     */
    public function apply(): self
    {
        $attributeCodes = [
            'wkv_last_order_rej',
            'default_phone_number',
        ];
        foreach ($attributeCodes as $attributeCode) {
            $this->updateAttribute($attributeCode);
        }

        return $this;
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies(): array
    {
        return [
            InstallSellerAttributes::class,
        ];
    }

    /**
     * @inheritdoc
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @param $attributeCode
     * @return void
     */
    private function updateAttribute($attributeCode): void
    {
        $customerSetup = $this->customerSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $attribute = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, $attributeCode);
        $customerEntity = $customerSetup->getEavConfig()->getEntityType(Customer::ENTITY);
        $attributeSetId = $customerEntity->getDefaultAttributeSetId();
        /** @var $attributeSet Set */
        $attributeSet = $this->attributeSetFactory->create();
        $attributeGroupId = $attributeSet->getDefaultGroupId($attributeSetId);
        if ($attribute->getId()) {
            try {
                $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, $attributeCode)
                    ->setData('used_in_forms', ['adminhtml_customer', 'adminhtml_checkout'])
                    ->setData('attribute_set_id', $attributeSetId)
                    ->setData('attribute_group_id', $attributeGroupId)
                    ->save();
            } catch (LocalizedException $e) {
            }
        }
    }
}
