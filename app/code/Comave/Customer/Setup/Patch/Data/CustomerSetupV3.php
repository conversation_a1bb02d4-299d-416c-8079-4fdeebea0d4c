<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use Magento\Customer\Model\Customer;
use Magento\Customer\Setup\CustomerSetup;
use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchRevertableInterface;

class CustomerSetupV3 implements DataPatchInterface, PatchRevertableInterface
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param CustomerSetupFactory $customerSetupFactory
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly CustomerSetupFactory $customerSetupFactory,
    ) {
    }

    /**
     * @inheritdoc
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Validator\ValidateException
     */
    public function apply(): self
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->customerSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $this->addCustomerClubAttribute($customerSetup);
        $this->addPhoneNumberAttribute($customerSetup);
        $this->addCustomerCountryAttribute($customerSetup);

        return $this;
    }


    /**
     * Add customer club attribute
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Validator\ValidateException
     */
    private function addCustomerClubAttribute(CustomerSetup $customerSetup): void
    {
        $customerClub = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, 'customerclub');
        if (!$customerClub->getId()) {
            $customerSetup->addAttribute(
                Customer::ENTITY,
                'customerclub',
                [
                    'type' => 'varchar',
                    'label' => 'Which club do you support?',
                    'input' => 'select',
                    'required' => false,
                    'sort_order' => 100,
                    'position' => 100,
                    'visible' => true,
                    'system' => false,
                    'user_defined' => true,
                    'is_used_in_grid' => true,
                    'is_visible_in_grid' => true,
                    'is_filterable_in_grid' => true,
                    'is_searchable_in_grid' => true,
                    'default' => '',
                    'used_in_forms' => [
                        'adminhtml_customer',
                        'customer_account_create',
                        'customer_account_edit',
                    ],
                ]
            );
        }
    }

    /**
     * Add phone number attribute
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Validator\ValidateException
     */
    private function addPhoneNumberAttribute(CustomerSetup $customerSetup): void
    {
        $phoneNoAttribute = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, 'phone_no');
        if ($phoneNoAttribute->getId()) {
            $customerSetup->updateAttribute(
                Customer::ENTITY,
                $phoneNoAttribute->getId(),
                'is_filterable_in_grid',
                false
            );
            $customerSetup->updateAttribute(
                Customer::ENTITY,
                $phoneNoAttribute->getId(),
                'is_searchable_in_grid',
                false
            );
        } else {
            $customerSetup->addAttribute(
                Customer::ENTITY,
                'phone_no',
                [
                    'type' => 'varchar',
                    'label' => 'Phone no.',
                    'input' => 'text',
                    'required' => false,
                    'sort_order' => 110,
                    'visible' => true,
                    'system' => false,
                    'user_defined' => true,
                    'position' => 110,
                    'is_used_in_grid' => true,
                    'is_visible_in_grid' => true,
                    'is_filterable_in_grid' => false,
                    'is_searchable_in_grid' => false,
                    'adminhtml_only' => false,
                    'used_in_forms' => [
                        'adminhtml_customer',
                        'customer_account_create',
                        'customer_account_edit',
                    ],
                ]
            );
        }
    }

    /**
     * Add customer country attribute
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Validator\ValidateException
     */
    private function addCustomerCountryAttribute(CustomerSetup $customerSetup): void
    {
        $customerCountryAttribute = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, 'customer_country');
        if ($customerCountryAttribute->getId()) {
            $customerSetup->updateAttribute(
                Customer::ENTITY,
                $customerCountryAttribute->getId(),
                'is_filterable_in_grid',
                false
            );
            $customerSetup->updateAttribute(
                Customer::ENTITY,
                $customerCountryAttribute->getId(),
                'is_searchable_in_grid',
                false
            );
        } else {
            $customerSetup->addAttribute(
                Customer::ENTITY,
                'customer_country',
                [
                    'type' => 'text',
                    'label' => 'Customer Country',
                    'input' => 'textarea',
                    'required' => false,
                    'sort_order' => 120,
                    'visible' => true,
                    'system' => false,
                    'user_defined' => true,
                    'position' => 120,
                    'is_used_in_grid' => true,
                    'is_visible_in_grid' => true,
                    'is_filterable_in_grid' => false,
                    'is_searchable_in_grid' => false,
                    'adminhtml_only' => false,
                    'used_in_forms' => [
                        'adminhtml_customer',
                        'customer_account_create',
                        'customer_account_edit',
                    ],
                ]
            );
        }
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * Revert patch
     */
    public function revert(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->customerSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $customerSetup->removeAttribute(Customer::ENTITY, 'customerclub');
        $customerSetup->removeAttribute(Customer::ENTITY, 'phone_no');
        $customerSetup->removeAttribute(Customer::ENTITY, 'customer_country');

        $this->moduleDataSetup->getConnection()->endSetup();
    }
}
