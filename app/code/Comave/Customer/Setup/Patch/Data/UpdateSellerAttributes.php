<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use <PERSON>gento\Customer\Model\Customer;
use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class UpdateSellerAttributes implements DataPatchInterface
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param CustomerSetupFactory $customerSetupFactory
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly CustomerSetupFactory $customerSetupFactory,
    ) {
    }

    /**
     * @inheritdoc
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Validator\ValidateException
     */
    public function apply(): self
    {
        $attributeCodes = ['customer_status', 'passport', 'is_traveller', 'fan_earn_reward', 'vatom_id', 'is_social'];
        foreach ($attributeCodes as $attributeCode) {
            $this->updateAttribute($attributeCode);
        }

        return $this;
    }

    /**
     * @param $attributeCode
     * @return void
     */
    private function updateAttribute($attributeCode): void
    {
        $customerSetup = $this->customerSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $attribute = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, $attributeCode);
        if ($attribute->getId()) {
            $tableName = $this->moduleDataSetup->getConnection()->getTableName('customer_form_attribute');
            $this->moduleDataSetup->getConnection()->insertOnDuplicate(
                $tableName,
                [
                    'form_code' => 'adminhtml_customer',
                    'attribute_id' => $attribute->getId(),
                ],
                ['form_code', 'attribute_id']
            );
        }
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies(): array
    {
        return [
            InstallSellerAttributes::class,
        ];
    }

    /**
     * @inheritdoc
     */
    public function getAliases(): array
    {
        return [];
    }
}
