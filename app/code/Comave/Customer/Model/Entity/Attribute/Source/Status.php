<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Customer\Model\Entity\Attribute\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;

class Status extends AbstractSource
{
    private const int STATUS_INACTIVE = 0;
    private const int STATUS_ACTIVE = 1;

    /**
     * Retrieve All options
     *
     * @return array
     */
    public function getAllOptions(): array
    {
        if ($this->_options === null) {
            $this->_options = [
                ['label' => __('Active'), 'value' => self::STATUS_ACTIVE],
                ['label' => __('Inactive'), 'value' => self::STATUS_INACTIVE],
            ];
        }

        return $this->_options;
    }

    /**
     * Retrieve the option array
     *
     * @return array
     */
    public function getOptionArray(): array
    {
        $_options = [];
        foreach ($this->getAllOptions() as $option) {
            $_options[$option['value']] = $option['label'];
        }

        return $_options;
    }
}