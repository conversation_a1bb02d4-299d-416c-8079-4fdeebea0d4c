<?php

declare(strict_types=1);

namespace Comave\Customer\Model\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;

class RefundMethod extends AbstractSource
{
    public const METHOD_IBAN = 'iban';
    public const METHOD_CREDIT_CARD = 'credit_card';

    public const LABEL_IBAN = 'IBAN';
    public const LABEL_CREDIT_CARD = 'Credit Card';

    /**
     * @return array[]
     */
    public function getAllOptions(): array
    {
        return [
            ['value' => '', 'label' => __(' ')],
            ['value' => self::METHOD_IBAN, 'label' => __(self::LABEL_IBAN)],
            ['value' => self::METHOD_CREDIT_CARD, 'label' => __(self::LABEL_CREDIT_CARD)],
        ];
    }
}
