<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<body>
		<referenceContainer name="content">
			<referenceContainer name="product.info.main">
				<block class="Comave\Club\Block\Product\View" name="product.info.club" after="product.info.price" template="Comave_Club::product/view/logo.phtml" ifconfig="comave_club/product_view_page/enable_club_info"/>
			</referenceContainer>
		</referenceContainer>
	</body>
</page>