<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<body>
        <referenceContainer name="columns.top">
            <container name="club.view.container" htmlTag="div" htmlClass="club-view" after="-">
                <block class="Comave\Club\Block\Club\View" name="club.image" template="Comave_Club::club/image.phtml"/>
                <block class="Comave\Club\Block\Club\View" name="club.description" template="Comave_Club::club/description.phtml"/>
            </container>
        </referenceContainer>
        <referenceContainer name="sidebar.main">
            <block class="Comave\Club\Block\GroupList" name="group_list" template="Comave_Club::grouplist.phtml"/>
            <block class="Magento\Framework\View\Element\Template" ifconfig="comave_club/general_settings/enable_search" name="club.searchform.main" template="Comave_Club::search/form.phtml" before="-"/>
        </referenceContainer>
        <referenceContainer name="content">
            <block class="Comave\Club\Block\Club\View" name="club.products" template="Comave_Club::club/view.phtml">
                <block class="Comave\Club\Block\Club\Product\ListProduct" name="club.products.list" as="product_list" template="Magento_Catalog::product/list.phtml">
                    <container name="category.product.list.additional" as="additional" />
                    <block class="Magento\Framework\View\Element\RendererList" name="category.product.type.details.renderers" as="details.renderers">
                        <block class="Magento\Framework\View\Element\Template" as="default"/>
                    </block>
                    <block class="Magento\Catalog\Block\Product\ProductList\Toolbar" name="product_list_toolbar" template="Magento_Catalog::product/list/toolbar.phtml">
                        <block class="Magento\Theme\Block\Html\Pager" name="product_list_toolbar_pager"/>
                    </block>
                    <action method="setToolbarBlockName">
                        <argument name="name" xsi:type="string">product_list_toolbar</argument>
                    </action>
                </block>
            </block>
            <block class="Magento\Cookie\Block\RequireCookie" name="require-cookie" template="Magento_Cookie::require_cookie.phtml">
                <arguments>
                    <argument name="triggers" xsi:type="array">
                        <item name="compareProductLink" xsi:type="string">.action.tocompare</item>
                    </argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
