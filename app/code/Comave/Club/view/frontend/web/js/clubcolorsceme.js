define([
    "jquery",
    "mage/mage",
    "jquery/ui"
    ], function($){
    'use strict';

    return function(config, element){
        var clubCollection = config.customData;
        $(document).ready(function() {
            if(clubCollection > 0)
            {
                $( ".product .product-item-info" ).addClass('sport-club');
                $( ".product-items .product-item .product-item-info" ).addClass('sport-club');
                $( ".action.tocart.primary" ).addClass('sport-club-tocart');
                $( ".cms-comave-retail .homeSlider .owl-carousel" ).addClass('sport-club-retailbanner');
                $( ".custom-slider .owl-controls .owl-prev" ).addClass('sport-club-owlprev');
                $( ".custom-slider .owl-controls .owl-next" ).addClass('sport-club-owlnext');
                $( ".weltpixel-quickview.weltpixel_quickview_button_v2" ).addClass('sport-club-quickview');
                $( ".cms-comave-retail .content.container-clubproducts" ).addClass('sport-club-productcarousel');
                $( ".cms-food-delivery-app .working-inner" ).addClass('food-sport-club-working-inner');
                $( ".marketplace-seller-collection .item.product.product-item button.action.tocart.primary" ).addClass('sports-food');
                $( ".marketplace-seller-collection .products.list.items.product-items li.item.product.product-item" ).addClass('sports-food');
                $( ".marketplace-seller-sellerlist .category-food" ).addClass('sports-category');
                $( ".marketplace-seller-sellerlist .category-slider" ).addClass('sports-banner-offer');
                $( ".marketplace-seller-sellerlist .columns .container.slick-slider button.slick-next.slick-arrow" ).addClass('sports-slide');
                $( ".marketplace-seller-sellerlist .columns .container.slick-slider button.slick-prev.slick-arrow" ).addClass('sports-slide');
                $( ".marketplace-seller-sellerlist .columns .container .slick-slider button.slick-next.slick-arrow" ).addClass('sports-slide');
                $( ".marketplace-seller-sellerlist .columns .container .slick-slider button.slick-prev.slick-arrow" ).addClass('sports-slide');
                $( ".marketplace-seller-collection .product-item-info.sport-club .addons-buttons a.weltpixel-quickview.weltpixel_quickview_button_v2" ).addClass('sport-club-quickview');
                $( ".action.primary" ).addClass('sport-club-action');
                $( ".action" ).addClass('sport-club-action');
                $( ".card.restaurant-card .resto-card-details" ).addClass('sports-seller');
                $( "body.theme-pearl" ).addClass('sports-club');
                $( ".wp-quickview-popup .box-tocart .action.primary.tocart" ).addClass('sport-club-tocart');
                $( ".qty-button" ).addClass('sport-club-qty');
                $( ".container-crystal a.cryviewprod" ).addClass('sport-club-viewmore');
                $( ".action.primary.checkout" ).addClass('sport-checkout');
                $( ".action.action-select-shipping-item" ).addClass('sport-shipping');
                $( ".action.action-show-popup" ).addClass('sport-show-popup');
                $( ".modal-popup .modal-inner-wrap .modal-footer .action.primary.action-save-address" ).addClass('sport-save-add');
                $( ".modal-popup .modal-inner-wrap .modal-footer .action.primary.action-hide-popup" ).addClass('sport-hide-popup');
                $( ".action.continue.primary" ).addClass('sport-continue');
                $( ".marketplace-seller-sellerlist .columns .container.top-rated-resto.slick-slider button.slick-next.slick-arrow" ).addClass('sports-slide');
                $( ".marketplace-seller-sellerlist .columns .container.top-rated-resto.slick-slider button.slick-prev.slick-arrow" ).addClass('sports-slide');
                $( ".marketplace-seller-sellerlist .columns latest-resto" ).addClass('sports-letest');
                $( ".marketplace-seller-sellerlist .columns toprated-resto" ).addClass('sports-top');
            }
            else
            {
                $( ".action.continue.primary" ).last().removeClass('sport-continue');
                $( ".modal-popup .modal-inner-wrap .modal-footer .action.primary.action-hide-popup" ).last().removeClass('sport-hide-popup');
                $( ".modal-popup .modal-inner-wrap .modal-footer .action.primary.action-save-address" ).last().removeClass('sport-save-add');
                $( ".action.action-show-popup" ).last().removeClass('sport-show-popup');
                $( ".action.action-select-shipping-item" ).last().removeClass('sport-shipping');
                $( ".action.primary.checkout" ).last().removeClass('sport-checkout');
                $( ".container-crystal a.cryviewprod" ).last().removeClass('sport-club-viewmore');
                $( ".qty-button" ).last().removeClass('sport-club-qty');
                $( ".wp-quickview-popup .box-tocart .action.primary.tocart" ).last().removeClass('sport-club-tocart');
                $( "body.theme-pearl" ).last().removeClass('sports-club');
                $( ".card.restaurant-card .resto-card-details" ).last().removeClass('sports-seller');
                $( ".action" ).last().removeClass('sport-club-action');
                $( ".action.primary" ).last().removeClass('sport-club-action');
                $( ".marketplace-seller-collection .product-item-info.sport-club .addons-buttons a.weltpixel-quickview.weltpixel_quickview_button_v2" ).last().removeClass('sport-club-quickview');
                $( ".marketplace-seller-sellerlist .columns .container.slick-slider button.slick-prev.slick-arrow" ).last().removeClass('sports-slide');
                $( ".marketplace-seller-sellerlist .columns .container.slick-slider button.slick-next.slick-arrow" ).last().removeClass('sports-slide');
                $( ".marketplace-seller-sellerlist .columns .container .slick-slider button.slick-prev.slick-arrow" ).last().removeClass('sports-slide');
                $( ".marketplace-seller-sellerlist .columns .container .slick-slider button.slick-next.slick-arrow" ).last().removeClass('sports-slide');
                $( ".marketplace-seller-sellerlist .category-slider" ).last().removeClass('sports-banner-offer');
                $( ".marketplace-seller-sellerlist .category-food" ).last().removeClass('sports-category');
                $( ".product .product-item-info" ).last().removeClass('sport-club');
                $( ".product-items .product-item .product-item-info" ).last().removeClass('sport-club');
                $( ".action.tocart.primary" ).last().removeClass('sport-club-tocart');
                $( ".cms-comave-retail .homeSlider .owl-carousel" ).last().removeClass('sport-club-retailbanner');
                $( ".custom-slider .owl-controls .owl-prev" ).last().removeClass('sport-club-owlprev');
                $( ".custom-slider .owl-controls .owl-next" ).last().removeClass('sport-club-owlnext');
                $( ".weltpixel-quickview.weltpixel_quickview_button_v2" ).last().removeClass('sport-club-quickview');
                $( ".cms-comave-retail .content.container-clubproducts" ).last().removeClass('sport-club-productcarousel');
                $( ".cms-food-delivery-app .working-inner" ).last().removeClass('food-sport-club-working-inner');
                $( ".marketplace-seller-collection .item.product.product-item button.action.tocart.primary" ).last().removeClass('sports-food');
                $( ".marketplace-seller-collection .products.list.items.product-items li.item.product.product-item" ).last().removeClass('sports-food');
                $( ".marketplace-seller-sellerlist .columns latest-resto" ).last().removeClass('sports-letest');
                $( ".marketplace-seller-sellerlist .columns toprated-resto" ).last().removeClass('sports-top');
            }
        });
        
    }
});
