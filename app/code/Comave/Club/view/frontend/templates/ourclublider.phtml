<?php $collection = $block->getClubCustomerCollection();

$writer = new \Zend_Log_Writer_Stream(BP . '/var/log/ourclubs.log');
            $logger = new \Zend_Log();
            $logger->addWriter($writer);
            $logger->info('order products');

$helper = $this->helper('Comave\Club\Helper\Data');
if(isset($collection)){
if($collection->count()>0)
{?>
<div class="OurClubs">
	<div class="ClubContent">
		<h2 class=clubtitle>Our Clubs</h2>
		<p class="clubcontent"> <b>Elevate your shopping needs with ComAve!</b><br>
			ComAve is proud of its exciting partnership with illustrious sports clubs designed to change the game when it comes to online shopping, Don't Miss Out on the Action – Gear Up with ComAve today, support your club and get the ultimate fan shopping experience!</p>
	</div>
	<div class="club-banner">
		<?php foreach ($collection as $seller) {
			$sellerid = $seller->getEntityId();
            $urlcollection = $block->getClubSellerCollection($sellerid);
			 $logger->info(print_r($sellerid,true).'  seller id');
         $mediaurl = $helper->getMediaUrl(). 'vendorfiles/image/'. $seller['wkv_clubbanner'];
         $clubUrl = $helper->getBaseUrl() . 'marketplace/seller/collection/shop/' . $urlcollection;
		 ?>
        <div class="clublogo">
            <a href="<?php echo $clubUrl; ?>"><img class="logo" src="<?php echo $mediaurl; ?>" /></a>
        	 
        </div>
		<?php }?>
	</div>
    
</div>


<?php } }
?>

	
		

	
