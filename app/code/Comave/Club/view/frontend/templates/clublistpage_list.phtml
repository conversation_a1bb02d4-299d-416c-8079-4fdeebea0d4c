<?php
$_clubCollection = $block->getCollection();
$show_club_name = $this->getConfig('club_list_page/show_club_name');
?>
<?php if ( $_clubCollection->count() ){ ?>
<?php $total = $_clubCollection->getSize(); ?>
<div class="clublist">
	<div class="block-content">
		<?php foreach ($_clubCollection as $_club) { ?>
		<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
			<div class="club-item">
				<div class="club-image"><a href="<?php echo $_club->getUrl(); ?>" title="<?php echo $_club->getName(); ?>"><img src="<?php echo $_club->getClogoUrl(); ?>" alt="<?php echo $_club->getName(); ?>"/></a></div>
				<?php if($show_club_name){ ?>
				<div class="club-name"><a href="<?php echo $_club->getUrl(); ?>" title="<?php echo $_club->getName(); ?>"><?php echo $_club->getName() ?></a></div>
				<?php } ?>
			</div>
		</div>
		<?php } ?>
	</div>
	<?php if($html = $block->getChildHtml('toolbar')){ ?>
	<div class="club-toolbar  toolbar toolbar-products">
		<?php echo $block->getChildHtml('toolbar'); ?>
	</div>
	<?php } ?>
</div>
<?php }else{ ?>
<div class="message info empty"><div><?php echo __('We can\'t find club matching the selection.'); ?></div></div>
<?php } ?>