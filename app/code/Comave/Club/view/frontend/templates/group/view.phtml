<?php
$_clubCollection = $block->getCollection();
$show_club_name = $this->getConfig('group_page/show_club_name');
// Boostrap Carousel
$lg_column_item = (int)$this->getConfig('group_page/lg_column_item',6);
$md_column_item = (int)$this->getConfig('group_page/md_column_item',6);
$sm_column_item = (int)$this->getConfig('group_page/sm_column_item',4);
$xs_column_item = (int)$this->getConfig('group_page/xs_column_item',2);

$lg_column = 12/$lg_column_item;
$md_column = 12/$md_column_item;
$sm_column = 12/$sm_column_item;
$xs_column = 12/$xs_column_item;
$i = 1;
$x = 0;
?>
<?php if ( $_clubCollection->count() ){ ?>
<?php $total = $_clubCollection->getSize(); ?>
<div class="clublist">
	<div class="block-content">
		<?php foreach ($_clubCollection as $_club) { ?>
		<?php if( $lg_column_item == 1 || $x%$lg_column_item == 0){ ?>
		<div class="row">
			<?php } ?>
			<div class="col-lg-<?php echo $lg_column ?> col-md-<?php echo $md_column ?> col-sm-<?php echo $sm_column ?> col-xs-<?php echo $xs_column ?>">
				<div class="club-item">
					<div class="club-image"><a href="<?php echo $_club->getUrl(); ?>" title="<?php echo $_club->getName(); ?>"><img src="<?php echo $_club->getClogoUrl(); ?>"/></a></div>
					<?php if($show_club_name){ ?>
					<div class="club-name"><a href="<?php echo $_club->getUrl(); ?>" title="<?php echo $_club->getName(); ?>"><?php echo $_club->getName() ?></a></div>
					<?php } ?>
				</div>
			</div>
			<?php if( $lg_column_item == 1 || ($x+1)%$lg_column_item == 0 || $i == $total ) {  ?>
		</div>
		<?php } ?>
		<?php $i++; $x++; ?>
		<?php } ?>
	</div>
	<div class="club-toolbar">
		<?php echo $block->getChildHtml('group-toolbar'); ?>
	</div>
</div>
<?php }else{ ?>
<div class="message info empty"><div><?php echo __('We can\'t find club matching the selection.'); ?></div></div>
<?php } ?>