<?php
$_clubCollection       = $block->getClubCollection();
$_helper                = $this->helper('Comave\Club\Helper\Data');
$title                  = $this->getConfig('widget_title');
$show_club_name        = $this->getConfig('show_club_name');

$blockId                = rand().time();
$number_item_per_column    = (int)$this->getConfig('number_item_per_column');
if($number_item_per_column == '' || $number_item_per_column == 0)
	$number_item_per_column = 1;

$default_items          = $this->getConfig('default_items');
$addition_class         = $this->getConfig('addition_class');
$mobile_items           = (int)$this->getConfig('mobile_items',1);
$tablet_small_items     = (int)$this->getConfig('tablet_small_items',2);
$tablet_items           = (int)$this->getConfig('tablet_items',3);
$portrait_items         = (int)$this->getConfig('portrait_items',4);
$large_items            = (int)$this->getConfig('large_items',5);
$dots                   = $this->getConfig('dots');
$nav                    = $this->getConfig('nav');
$nav_prev               = $this->getConfig('nav_prev');
$nav_next               = $this->getConfig('nav_next');
$loop                   = $this->getConfig('loop');
$autoplay               = $this->getConfig('autoplay');
$autoplay_timeout       = (int)$this->getConfig('autoplay_timeout',3000);
$autoplay_pauseonhover  = (int)$this->getConfig('autoplay_pauseonhover');
$auto_play              = $this->getConfig('auto_play');
$speed                  = $this->getConfig('speed');
$rtl                    = $this->getConfig('rtl');

$html = '';
$cmsblock = $this->getConfig('cmsblock');
$pretext_html = $this->getConfig('pretext_html');
if($cmsblock === 'pretext_html' && $pretext_html){
	$html = base64_decode($pretext_html);
}elseif($cmsblock!=''){
	$html = $this->getCmsBlockModel()->load($cmsblock)->getContent();
}
$html = $_helper->filter($html);
?>
<?php if ( $_clubCollection->count() ){ ?>
<?php
$_collection = [];
$i = $x = 0;
$total  = $_clubCollection->count();
$column = 8;
if($total%$number_item_per_column == 0){
	$column = $total/$number_item_per_column;
}else{
	$column = floor($total/$number_item_per_column)+1;
}
if($column<$default_items) $column = $default_items;
$i = $x = 0;
foreach ($_clubCollection as $_club) {
	if($i<$column){
		$i++;
	}else{
		$i = 1;
		$x++;
	}
	$_collection[$i][$x] = $_club;
} 
?>
<div class="block widget coditron-widget club-widget <?php echo $addition_class?$addition_class:'' ?>">
	<?php if($title){ ?>
	<div class="block-title"><strong><?php echo $title ?></strong></div>
	<?php } ?>
	<?php if($html!=''){ ?>
	<div class="pretext-html"><?php echo $html ?></div>
	<?php } ?>
	<div class="block-content clubowl-play">
		<ul id="comave-carousel<?php echo $blockId ?>" class="owl-carousel comave-carousel<?php echo $blockId ?>" data-rtl="<?php echo $rtl?"true":"false" ?>" data-loop="<?php echo $loop?"true":"false" ?>" data-nav="false" data-dot="<?php echo $dots?"true":"false" ?>" data-autoplay="<?php echo $autoplay?"true":"false" ?>" data-autoplay-timeout="<?php echo $autoplay_timeout ?>" data-pauonhover="<?php echo $autoplay_pauseonhover?"true":"false" ?>" data-mobile-items="<?php echo (int)$mobile_items ?>" data-tablet-small-items="<?php echo (int)$tablet_small_items ?>" data-tablet-items="<?php echo (int)$tablet_items ?>" data-portrait-items="<?php echo (int)$portrait_items ?>" data-large-items="<?php echo (int)$default_items ?>" data-large-max-items="<?php echo (int)$large_items ?>" >
			<?php foreach ($_collection as $_clubCollection) { ?>
			<li class="item">
				<?php foreach ($_clubCollection as $_club) { ?>
				<div class="club-item">
					<div class="club-image"><a href="<?php echo $_club->getUrl(); ?>" title="<?php echo $_club->getName(); ?>"><img src="<?php echo $_club->getClogoUrl(); ?>" alt="<?php echo $_club->getName(); ?>"/></a></div>
					<?php if($show_club_name){ ?>
					<div class="club-name"><a href="<?php echo $_club->getUrl(); ?>" title="<?php echo $_club->getName(); ?>"><?php echo $_club->getName() ?></a></div>
					<?php } ?>
				</div>
				<?php } ?>
			</li>
			<?php } ?>
		</ul>
		<?php if($nav){ ?>
		<a href="#" class="owl-left"><?php echo $nav_prev?$nav_prev:'Prev'; ?></a>
		<a href="#" class="owl-right"><?php echo $nav_next?$nav_next:'Next'; ?></a>
		<?php } ?>
	</div>
</div>
<?php } ?>
<script type="text/javascript">
	require(['Comave_Club/js/club']);
</script>