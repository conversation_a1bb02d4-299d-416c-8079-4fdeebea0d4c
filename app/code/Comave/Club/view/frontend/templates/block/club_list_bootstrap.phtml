<?php
$_clubCollection = $block->getClubCollection();
$_helper = $this->helper('Comave\Club\Helper\Data');
$title = $this->getConfig('club_block/title');
$blockId = rand().time();

// Boostrap Carousel
$itemsperpage = (int)$this->getConfig('club_block/item_per_page',6);
$lg_column_item = (int)$this->getConfig('club_block/lg_column_item',6);
$md_column_item = (int)$this->getConfig('club_block/md_column_item',6);
$sm_column_item = (int)$this->getConfig('club_block/sm_column_item',6);
$xs_column_item = (int)$this->getConfig('club_block/xs_column_item',6);

$lg_column = 12/$lg_column_item;
$md_column = 12/$md_column_item;
$sm_column = 12/$sm_column_item;
$xs_column = 12/$xs_column_item;

$show_club_name = $this->getConfig('club_block/show_club_name');
$addition_class = $this->getConfig('club_block/addition_class');
$interval = $this->getConfig('club_block/interval',1000);
$auto_play = $this->getConfig('club_block/auto_play');
$speed = $this->getConfig('club_block/speed');
$dots = $this->getConfig('club_block/dots',true);
$nav = $this->getConfig('club_block/nav',true);
$nav_prev = $this->getConfig('club_block/nav_prev');
$nav_next = $this->getConfig('club_block/nav_next');

?>
<?php if ( $_clubCollection->count() ){ ?>
<?php
$total = $_clubCollection->getSize();
$totalPage = '';
if($total%$itemsperpage == 0){
	$totalPage = $total/$itemsperpage;
}else{
	$totalPage = floor($total/$itemsperpage)+1;
}
$pretext                = $_helper->filter($this->getConfig('club_block/pretext'));
?>
<div class="block widget coditron-widget club-widget <?php echo $addition_class?$addition_class:'' ?>">
	<?php if($title){ ?>
	<div class="block-title">
		<strong><?php echo $title ?></strong>
	</div>
	<?php } ?>
	<?php if($pretext!=''){ ?>
	<div class="pretext-html"><?php echo $pretext ?></div>
	<?php } ?>
	<div id="productcarousel-<?php echo $blockId ?>" class="block-content carousel slide" data-ride="carousel" data-interval="<?php echo $interval?$interval:"false"; ?>">
		<!-- Indicators -->
		<?php if($dots && $totalPage>0){ ?>
		<ol class="carousel-indicators">
			<?php for ($i=0; $i < $totalPage ; $i++) { ?>
			<li data-target="#productcarousel-<?php echo $blockId ?>" data-slide-to="<?php echo $i ?>"></li>
			<?php } ?>
		</ol>
		<?php } ?>
		<!-- Wrapper for slides -->
		<div class="carousel-inner" role="listbox">
			<?php
			$i = 1;
			$x = 0;
			?>
			<?php foreach ($_clubCollection as $_club) { ?>
			<?php if( $itemsperpage == 1 || $i % $itemsperpage == 1){ ?>
			<div class="item <?php if($i==1){ ?>active<?php } ?>">
				<?php } ?>
				<?php if( $lg_column_item == 1 || $x%$lg_column_item == 0 || $x%$itemsperpage == 0){ ?>
				<div class="row">
					<?php } ?>
					<div class="col-lg-<?php echo $lg_column ?> col-md-<?php echo $md_column ?> col-sm-<?php echo $sm_column ?> col-xs-<?php echo $xs_column ?>">
						<div class="club-item">
							<div class="club-image"><a href="<?php echo $_club->getUrl(); ?>" title="<?php echo $_club->getName(); ?>"><img src="<?php echo $_club->getClogoUrl(); ?>" alt="<?php echo $_club->getName(); ?>"/></a></div>
							<?php if($show_club_name){ ?>
							<div class="club-name"><a href="<?php echo $_club->getUrl(); ?>" title="<?php echo $_club->getName(); ?>"><?php echo $_club->getName() ?></a></div>
							<?php } ?>
						</div>
					</div>
					<?php if( $lg_column_item == 1 || ($x+1)%$lg_column_item == 0 || $i == $total || ($x>0 && ($x+1)%$itemsperpage == 0) ) {  ?>
				</div>
				<?php } ?>
				<?php if( $itemsperpage == 1 || ($i+1)%$itemsperpage == 1 || $i == $total ) { $x = -1; ?>
			</div>
			<?php } ?>
			<?php $i++; $x++; ?>
			<?php } ?>
		</div>
		<?php if($nav && $total>$xs_column_item){ ?>
		<div class="carousel-controls">
			<a class="left carousel-control" href="#productcarousel-<?php echo $blockId ?>" role="button" data-slide="prev"><?php echo $nav_prev?$nav_prev:'Previous'); ?></a>
			<a class="right carousel-control" href="#productcarousel-<?php echo $blockId ?>" role="button" data-slide="next"><?php echo $nav_next?$nav_next:'Next'; ?></a>
		</div>
		<?php } ?>
	</div>
</div>
<?php } ?>