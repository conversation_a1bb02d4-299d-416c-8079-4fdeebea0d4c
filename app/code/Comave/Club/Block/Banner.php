<?php
declare(strict_types=1);

namespace Comave\Club\Block;

use Magento\Framework\View\Element\Template;
use Magento\Customer\Model\Session;
use Comave\Club\Helper\Data as ClubHelper;
use Comave\Club\Model\ClubFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;

class Banner extends Template
{
    protected $customerSession;
    private $clubFactory;
    protected $clubHelper;

    public function __construct(
        ClubFactory                                                      $clubFactory,
        Template\Context                                                 $context,
        Session                                                          $customerSession,
        \Magento\Framework\App\Http\Context                              $httpContext,
        ClubHelper                                                       $clubHelper,
        CustomerRepositoryInterface                                      $customerRepositoryInterface,
        \Magento\Store\Model\StoreManagerInterface                       $storeManager,
        ScopeConfigInterface                                             $scopeConfig,
        \Magento\Framework\App\Request\Http                              $request,
        \Magento\Store\Api\WebsiteRepositoryInterface                    $websiteRepository,
        \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory $customerFactory,
        array                                                            $data = []
    )
    {
        parent::__construct($context, $data);
        $this->customerSession = $customerSession;
        $this->httpContext = $httpContext;
        $this->clubFactory = $clubFactory;
        $this->clubHelper = $clubHelper;
        $this->_customerRepositoryInterface = $customerRepositoryInterface;
        $this->_storeManager = $storeManager;
        $this->scopeConfig = $scopeConfig;
        $this->request = $request;
        $this->websiteRepository = $websiteRepository;
        $this->customerFactory = $customerFactory;
    }

    public function isCustomerLoggedIn()
    {
        return (bool)$this->httpContext->getValue(\Magento\Customer\Model\Context::CONTEXT_AUTH);
    }

    public function getCustId()
    {
        return $this->httpContext->getValue('customer_id');
    }

    public function getMediaUrl(): string
    {
        return $this->_storeManager
            ->getStore()
            ->getBaseUrl(
                \Magento\Framework\UrlInterface::URL_TYPE_MEDIA
            );
    }

    public function getClubData()
    {
        $getCurrentClubId = $this->getCustomerClubId();
        if ($getCurrentClubId) {
            //$] = $this->clubFactory->create()->getCollection()->addFieldToFilter('uniqueid', $getCurrentClubId);
            $customerCollection = $this->customerFactory->create()
                ->addAttributeToSelect(['wkv_club_unique_identfier', 'wkv_club_name', 'wkv_club_subtitles', 'wkv_club_first_banner', 'wkv_club_second_banner', 'wkv_clubbanner', 'wkv_club_third_banner', 'wkv_club_first_color', 'wkv_club_second_color', 'wkv_club_third_color', 'wkv_club_logo', 'wkv_club_watermark_image'])
                ->addAttributeToFilter('wkv_club_unique_identfier', $getCurrentClubId);

            return $customerCollection;

        } elseif ($this->cameFromCampion()) {
            $isFromCampion = $this->cameFromCampion();

            //$groupCollection = $this->clubFactory->create()->getCollection()->addFieldToFilter('uniqueid', $isFromCampion);

            $customerCollection = $this->customerFactory->create()
                ->addAttributeToSelect(['wkv_club_unique_identfier', 'wkv_club_name', 'wkv_club_subtitles', 'wkv_club_first_banner', 'wkv_club_second_banner', 'wkv_clubbanner', 'wkv_club_third_banner', 'wkv_club_first_color', 'wkv_club_second_color', 'wkv_club_third_color', 'wkv_club_logo', 'wkv_club_watermark_image'])
                ->addAttributeToFilter('wkv_club_unique_identfier', $isFromCampion);

            return $customerCollection;
        } else {
            $isGuest = '';

            $groupCollection = $this->clubFactory->create()->getCollection()
                ->addFieldToFilter('uniqueid', $isGuest);

            return $groupCollection;
        }
    }

    private function getCustomerClubId()
    {
        $clubId = false;
        $paramData = $this->getRequest()->getParams();

        if ($this->isCustomerLoggedIn()) {
            $customerId = $this->getCustId();
            if (empty($customerId)) {
                return '';
            }

            $currentCustomer = $this->_customerRepositoryInterface->getById($customerId);

            if ($currentCustomer) {
                if ($currentCustomer->getCustomAttribute('customerclub')) {
                    $clubId = $currentCustomer->getCustomAttribute('customerclub')->getValue();
                    return $clubId;
                } elseif (isset($paramData['clubid'])) {
                    return $paramData['clubid'];
                }
            }
        } elseif (isset($paramData['clubid'])) {
            return $paramData['clubid'];
        }

        return $clubId;
    }

    private function cameFromCampion()
    {
        $clubCode = false;
        if ($this->clubHelper->getCookieValue()) {
            $clubCode = $this->clubHelper->getCookieValue();
            return $clubCode;
        }

        return $clubCode;
    }

    public function getActionName()
    {
        return $this->request->getFullActionName();
    }

    public function getWebName()
    {
        $currentWebsiteId = $this->_storeManager->getStore()->getWebsiteId();

        $website = $this->websiteRepository->getById($currentWebsiteId);
        $websiteName = $website->getName();

        return $websiteName;
    }
}
