<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Block\Widget;

class AbstractWidget extends \Magento\Framework\View\Element\Template implements \Magento\Widget\Block\BlockInterface
{
	/**
	 * @var \Comave\Club\Helper\Data
	 */
	protected $_clubHelper;

	/**
     * @param \Magento\Framework\View\Element\Template\Context $context     
     * @param \Comave\Club\Helper\Data                           $clubHelper
     * @param array                                            $data        
     */
	public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Comave\Club\Helper\Data $clubHelper,
        array $data = []
        ) {
        $this->_clubHelper = $clubHelper;
        parent::__construct($context, $data);
    }

    public function getConfig($key, $default = '')
    {
        if($this->hasData($key))
        {
            return $this->getData($key);
        }
        return $default;
    }
}