<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Block\Widget;

class Clublist extends AbstractWidget
{
    /**
     * Group Collection
     */
    protected $_clubCollection;

	/**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @var \Magento\Catalog\Helper\Category
     */
    protected $_clubHelper;

    /**
     * @var \Magento\Cms\Model\Block
     */
    protected $_blockModel;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context         
     * @param \Magento\Framework\Registry                      $registry        
     * @param \Comave\Club\Helper\Data                           $clubHelper
     * @param \Comave\Club\Model\Club                           $clubCollection
     * @param array                                            $data            
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Comave\Club\Helper\Data $clubHelper,
        \Comave\Club\Model\Club $clubCollection,
        \Magento\Cms\Model\Block $blockModel,
        array $data = []
        ) {
        $this->_clubCollection = $clubCollection;
        $this->_clubHelper = $clubHelper;
        $this->_coreRegistry = $registry;
        $this->_blockModel = $blockModel;
        parent::__construct($context, $clubHelper);
    }

    public function getCmsBlockModel(){
        return $this->_blockModel;
    }

    public function _toHtml()
    {
        if(!$this->_clubHelper->getConfig('general_settings/enable')) return;
        $carousel_layout = $this->getConfig('carousel_layout');
        if($carousel_layout == 'owl_carousel'){
            $this->setTemplate('widget/club_list_owl.phtml');
        }else{
            $this->setTemplate('widget/club_list_bootstrap.phtml');
        }
        if(($template = $this->getConfig('template')) != ''){
            $this->setTemplate($template);
        }
        return parent::_toHtml();
    }

    public function getClubCollection()
    {
        $number_item = $this->getConfig('number_item',12);
        $clubGroups = $this->getConfig('club_groups');
        $store = $this->_storeManager->getStore();
        $collection = $this->_clubCollection->getCollection()
        ->addFieldToFilter('status',1)
        ->addStoreFilter($store, false);

        $clubGroups = explode(',', $clubGroups);
        if(is_array($clubGroups))
        {
            $collection->addFieldToFilter('group_id',array('in' => $clubGroups));
        }
        $collection->setPageSize($number_item)
        ->setCurPage(1)
        ->setOrder('position','ASC');
        return $collection;
    }
}