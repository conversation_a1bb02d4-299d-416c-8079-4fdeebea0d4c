<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Block;

class GroupList extends \Magento\Framework\View\Element\Template
{
    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @var \Comave\Club\Helper\Data
     */
    protected $_clubHelper;

    /**
     * @var \Comave\Club\Model\Club
     */
    protected $_group;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context      
     * @param \Magento\Framework\Registry                      $registry     
     * @param \Comave\Club\Helper\Data                           $clubHelper  
     * @param \Comave\Club\Model\Group                           $group        
     * @param \Magento\Store\Model\StoreManagerInterface       $storeManager 
     * @param array                                            $data         
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Comave\Club\Helper\Data $clubHelper,
        \Comave\Club\Model\Group $group,
        array $data = []
        ) { 
        $this->_group = $group;
        $this->_coreRegistry = $registry;
        $this->_clubHelper = $clubHelper;
        parent::__construct($context, $data);
    }

    public function _construct()
    {
        if(!$this->getConfig('general_settings/enable')) return;
        if(!$this->getConfig('general_settings/enable_menu')) return;
        parent::_construct();
    }

    public function getGroupList(){
        $collection = $this->_group->getCollection()
        ->addFieldToFilter('status',1)
        ->addFieldToFilter('shown_in_sidebar',1)
        ->setOrder('position','ASC');
        return $collection;
    }
    public function getConfig($key, $default = '')
    {
        $result = $this->_clubHelper->getConfig($key);
        if(!$result){

            return $default;
        }
        return $result;
    }
}