<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Block\Product;

use Magento\Customer\Model\Context as CustomerContext;

class View extends \Magento\Framework\View\Element\Template
{
    /**
     * Group Collection
     */
    protected $_clubCollection;

    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @var \Magento\Catalog\Helper\Category
     */
    protected $_clubHelper;

    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    protected $_resource;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context         
     * @param \Magento\Framework\Registry                      $registry        
     * @param \Comave\Club\Helper\Data                           $clubHelper
     * @param \Comave\Club\Model\Club                           $clubCollection
     * @param array                                            $data            
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Comave\Club\Helper\Data $clubHelper,
        \Comave\Club\Model\Club $clubCollection,
        \Magento\Framework\App\ResourceConnection $resource,
        array $data = []
        ) {
        $this->_clubCollection = $clubCollection;
        $this->_clubHelper = $clubHelper;
        $this->_coreRegistry = $registry;
        $this->_resource = $resource;
        parent::__construct($context, $data); 
    }

    /**
     * Retrieve current product model
     *
     * @return \Magento\Catalog\Model\Product
     */
    public function getProduct()
    {
        return $this->_coreRegistry->registry('current_product');
    }

    public function getClubCollection(){
        $product = $this->getProduct();
        $connection = $this->_resource->getConnection();
        $table_name = $this->_resource->getTableName('comave_club_product');
        $clubIds = $connection->fetchCol(" SELECT club_id FROM ".$table_name." WHERE product_id = ".$product->getId());
        if($clubIds || count($clubIds) > 0) {
            $collection = $this->_clubCollection->getCollection()
                ->setOrder('position','ASC')
                ->addFieldToFilter('status',1);
            $collection->getSelect()->where('club_id IN (?)', $clubIds);
            return $collection;
        }
        return false;
    }

    public function _toHtml(){
        if(!$this->_clubHelper->getConfig('product_view_page/enable_club_info')) return;

        return parent::_toHtml();
    }

}