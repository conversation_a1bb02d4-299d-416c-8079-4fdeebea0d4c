<?php
declare(strict_types=1);

namespace Comave\Club\Block\Club;

use Comave\Club\Api\ClubRepositoryInterface;
use Comave\Club\Api\Data\ClubInterface;
use Comave\Club\Model\ConfigProvider;
use Comave\Club\Model\Group;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Model\StoreManagerInterface;

class View extends Template
{
    public function __construct(
        Context $context,
        private readonly Registry $coreRegistry,
        private readonly Group $groupModel,
        private readonly StoreManagerInterface $storeManager,
        private readonly ConfigProvider $configProvider,
        private readonly ClubRepositoryInterface $clubRepository,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * Prepare breadcrumbs
     *
     * @return void
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function _addBreadcrumbs()
    {
        $breadcrumbsBlock = $this->getLayout()->getBlock('breadcrumbs');
        $baseUrl = $this->storeManager->getStore()->getBaseUrl();
        $storeId = (int)$this->storeManager->getStore()->getId();
        $clubRoute = $this->configProvider->getClubUrl($storeId);
        $clubRoute = $clubRoute ? $clubRoute : "comave_club/index/index";
        $page_title = $this->configProvider->getClubPageTitle($storeId);
        $club = $this->getCurrentClub();

        $group = false;
        if ($groupId = $club->getGroupId()) {
            $group = $this->groupModel->load($groupId);
        }
        if ($breadcrumbsBlock) {
            $breadcrumbsBlock->addCrumb(
                'home',
                [
                    'label' => __('Home'),
                    'title' => __('Go to Home Page'),
                    'link' => $baseUrl,
                ]
            );

            $breadcrumbsBlock->addCrumb(
                'comave_club',
                [
                    'label' => $page_title,
                    'title' => $page_title,
                    'link' => $baseUrl.$clubRoute,
                ]
            );

            if ($group && $group->getStatus()) {
                $breadcrumbsBlock->addCrumb(
                    'group',
                    [
                        'label' => $group->getName(),
                        'title' => $group->getName(),
                        'link' => $group->getUrl(),
                    ]
                );
            }

            $breadcrumbsBlock->addCrumb(
                'club',
                [
                    'label' => $club->getName(),
                    'title' => $club->getName(),
                    'link' => '',
                ]
            );
        }
    }

    public function getCurrentClub(): ?ClubInterface
    {
        // @TODO: remove this later
        $club = $this->coreRegistry->registry('current_club');
        if ($club) {
            $this->setData('current_club', $club);
        }

        if (empty($club)) {
            $clubId = $this->getRequest()->getParam('id');
            try {
                $club = $this->clubRepository->get((int)$clubId);
            } catch (NoSuchEntityException $e) {
                $club = null;
            }
        }

        return $club;
    }

    public function getProductListHtml(): string
    {
        return $this->getChildHtml('product_list');
    }

    /**
     * @return View
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function _prepareLayout()
    {
        $this->_addBreadcrumbs();

        return parent::_prepareLayout();
    }
}
