<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Block;

class Clubpage extends \Magento\Framework\View\Element\Template
{
    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @var \Comave\Club\Helper\Data
     */
    protected $_clubHelper;

    /**
     * @var \Comave\Club\Model\Club
     */
    protected $_club;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context      
     * @param \Magento\Framework\Registry                      $registry     
     * @param \Comave\Club\Helper\Data                           $clubHelper  
     * @param \Comave\Club\Model\Club                           $club        
     * @param \Magento\Store\Model\StoreManagerInterface       $storeManager 
     * @param array                                            $data         
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Comave\Club\Helper\Data $clubHelper,
        \Comave\Club\Model\Club $club,
        array $data = []
        ) {
        $this->_club = $club;
        $this->_coreRegistry = $registry;
        $this->_clubHelper = $clubHelper;
        parent::__construct($context, $data);
    }

    public function _construct()
    {
        if(!$this->getConfig('general_settings/enable')) return;
        parent::_construct();

        $store = $this->_storeManager->getStore();
        $itemsperpage = (int)$this->getConfig('club_list_page/item_per_page',12);
        $club = $this->_club;
        $clubCollection = $club->getCollection()
        ->addFieldToFilter('status',1)
        ->addStoreFilter($store)
        ->setOrder('position','ASC');
        $this->setCollection($clubCollection);

        $template = '';
        $layout = $this->getConfig('club_list_page/layout');
        if($layout == 'grid'){
            $template = 'clublistpage_grid.phtml';
        }else{
            $template = 'clublistpage_list.phtml';
        }
        if(!$this->hasData('template')){
            $this->setTemplate($template);
        }
    }

	/**
     * Prepare breadcrumbs
     *
     * @param \Magento\Cms\Model\Page $club
     * @throws \Magento\Framework\Exception\LocalizedException
     * @return void
     */
    protected function _addBreadcrumbs()
    {
        $breadcrumbsBlock = $this->getLayout()->getBlock('breadcrumbs');
        $baseUrl = $this->_storeManager->getStore()->getBaseUrl();
        $clubRoute = $this->_clubHelper->getConfig('general_settings/route');
        $page_title = $this->_clubHelper->getConfig('club_list_page/page_title');

        if($breadcrumbsBlock){

        $breadcrumbsBlock->addCrumb(
            'home',
            [
            'label' => __('Home'),
            'title' => __('Go to Home Page'),
            'link' => $baseUrl
            ]
            );
        $breadcrumbsBlock->addCrumb(
            'comave_club',
            [
            'label' => $page_title,
            'title' => $page_title,
            'link' => ''
            ]
            );
        }
    }

    /**
     * Set club collection
     * @param \Comave\Club\Model\Club
     */
    public function setCollection($collection)
    {
        $this->_collection = $collection;
        return $this->_collection;
    }

    /**
     * Retrive club collection
     * @param \Comave\Club\Model\Club
     */
    public function getCollection()
    {
        $this->_collection->getSelect()->reset(\Magento\Framework\DB\Select::ORDER);
        $this->_collection->setOrder('position','ASC');
        return $this->_collection;
    }

    public function getConfig($key, $default = '')
    {
        $result = $this->_clubHelper->getConfig($key);
        if(!$result){

            return $default;
        }
        return $result;
    }

    /**
     * Prepare global layout
     *
     * @return $this
     */
    protected function _prepareLayout()
    {
        $page_title = $this->getConfig('club_list_page/page_title');
        $meta_description = $this->getConfig('club_list_page/meta_description');
        $meta_keywords = $this->getConfig('club_list_page/meta_keywords');
        $this->_addBreadcrumbs();
        $this->pageConfig->addBodyClass('comave-clublist');
        if($page_title){
            $this->pageConfig->getTitle()->set($page_title);   
        }
        if($meta_keywords){
            $this->pageConfig->setKeywords($meta_keywords);   
        }
        if($meta_description){
            $this->pageConfig->setDescription($meta_description);   
        }
        return parent::_prepareLayout();
    }

    /**
     * Retrieve Toolbar block
     *
     * @return \Magento\Catalog\Block\Product\ProductList\Toolbar
     */
    public function getToolbarBlock()
    {
        $block = $this->getLayout()->getBlock('comave_club_toolbar');
        if ($block) {
            $block->setDefaultOrder("position");
            $block->removeOrderFromAvailableOrders("price");
            return $block;
        }
    }

    /**
     * Need use as _prepareLayout - but problem in declaring collection from
     * another block (was problem with search result)
     * @return $this
     */
    protected function _beforeToHtml()
    {
        $collection = $this->getCollection();
        $toolbar = $this->getToolbarBlock();

        // set collection to toolbar and apply sort
        if($toolbar){
            $itemsperpage = (int)$this->getConfig('club_list_page/item_per_page',12);
            $toolbar->setData('_current_limit',$itemsperpage)->setCollection($collection);
            $this->setChild('toolbar', $toolbar);
        }
        return parent::_beforeToHtml();
    }
}