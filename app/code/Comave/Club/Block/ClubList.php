<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Block;
use Magento\Customer\Model\Context as CustomerContext;

class ClubList extends \Magento\Framework\View\Element\Template
{
    /**
     * Group Collection
     */
    protected $_clubCollection;

    protected $_collection = null;

	/**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @var \Magento\Catalog\Helper\Category
     */
    protected $_clubHelper;

    /**
     * @var \Magento\Framework\App\Http\Context
     */
    protected $httpContext;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context         
     * @param \Magento\Framework\Registry                      $registry        
     * @param \Comave\Club\Helper\Data                           $clubHelper     
     * @param \Comave\Club\Model\Club                           $clubCollection 
     * @param array                                            $data            
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Comave\Club\Helper\Data $clubHelper,
        \Comave\Club\Model\Club $clubCollection,
        \Magento\Framework\App\Http\Context $httpContext,
        array $data = []
        ) {
        $this->_clubCollection = $clubCollection;
        $this->_clubHelper = $clubHelper;
        $this->_coreRegistry = $registry;
        $this->httpContext = $httpContext;
        parent::__construct($context, $data);
    }

    public function _construct(){
        if(!$this->getConfig('general_settings/enable') || !$this->getConfig('club_block/enable')) return;
        parent::_construct();
        $carousel_layout = $this->getConfig('club_block/carousel_layout');
        $template = '';
        if($carousel_layout == 'owl_carousel'){
            $template = 'block/club_list_owl.phtml';
        }else{
            $template = 'block/club_list_bootstrap.phtml';
        }
        if(!$this->getTemplate() && $template!=''){
            $this->setTemplate($template);
        }
    }

    public function getConfig($key, $default = '')
    {   
        $widget_key = explode('/', $key);
        if( (count($widget_key)==2) && ($resultData = $this->hasData($widget_key[1])) )
        {
            return $this->getData($widget_key[1]);
        }
        $result = $this->_clubHelper->getConfig($key);
        if($result == ""){
            return $default;
        }
        return $result;
    }

    public function getClubCollection()
    {
        if(!$this->_collection) {
            $number_item = $this->getConfig('club_block/number_item');
            $clubGroups = $this->getConfig('club_block/club_groups');
            $store = $this->_storeManager->getStore();
            $collection = $this->_clubCollection->getCollection()
            ->setOrder('position','ASC')
            ->addStoreFilter($store)
            ->addFieldToFilter('status',1);
            $clubGroups = explode(',', $clubGroups);
            if(is_array($clubGroups) && count($clubGroups)>0)
            {
                $collection->addFieldToFilter('group_id',array('in' => $clubGroups));
            }
            $collection->setPageSize($number_item)
            ->setCurPage(1)
            ->setOrder('position','ASC');
            $this->_collection = $collection;
        }
        return $this->_collection;
    }


    /**
     * Get Key pieces for caching block content
     *
     * @return array
     */
    public function getCacheKeyInfo()
    {
        return [
        'MAGETOP_BRAND_LIST',
        $this->_storeManager->getStore()->getId(),
        $this->_design->getDesignTheme()->getId(),
        $this->httpContext->getValue(CustomerContext::CONTEXT_GROUP),
        'template' => $this->getTemplate(),
        $this->getProductsCount()
        ];
    }

    public function _toHtml()
    {
        return parent::_toHtml();
    }
}