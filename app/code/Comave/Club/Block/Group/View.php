<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Block\Group;

class View extends \Magento\Framework\View\Element\Template
{
    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @var \Comave\Club\Helper\Data
     */
    protected $_clubHelper;

    /**
     * @var \Comave\Club\Model\Club
     */
    protected $_club;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context      
     * @param \Magento\Framework\Registry                      $registry     
     * @param \Comave\Club\Helper\Data                           $clubHelper  
     * @param \Comave\Club\Model\Club                           $club        
     * @param \Magento\Store\Model\StoreManagerInterface       $storeManager 
     * @param \Comave\Club\Helper\Data                           $clubHelper  
     * @param array                                            $data         
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Comave\Club\Model\Club $club,
        \Comave\Club\Helper\Data $clubHelper,
        array $data = []
        ) {
        $this->_club = $club;
        $this->_coreRegistry = $registry;
        $this->_clubHelper = $clubHelper;
        parent::__construct($context, $data);
    }

    public function _construct()
    {
        parent::_construct();
        $club = $this->_club;
        $group = $this->getCurrentGroup();
        $clubCollection = $club->getCollection()
        ->addFieldToFilter('group_id',$group->getId())
        ->addFieldToFilter('status',1)
        ->setOrder('position','ASC');
        $this->setCollection($clubCollection);
        $template = 'group/view.phtml';
        if(!$this->hasData('template')){
            $this->setTemplate($template);
        }
    }

    public function getCurrentGroup()
    {
        $group = $this->_coreRegistry->registry('current_group_club');
        if ($group) {
            $this->setData('current_group_club', $group);
        }
        return $group;
    }

	/**
     * Prepare breadcrumbs
     *
     * @return void
     */
    protected function _addBreadcrumbs()
    {
        $breadcrumbsBlock = $this->getLayout()->getBlock('breadcrumbs');
        $baseUrl = $this->_storeManager->getStore()->getBaseUrl();
        $group = $this->getCurrentGroup();
        $baseUrl = $this->_storeManager->getStore()->getBaseUrl();
        $clubRoute = $this->_clubHelper->getConfig('general_settings/route');
        $page_title = $this->_clubHelper->getConfig('club_list_page/page_title');

        if($breadcrumbsBlock){
        $breadcrumbsBlock->addCrumb(
            'home',
            [
                'label' => __('Home'),
                'title' => __('Go to Home Page'),
                'link' => $baseUrl
            ]
            );

        $breadcrumbsBlock->addCrumb(
            'comave_club',
            [
                'label' => $page_title,
                'title' => $page_title,
                'link' => $baseUrl.$clubRoute
            ]
            );

        $breadcrumbsBlock->addCrumb(
            'club',
            [
                'label' => $group->getName(),
                'title' => $group->getName(),
                'link' => ''
            ]
            );
        }
    }

    /**
     * Set club collection
     * @param \Comave\Club\Model\Club
     */
    public function setCollection($collection)
    {
        $this->_collection = $collection;
        return $this->_collection;
    }

    /**
     * Retrive club collection
     * @param \Comave\Club\Model\Club
     */
    public function getCollection()
    {
        return $this->_collection;
    }

    public function getConfig($key, $default = '')
    {
        $result = $this->_clubHelper->getConfig($key);
        if(!$result){
            return $default;
        }
        return $result;
    }

    /**
     * Prepare global layout
     *
     * @return $this
     */
    protected function _prepareLayout()
    {
        $this->_addBreadcrumbs();
        $this->pageConfig->addBodyClass('comave-clublist');
        $group = $this->getCurrentGroup();
        $page_title = $group->getName();
        if($page_title){
            $this->pageConfig->getTitle()->set($page_title);
            $this->pageConfig->setKeywords($page_title);
            $this->pageConfig->setDescription($page_title); 
        }
        return parent::_prepareLayout();
    }

    /**
     * Retrieve Toolbar block
     *
     * @return \Magento\Catalog\Block\Product\ProductList\Toolbar
     */
    public function getToolbarBlock()
    {
        $block = $this->getLayout()->getBlock('comave_club_toolbar');
        if ($block) {
            $block->setDefaultOrder("position");
            $block->removeOrderFromAvailableOrders("price");
            return $block;
        }
    }

    /**
     * Need use as _prepareLayout - but problem in declaring collection from
     * another block (was problem with search result)
     * @return $this
     */
    protected function _beforeToHtml()
    {
        $collection = $this->getCollection();
        $toolbar = $this->getToolbarBlock();

        // set collection to toolbar and apply sort
        if($toolbar){
            $itemsperpage = (int)$this->getConfig('group_page/item_per_page',12);
            $toolbar->setData('_current_limit',$itemsperpage)->setCollection($collection);
            $this->setChild('group-toolbar', $toolbar);
        }
        return parent::_beforeToHtml();
    }
}