<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */

namespace Comave\Club\Block;

use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Webkul\Marketplace\Helper\Data as MpHelper;
use Webkul\Marketplace\Model\ProductFactory as MpProductModel;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory as SellerCollectionFactory;

/**
 * Seller Product's Collection Block.
 */
class Sportclubsellerlist extends \Magento\Catalog\Block\Product\ListProduct
{
    /**
     * @var CollectionFactory
     */
    protected $_productCollectionFactory;

    /**
     * @var \Magento\Catalog\Model\Product
     */
    protected $_productlists;

    /**
     * @var CategoryRepositoryInterface
     */
    protected $_categoryRepository;

    /**
     * @var \Magento\Framework\Stdlib\StringUtils
     */
    protected $stringUtils;

    /**
     * @var MpHelper
     */
    protected $mpHelper;

    /**
     * @var MpProductModel
     */
    protected $mpProductModel;

    protected $productCollectionFactory;

    protected $_sellerlistCollectionFactory;

    protected $sellerCollection;

    protected $sellerList;

    protected $_productType;

    protected $_helperProducts;


    /**
     * @param \Magento\Catalog\Block\Product\Context $context
     * @param \Magento\Framework\Data\Helper\PostHelper $postDataHelper
     * @param \Magento\Framework\Url\Helper\Data $urlHelper
     * @param \Magento\Framework\ObjectManagerInterface $objectManager
     * @param CollectionFactory $productCollectionFactory
     * @param \Magento\Catalog\Model\Layer\Resolver $layerResolver
     * @param CategoryRepositoryInterface $categoryRepository
     * @param \Magento\Framework\Stdlib\StringUtils $stringUtils
     * @param MpHelper $mpHelper
     * @param MpProductModel $mpProductModel
     * @param array $data
     */
    public function __construct(
        \Magento\Catalog\Block\Product\Context $context,
        \Magento\Framework\Data\Helper\PostHelper $postDataHelper,
        \Comave\Club\Helper\Products $helperProducts,
        \Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory $sellerlistCollectionFactory,
        \Magento\Framework\Url\Helper\Data $urlHelper,
        \Magento\Framework\ObjectManagerInterface $objectManager,
        CollectionFactory $_productCollectionFactory,
        SellerCollectionFactory $sellerCollection = null,
        //ProductCollectionFactory $productCollectionFactory,
        \Magento\Catalog\Model\Layer\Resolver $layerResolver,
        CategoryRepositoryInterface $categoryRepository,
        \Magento\Framework\Stdlib\StringUtils $stringUtils = null,
        MpHelper $mpHelper = null,
        MpProductModel $mpProductModel = null,
        \Magento\Framework\App\Http\Context $httpContext,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        array $data = []
    ) {
        $this->_sellerlistCollectionFactory = $sellerlistCollectionFactory;
        $this->_productCollectionFactory = $_productCollectionFactory;
        $this->_helperProducts = $helperProducts;
        //$this->productCollectionFactory = $productCollectionFactory;
        $this->_categoryRepository = $categoryRepository;
        $this->stringUtils = $stringUtils ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(\Magento\Framework\Stdlib\StringUtils::class);
        $this->mpHelper = $mpHelper ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(MpHelper::class);
        $this->mpProductModel = $mpProductModel ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(MpProductModel::class);
        $this->sellerCollection = $sellerCollection ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->create(CollectionFactory::class);
        $this->httpContext = $httpContext;
        $this->_customerRepositoryInterface = $customerRepositoryInterface;
        $this->_customerFactory = $customerFactory;

        parent::__construct(
            $context,
            $postDataHelper,
            $layerResolver,
            $categoryRepository,
            $urlHelper,
            $data
        );
    }

    public function isCustomerLoggedIn()
    {
        return (bool)$this->httpContext->getValue(\Magento\Customer\Model\Context::CONTEXT_AUTH);
    }

    public function getCustId()
    {
        return $this->httpContext->getValue('customer_id');
    }

    // Get current customer associated club identifier

    public function getCustClubIdentifier()
    {
        $clubIdentifier = false;

        if ($this->isCustomerLoggedIn()) {
            $customerId = $this->getCustId();
            $currentCustomer = $this->_customerRepositoryInterface->getById($customerId);

            if ($currentCustomer) {
                if ($currentCustomer->getCustomAttribute('customerclub')) {
                    $clubIdentifier = $currentCustomer->getCustomAttribute('customerclub')->getValue();
                }

                return $clubIdentifier;
            }
        }

        return $clubIdentifier;

    }

    // Get current customer associated club data
    public function getCustomerCollection()
    {
        $sellerid = ' ';

        $custclubIdentifier = $this->getCustClubIdentifier();
        $customerCollection = $this->_customerFactory->create()->getCollection()
            ->addAttributeToSelect("entity_id")
            ->addAttributeToSelect("email")
            ->addFieldToFilter(
                'wkv_club_unique_identfier',
                ['eq' => $custclubIdentifier]
            )
            ->load();
        foreach ($customerCollection as $customer) {

            $sellerid = $customer['entity_id'];
        }

        return $sellerid;
    }

    public function getClubCustomerCollection()
    {
        $customerCollection = false;

        if ($this->isCustomerLoggedIn()) {
            $custclubIdentifier = $this->getCustClubIdentifier();
            $customerCollection = $this->_customerFactory->create()->getCollection()
                ->addAttributeToSelect("*")
                ->addFieldToFilter(
                    'wkv_club_unique_identfier',
                    ['neq' => $custclubIdentifier]
                )
                ->load();

            return $customerCollection;
        }

    }

    /**
     * Get product collection
     *
     * @return bool|\Magento\Catalog\Model\ResourceModel\Product\Collection
     */
    public function _getProductCollection()
    {

        $sellerId = $this->getCustomerCollection();

        if (!$this->_productlists) {

            $paramData = $this->getRequest()->getParams();

            $productname = $this->getRequest()->getParam('name');
            $querydata = $this->mpProductModel->create()
                ->getCollection()
                ->addFieldToFilter(
                    'seller_id',
                    ['eq' => $sellerId]
                )
                ->addFieldToFilter(
                    'status',
                    ['eq' => 1]
                )
                ->addFieldToSelect('mageproduct_id')
                ->setOrder('mageproduct_id');

            $collectionByIds = $this->_productCollectionFactory->create();
            $collectionByIds->addAttributeToSelect('*');
            $collectionByIds->addFieldToFilter('entity_id', ['in' => $querydata->getData()]);
            $collectionByIds->addAttributeToSort('created_at', 'DESC');

            $this->_productlists = $collectionByIds;
            $this->_eventManager->dispatch(
                'catalog_block_product_list_collection',
                ['collection' => $collectionByIds]
            );
        }
        $this->_productlists->getSize();

        return $this->_productlists;
    }

    public function getSellerCollection()
    {

        $sellerId = $this->getCustomerCollection();
        if (!$this->sellerList) {
            $helper = $this->mpHelper;

            $collection = $this->_sellerlistCollectionFactory->create()
                ->addFieldToSelect(
                    '*'
                )->addFieldToFilter(
                    'seller_id',
                    ['eq' => $sellerId]
                )->setOrder(
                    'entity_id',
                    'desc'
                );
            $this->sellerList = $collection;
        }

        return $this->sellerList;
    }

    public function getClubSellerCollection($sellerid)
    {
        $writer = new \Zend_Log_Writer_Stream(BP.'/var/log/clublist.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info('order products');

        $logger->info(print_r($sellerid, true).'  seller id');
        $sellerId = '';
        $shopurl = '';
        if (!$this->sellerList) {
            $helper = $this->mpHelper;

            $collection = $this->_sellerlistCollectionFactory->create()
                ->addFieldToSelect(
                    '*'
                )->addFieldToFilter(
                    'seller_id',
                    ['eq' => $sellerid]
                );

            foreach ($collection as $coll) {
                $shopurl = $coll->getShopUrl();
                $logger->info(print_r($coll->getShopUrl(), true).'data');
            }
            $this->sellerList = $shopurl;
        }

        return $this->sellerList;
    }
}
