<?php
declare(strict_types=1);

namespace Comave\BigBuyShipping\Service;

use Psr\Http\Client\ClientExceptionInterface;
use Psr\Log\LoggerInterface;
use Comave\BigBuyShipping\Model\ConfigurableApi;
use Comave\SellerApi\Service\RequestHandler;
use Magento\Framework\Serialize\SerializerInterface;

class BigBuyShippingService
{
    private bool $debug = false;

    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly ConfigurableApi $configurableApi,
        private readonly RequestHandler $requestHandler,
        private readonly SerializerInterface $serializer
    ) {
    }

    /**
     * This will return lowest shipping cost value. In case of errors it will log and silently return null.
     * This is due to most errors are not resolvable by changing user input, so not relevant for the user.
     * @param string $productSku
     * @param string $countryIsoCode
     * @param bool $debug
     * @return array
     */
    public function fetchLowestShippingCost(string $productSku, string $countryIsoCode, bool $debug = false): array
    {
        $params = [
            "productCountry" => [
                "reference" => $productSku,
                "countryIsoCode" => $countryIsoCode
            ]
        ];

        $this->configurableApi
            ->setMethod('POST')
            ->setParams($this->serializer->serialize($params))
            ->setEndpoint(ConfigurableApi::URI_SHIPPING_COUNTRY_ONE_PRODUCT);

        $this->logDebug("Requesting api for country $countryIsoCode..", 'info');

        try {
            $response = $this->requestHandler->handleRequest($this->configurableApi);
        } catch (ClientExceptionInterface|\Exception $e) {
            $this->logger->error('Exception', [$e->getMessage()]);
            return ['error' => $e->getMessage()];
        }

        // store body content before logging or doing anything else, as it is a stream, otherwise rewind() is needed
        $responseBodyContent = $response->getResult()->getBody()->getContents();
        $this->logDebug('API response', 'info', [
            $response->getResult()->getStatusCode(),
            $response->getResult()->getReasonPhrase(),
            $responseBodyContent,
        ]);

        if ($response->hasError()) {
            $this->logger->error('API Response has errors', [$responseBodyContent]);
            return [
                'error' => $response->getResult()->getReasonPhrase(),
                'status' => $response->getResult()->getStatusCode()
            ];
        }

        if (empty($responseBodyContent)) {
            $this->logger->error('Unexpected empty response from api without error status.');
            return ['error' => 'Unexpected empty response from api without error status.'];
        }
        $response = $this->serializer->unserialize($responseBodyContent);

        $this->logDebug('Country API response', 'info', [$response]);

        if (!isset($response['shippingCost'])) {
            $this->logger->warning("No shipping cost returned for sku $productSku and country $countryIsoCode");
            return ['error' => "No shipping cost returned for sku $productSku and country $countryIsoCode"];
        }

       return $response;
    }

    /**
     * @throws ClientExceptionInterface
     * @throws \Exception
     */
    public function fetchShippingRates(array $products, string $countryIsoCode, string $postCode, $debug = false): array
    {
        $this->debug = $debug;

        $params = [
            "order" => [
                "products" => $products,
                "delivery" => [
                    "id" => 1234,
                    "isoCountry" => strtolower($countryIsoCode), // Convert country to lowercase (BigBuy format)
                    "postCode" => $postCode,
                ]
            ]
        ];
        $this->configurableApi->setParams($this->serializer->serialize($params))->setEndpoint();

        $this->logDebug(
            'Request method, endpoint, body',
            'info',
            [
                $this->configurableApi->getMethod(),
                $this->configurableApi->getEndpoint(),
                $this->configurableApi->getParams()
            ]
        );

        $response = $this->requestHandler->handleRequest($this->configurableApi);

        // store body content before logging or doing anything else, as it is a stream, otherwise rewind() is needed
        $responseBodyContent = $response->getResult()->getBody()->getContents();

        $this->logDebug('API response', 'info', [
            $response->getResult()->getStatusCode(),
            $response->getResult()->getReasonPhrase(),
            $responseBodyContent,
        ]);

        if ($response->hasError()) {
            throw new \Exception($response->getResult()->getReasonPhrase(), $response->getResult()->getStatusCode());
        }

        if (empty($responseBodyContent)) {
            throw new \Exception('Unexpected empty response from api without error status.');
        }
        $response = $this->serializer->unserialize($responseBodyContent);

        // parse response
        if (empty($response) || !isset($response['shippingOptions'])) {
            throw new \Exception('Unexpected response content for shipping rates.');
        }

        $shippingMethods = [];
        foreach ($response['shippingOptions'] as $option) {
            $service = $option['shippingService'];

            // Exclude shipping method if any products/categories are excluded
            if (!empty($service['excludedProductReferences']) || !empty($service['excludedCategoryIds'])) {
                $this->logDebug('Found excluded products/categories in response', 'info', $service);
                // TODO: check if we should treat these differently, instead of skipping the shipping method
                continue;
            }

            $shippingMethods[] = [
                'method_code' => $this->encodeMethodCode($service['name']), // e.g., "gls", "postal service"
                'carrier_name' => $service['serviceName'], // e.g., "GLS"
                'cost' => $option['cost'],
                'delivery_time' => $service['delay'], // e.g., "2-3 business days"
                'transport_method' => $service['transportMethod'] // e.g., "van"
            ];
        }

        if (empty($shippingMethods)) {
            $this->logDebug('No applicable shipping methods were found', 'info');
        }

        $this->logDebug('Applicable shipping methods', 'info', $shippingMethods);

        return $shippingMethods;
    }

    /**
     * @param string|null $message
     * @param mixed|null $level
     * @param array $context
     * @return void
     */
    private function logDebug(?string $message = null, mixed $level = null, array $context = []): void
    {
        if ($message === null || !$this->debug) {
            return;
        }

        $this->logger->log($level ?? 'warning', $message, $context);
    }

    /**
     * Encode third-party shipping method code for Magento-safe storage.
     */
    public static function encodeMethodCode(string $methodCode): string
    {
        return rawurlencode(trim($methodCode));
    }

    /**
     * Decode Magento-safe method code back to the original format.
     */
    public static function decodeMethodCode(string $encodedCode): string
    {
        return rawurldecode($encodedCode);
    }

    /**
     * Extract and decode shipping method code from Magento stored carrier_methodcode format
     *
     * Seems Bigbuy expects lowercase code: https://api.bigbuy.eu/rest/doc#tag/Order/operation/post_rest_order_order_create
     * regardless of available codes from: https://api.bigbuy.eu/rest/doc#tag/Shipping/operation/get_rest_shipping_carriers
     * @param string $storedShippingMethod
     * @return string
     */
    public static function getBigbuyMethodCodeFromShippingMethod(string $storedShippingMethod): string
    {
        $storedMethodCode = explode('_', $storedShippingMethod, 2)[1] ?? '';
        return strtolower(static::decodeMethodCode($storedMethodCode));
    }
}
