<?php
declare(strict_types=1);

namespace Comave\BigBuyShipping\Model;

use Comave\BigBuy\Model\ConfigProvider;
use Comave\SellerApi\Api\ConfigurableApiInterface;

/**
 * @link https://api.bigbuy.eu/rest/doc#tag/Shipping/operation/post_rest_shipping_order_shipping
 */
class ConfigurableApi implements ConfigurableApiInterface
{
    public const string URI_SHIPPING_ORDERS = '/rest/shipping/orders.json';
    public const string URI_SHIPPING_COUNTRY_ONE_PRODUCT = '/rest/shipping/lowest-shipping-cost-by-country.json';
    public const string URI_SHIPPING_COUNTRY_ALL_PRODUCTS = '/rest/shipping/lowest-shipping-costs-by-country/%s.json';

    private string $method = 'POST';
    private string $endpoint;
    private string $params;

    /**
     * @param \Comave\BigBuy\Model\ConfigProvider $configProvider
     */
    public function __construct(
        private readonly ConfigProvider $configProvider
    ) {
    }

    /**
     * @return string
     */
    public function getEndpoint(): string
    {
        return $this->endpoint;
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function getHeaders(): array
    {
        $apiKey = $this->configProvider->getApiKey();

        return [
            "Authorization" => "Bearer $apiKey",
            "Content-Type" => "application/json",
        ];
    }

    /**
     * @return string
     */
    public function getMethod(): string
    {
        return $this->method;
    }

    /**
     * @param string $method
     * @return ConfigurableApi
     */
    public function setMethod(string $method): ConfigurableApi
    {
        $this->method = $method;

        return $this;
    }

    /**
     * @return array|string|null
     */
    public function getParams(): array|string|null
    {
        return $this->params;
    }

    /**
     * @param array|string|null $params
     * @return self
     */
    public function setParams(array|string|null $params): ConfigurableApiInterface
    {
        $this->params = $params;

        return $this;
    }

    /**
     * Not mandatory, getEndpoint will return the default endpoint()
     *
     * @param string $endpoint
     * @param string|null $baseUri
     * @return self
     */
    public function setEndpoint(
        string $endpoint = self::URI_SHIPPING_ORDERS,
        ?string $baseUri = null
    ): ConfigurableApiInterface {
        $this->endpoint = ($baseUri ?? $this->configProvider->getApiEndpoint()).$endpoint;

        return $this;
    }
}
