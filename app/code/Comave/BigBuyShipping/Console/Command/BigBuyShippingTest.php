<?php
declare(strict_types=1);

namespace Comave\BigBuyShipping\Console\Command;

use Composer\Console\Input\InputOption;
use Magento\Framework\App\State;
use Magento\Framework\App\Area;
use Magento\Framework\Console\Cli;
use Magento\Framework\ObjectManagerInterface;
use Magento\Quote\Model\Quote\Address\RateRequest;

use Magento\Shipping\Model\CarrierFactory;
use Magento\Shipping\Model\Config as ShippingConfig;
use Magento\Catalog\Model\ProductFactory;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Comave\BigBuyShipping\Model\Carrier\BigBuyShipping;
use Comave\BigBuy\Model\ConfigProvider;


class BigBuyShippingTest extends Command
{
    const string INPUT_SKU = 'sku';
    const string INPUT_EXTRA = 'extra';

    public function __construct(
        private readonly State $state,
        private readonly ObjectManagerInterface $objectManager,
        private readonly CarrierFactory $carrierFactory,
        private readonly ShippingConfig $shippingConfig,
        private readonly ConfigProvider $configProvider,
        private readonly ProductFactory $productFactory,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct();
    }

    protected function configure()
    {
        $this->setName('bigbuy:shipping:api:test')
            ->setDescription('Test BigBuy Shipping API with a product SKU')
            ->addArgument(
                self::INPUT_SKU,
                InputArgument::OPTIONAL,
                'Product SKU'
            )
            ->addOption(
                self::INPUT_EXTRA,
                self::INPUT_EXTRA,
                InputOption::VALUE_NONE,
                'Extra fake SKU to be added to the simulated order'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->state->setAreaCode(Area::AREA_ADMINHTML);
        } catch (\Exception $e) {
            // Area code is already set
        }

        // Some extra info checks for debugging
        $apiMode = $this->configProvider->getApiConnectionMode();
        $output->writeln("BigBuy API mode: <info>$apiMode</info>");

        $carriers = $this->shippingConfig->getAllCarriers();
        $registered = array_key_exists(BigBuyShipping::CARRIER_CODE, $carriers) ? '<info>YES</info>' : '<error>NO</error>';
        $output->writeln("BigBuy Carrier registered in Magento: " . $registered);

        $activeCarriers = $this->shippingConfig->getActiveCarriers();
        $active = array_key_exists(BigBuyShipping::CARRIER_CODE, $activeCarriers) ? '<info>YES</info>' : '<error>NO</error>';
        $output->writeln("BigBuy Carrier active in Magento: " . $active);

        $sku = $input->getArgument(self::INPUT_SKU);
        $extraSkuFlag = $input->getOption(self::INPUT_EXTRA);

        if (!$sku) {
            // Default to known existing SKU by environment, as of Feb2025
            switch ($apiMode) {
                case 'sandbox':
                    $sku = 'S0593394';  // not found: F1515101
                    break;
                case 'production':
                    $sku = 'S0593394';  // or S0103120
                    break;
                default:
                    $output->writeln("<error>No default SKU for unknown BigBuy api mode.</error>");
                    return Cli::RETURN_FAILURE;
            }
        }
        $output->writeln("<info>Testing BigBuy Shipping API for SKU: $sku</info>");
        if ($extraSkuFlag) {
            $output->writeln("<comment>Additional SKU 'FAKE-SKU' will be added to simulated order</comment>");
        }
        // Load our BigBuy shipping carrier
        $carrier = $this->carrierFactory->get(BigBuyShipping::CARRIER_CODE);

        if (!$carrier) {
            $output->writeln("<error>BigBuy shipping carrier cannot be instantiated.</error>");
            return Cli::RETURN_FAILURE;
        }

        // Create a fake RateRequest and call collectRates
        $rateRequest = $this->createFakeRateRequest($sku, $extraSkuFlag);
        $rateResult = $carrier->collectRates($rateRequest);

        if (!$rateResult || !$rateResult->getAllRates()) {
            $output->writeln("<comment>No shipping options found.</comment>");
            return Cli::RETURN_FAILURE;
        }

        // Display shipping rates
        $output->writeln("<info>Shipping Options:</info>");
        foreach ($rateResult->getAllRates() as $rate) {
            $output->writeln(
                "<comment>Carrier:</comment> {$rate->getCarrierTitle()} | " .
                "<comment>Method:</comment> {$rate->getMethodTitle()} | " .
                "<comment>Cost:</comment> {$rate->getPrice()} EUR"
            );
        }

        return Cli::RETURN_SUCCESS;
    }

    private function createFakeRateRequest($sku, bool $extraSku = false): RateRequest
    {
        /** @var RateRequest $rateRequest */
        $rateRequest = $this->objectManager->create(RateRequest::class);

        // Fake customer shipping address
        $rateRequest->setDestCountryId('es'); // Spain
        $rateRequest->setDestPostcode('46011');
        $rateRequest->setDestRegionCode('Valencia');
        $rateRequest->setDestCity('Valencia');

        $product = $this->productFactory->create();
        $product->setSku($sku);
        $product->setIsVirtual(false);

        $items = [
            new \Magento\Framework\DataObject([
                'sku' => $sku,
                'qty' => 1, // Test with quantity 1
                'product' => $product
            ])
        ];

        if ($extraSku) {
            $product = $this->productFactory->create();
            $product->setSku('FAKE-SKU');
            $product->setIsVirtual(false);
            $items[] = new \Magento\Framework\DataObject([
                'sku' => $product->getSku(),
                'qty' => 2, // Test with quantity 1
                'product' => $product
            ]);
        }

        // Fake product(s) data
        $rateRequest->setAllItems($items);

        return $rateRequest;
    }
}
