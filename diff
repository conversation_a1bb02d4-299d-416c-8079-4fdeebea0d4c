diff --git a/app/code/Comave/Marketplace/Plugin/BlockSellerStripeActions.php b/app/code/Comave/Marketplace/Plugin/BlockSellerStripeActions.php
index d1ce14308..7e7dd6dc7 100644
--- a/app/code/Comave/Marketplace/Plugin/BlockSellerStripeActions.php
+++ b/app/code/Comave/Marketplace/Plugin/BlockSellerStripeActions.php
@@ -9,16 +9,16 @@ use Magento\Framework\Controller\ResultFactory;
 use Magento\Framework\Message\ManagerInterface as MessageManager;
 use Magento\Framework\UrlInterface;
 use Psr\Log\LoggerInterface;
+use Comave\Marketplace\Service\AdminImpersonation;
 
 class BlockSellerStripeActions
 {
-    private const CORE_IMPERSONATION_KEY = 'logged_as_customer_admind_id';
-
     private const ERROR_MESSAGE = 'Stripe settings are locked while you are in Admin Mode.';
 
     private const SELLER_PAYOUTS_PATH = 'seller_payouts/seller/payouts';
 
     public function __construct(
+        private readonly AdminImpersonation $adminImpersonation,
         private readonly CustomerSession $customerSession,
         private readonly ResultFactory   $resultFactory,
         private readonly MessageManager  $messageManager,
@@ -28,7 +28,7 @@ class BlockSellerStripeActions
 
     public function aroundExecute(ActionInterface $subject, callable $proceed)
     {
-        if (!$this->isAdminImpersonator()) {
+        if (!$this->adminImpersonation->isAdminImpersonating()) {
             return $proceed();
         }
 
@@ -36,7 +36,7 @@ class BlockSellerStripeActions
 
         $this->logger->warning('[StripeImpersonationGuard] Blocked impersonated attempt to modify Stripe settings.', [
             'controller' => get_class($subject),
-            'admin_user_id' => $this->customerSession->getData(self::CORE_IMPERSONATION_KEY),
+            'admin_user_id' => $this->customerSession->getData(AdminImpersonation::CORE_IMPERSONATION_KEY),
             'customer_id' => $this->customerSession->getCustomerId(),
         ]);
 
@@ -44,9 +44,4 @@ class BlockSellerStripeActions
         $result->setUrl($this->url->getUrl(self::SELLER_PAYOUTS_PATH));
         return $result;
     }
-
-    private function isAdminImpersonator(): bool
-    {
-        return (bool) $this->customerSession->getData(self::CORE_IMPERSONATION_KEY);
-    }
 }
diff --git a/app/code/Comave/Marketplace/Service/AdminImpersonation.php b/app/code/Comave/Marketplace/Service/AdminImpersonation.php
new file mode 100644
index 000000000..8db2e9dee
--- /dev/null
+++ b/app/code/Comave/Marketplace/Service/AdminImpersonation.php
@@ -0,0 +1,26 @@
+<?php
+declare(strict_types=1);
+
+namespace Comave\Marketplace\Service;
+
+use Magento\Customer\Model\Session as CustomerSession;
+
+class AdminImpersonation
+{
+    public const CORE_IMPERSONATION_KEY = 'logged_as_customer_admind_id';
+
+    public function __construct(
+        private readonly CustomerSession $customerSession
+    ) {
+    }
+
+    /**
+     * Check if admin is impersonating a customer
+     *
+     * @return bool
+     */
+    public function isAdminImpersonating(): bool
+    {
+        return (bool) $this->customerSession->getData(self::CORE_IMPERSONATION_KEY);
+    }
+}
diff --git a/app/code/Comave/SellerPayouts/Block/StripeButton.php b/app/code/Comave/SellerPayouts/Block/StripeButton.php
new file mode 100644
index 000000000..654ad70c2
--- /dev/null
+++ b/app/code/Comave/SellerPayouts/Block/StripeButton.php
@@ -0,0 +1,69 @@
+<?php
+declare(strict_types=1);
+
+namespace Comave\SellerPayouts\Block;
+
+use Comave\Marketplace\Service\AdminImpersonation;
+use Magento\Framework\View\Element\Template;
+use Magento\Framework\View\Element\Template\Context;
+
+class StripeButton extends Template
+{
+    public function __construct(
+        Context $context,
+        private readonly AdminImpersonation $adminImpersonation,
+        array $data = []
+    ) {
+        parent::__construct($context, $data);
+    }
+
+    /**
+     * Check if admin is impersonating a customer
+     *
+     * @return bool
+     */
+    public function isAdminImpersonating(): bool
+    {
+        return $this->adminImpersonation->isAdminImpersonating();
+    }
+
+    /**
+     * Get button label
+     *
+     * @return string
+     */
+    public function getLabel(): string
+    {
+        return (string) $this->getData('label');
+    }
+
+    /**
+     * Get button URL
+     *
+     * @return string
+     */
+    public function getButtonUrl(): string
+    {
+        return (string) $this->getData('url');
+    }
+
+    /**
+     * Get button type
+     *
+     * @return string
+     */
+    public function getType(): string
+    {
+        return $this->getData('type') ?: 'button';
+    }
+
+    /**
+     * Get CSS class
+     *
+     * @return string
+     */
+    public function getCssClass(): string
+    {
+        return (string) $this->getData('css_class');
+    }
+}
diff --git a/app/code/Comave/SellerPayouts/Block/StripeDetails.php b/app/code/Comave/SellerPayouts/Block/StripeDetails.php
index 3aaa539bc..bd3cda2cc 100644
--- a/app/code/Comave/SellerPayouts/Block/StripeDetails.php
+++ b/app/code/Comave/SellerPayouts/Block/StripeDetails.php
@@ -10,6 +10,7 @@ declare(strict_types=1);
 namespace Comave\SellerPayouts\Block;
 
 use Comave\Logger\Model\ComaveLogger;
+use Comave\Marketplace\Service\AdminImpersonation;
 use Comave\SellerPayouts\Helper\Data as PayoutHelper;
 use Magento\Customer\Model\CustomerFactory;
 use Magento\Customer\Model\Session;
@@ -31,6 +32,7 @@ class StripeDetails extends \Magento\Framework\View\Element\Template
      * @param \Comave\SellerPayouts\Helper\Data $payoutHelper
      * @param \Comave\Logger\Model\ComaveLogger $logger
      * @param \Magento\Framework\App\Http\Context $httpContext
+     * @param \Comave\Marketplace\Service\AdminImpersonation $adminImpersonation
      * @param mixed[] $data
      */
     public function __construct(
@@ -42,6 +44,7 @@ class StripeDetails extends \Magento\Framework\View\Element\Template
         private PayoutHelper $payoutHelper,
         private ComaveLogger $logger,
         private Context $httpContext,
+        private AdminImpersonation $adminImpersonation,
         array $data = []
     ) {
         parent::__construct($context, $data);
@@ -136,4 +139,14 @@ class StripeDetails extends \Magento\Framework\View\Element\Template
 
         return $countries;
     }
+
+    /**
+     * Check if admin is impersonating a customer
+     *
+     * @return bool
+     */
+    public function isAdminImpersonating(): bool
+    {
+        return $this->adminImpersonation->isAdminImpersonating();
+    }
 }
diff --git a/app/code/Comave/SellerPayouts/view/frontend/templates/seller/stripe-button.phtml b/app/code/Comave/SellerPayouts/view/frontend/templates/seller/stripe-button.phtml
new file mode 100644
index 000000000..dd4309d48
--- /dev/null
+++ b/app/code/Comave/SellerPayouts/view/frontend/templates/seller/stripe-button.phtml
@@ -0,0 +1,20 @@
+<?php /** @var $block \Comave\SellerPayouts\Block\StripeButton */ ?>
+<?php
+$isAdminImpersonating = $block->isAdminImpersonating();
+$label = $block->getLabel();
+$url = $block->getButtonUrl();
+$type = $block->getType();
+$cssClass = $block->getCssClass();
+?>
+
+<div class="<?= $block->escapeHtmlAttr($cssClass) ?>">
+    <?php if ($isAdminImpersonating): ?>
+        <button type="button" disabled><?= $block->escapeHtml(__($label)) ?></button>
+    <?php elseif ($url): ?>
+        <a href="<?= $block->escapeUrl($url) ?>">
+            <button type="<?= $block->escapeHtmlAttr($type) ?>"><?= $block->escapeHtml(__($label)) ?></button>
+        </a>
+    <?php else: ?>
+        <button type="<?= $block->escapeHtmlAttr($type) ?>"<?= $isAdminImpersonating ? ' disabled' : '' ?>><?= $block->escapeHtml(__($label)) ?></button>
+    <?php endif; ?>
+</div>
diff --git a/app/code/Comave/SellerPayouts/view/frontend/templates/seller/stripeconnect.phtml b/app/code/Comave/SellerPayouts/view/frontend/templates/seller/stripeconnect.phtml
index 50a98aab7..95240fc8e 100644
--- a/app/code/Comave/SellerPayouts/view/frontend/templates/seller/stripeconnect.phtml
+++ b/app/code/Comave/SellerPayouts/view/frontend/templates/seller/stripeconnect.phtml
@@ -1,8 +1,9 @@
-<?php /** @var $block \Magento\Framework\View\Element\Template */ ?>
+<?php /** @var $block \Comave\SellerPayouts\Block\StripeDetails */ ?>
 <?php
 $customer = $block->getCustomerDetails();
 $signInlink = '';
 $verificationlink = '';
+$isAdminImpersonating = $block->isAdminImpersonating();
 
 if ($customer) {
     if (!empty($customer->getStripeClientId())) {
@@ -29,58 +30,98 @@ if ($customer) {
     $countries = $block->getCountryOptions();
 }
 ?>
-<div class="stripe-connect">
+<div class="stripe-connect<?= $isAdminImpersonating ? ' disabled' : '' ?>">
+        <?php if ($isAdminImpersonating): ?>
+            <div class="message message-warning warning">
+                <span><?= $block->escapeHtml(__('(ADMIN MODE) Stripe settings are disabled while you are logged in as this customer.')) ?></span>
+            </div>
+        <?php endif; ?>
         <div id="stripe_icon">
-            <img src="<?php echo $block->getViewFileUrl('Comave_SellerPayouts::images/stripe_icon.png'); ?>" alt="icon...">
-            <p><?php echo __('Connect with Stripe') ?></p>
+            <img src="<?= $block->escapeUrl($block->getViewFileUrl('Comave_SellerPayouts::images/stripe_icon.png')) ?>" alt="icon...">
+            <p><?= $block->escapeHtml(__('Connect with Stripe')) ?></p>
         </div>
-        <form id="stripe-connect-form-seller" action="<?php echo $block->getUrl('seller_payouts/seller/verify'); ?>" method="post">
+        <form id="stripe-connect-form-seller" action="<?= $block->escapeUrl($block->getUrl('seller_payouts/seller/verify')) ?>" method="post"<?= $isAdminImpersonating ? ' class="disabled" data-admin-impersonating="true"' : ' data-admin-impersonating="false"' ?>>
             <div>
-                <label for="stripe_account_email"><?php echo __('Email ID'); ?></label>
-                <input type="email" id="stripe_account_email" name="stripe_account_email" value="<?php echo $emailId; ?>" required placeholder="<?php echo __('Enter Your Email ID'); ?>" />
+                <label for="stripe_account_email"><?= $block->escapeHtml(__('Email ID')) ?></label>
+                <input type="email" id="stripe_account_email" name="stripe_account_email" value="<?= $block->escapeHtmlAttr($emailId) ?>" required placeholder="<?= $block->escapeHtmlAttr(__('Enter Your Email ID')) ?>"<?= $isAdminImpersonating ? ' disabled' : '' ?> />
             </div>
             <div>
-                <label for="stripe_account_name"><?php echo __('Name'); ?></label>
-                <input type="text" id="stripe_account_name" name="stripe_account_name" value="<?php echo $name; ?>" required placeholder="<?php echo __('Enter Your Name'); ?>" />
+                <label for="stripe_account_name"><?= $block->escapeHtml(__('Name')) ?></label>
+                <input type="text" id="stripe_account_name" name="stripe_account_name" value="<?= $block->escapeHtmlAttr($name) ?>" required placeholder="<?= $block->escapeHtmlAttr(__('Enter Your Name')) ?>"<?= $isAdminImpersonating ? ' disabled' : '' ?> />
             </div>
             <div>
-                <label for="seller_stripe_country"><?php echo __('Country'); ?></label>
-                <select id="seller_stripe_country" name="seller_stripe_country" required>
-                    <option value=""><?php echo __('Select Your Country'); ?></option>
+                <label for="seller_stripe_country"><?= $block->escapeHtml(__('Country')) ?></label>
+                <select id="seller_stripe_country" name="seller_stripe_country" required<?= $isAdminImpersonating ? ' disabled' : '' ?>>
+                    <option value=""><?= $block->escapeHtml(__('Select Your Country')) ?></option>
                     <?php foreach ($countries as $countryCode => $countryName) : ?>
-                        <option value="<?php echo $countryCode; ?>" <?php echo ($countryCode == $sellerstripecountry) ? 'selected' : ''; ?>><?php echo $countryName; ?></option>
+                        <option value="<?= $block->escapeHtmlAttr($countryCode) ?>" <?= ($countryCode == $sellerstripecountry) ? 'selected' : '' ?>><?= $block->escapeHtml($countryName) ?></option>
                     <?php endforeach; ?>
                 </select>
             </div>
             <?php if (empty($StripeClientId)) : ?>
-                <div class="stripe_create_button">
-                    <button type="submit"><?php echo __('Create Account'); ?></button>
-                </div>
+                <?= $block->getLayout()->createBlock(
+                    \Comave\SellerPayouts\Block\StripeButton::class,
+                    '',
+                    [
+                        'data' => [
+                            'label' => 'Create Account',
+                            'type' => 'submit',
+                            'css_class' => 'stripe_create_button'
+                        ]
+                    ]
+                )->toHtml() ?>
             <?php endif; ?>
             <?php if (!empty($verificationlink)) : ?>
-                <div class="stripe_verify_button">
-                    <a href="<?php echo $verificationlink; ?>">
-                        <button type="button"><?php echo __('Verify Account'); ?></button>
-                    </a>
-                </div>
+                <?= $block->getLayout()->createBlock(
+                    \Comave\SellerPayouts\Block\StripeButton::class,
+                    '',
+                    [
+                        'data' => [
+                            'label' => 'Verify Account',
+                            'url' => $verificationlink,
+                            'css_class' => 'stripe_verify_button'
+                        ]
+                    ]
+                )->toHtml() ?>
             <?php endif; ?>
             <?php if (!empty($signInlink)) : ?>
-                <div class="stripe_signin_button">
-                    <a href="<?php echo $signInlink; ?>">
-                        <button type="button"><?php echo __('Sign In'); ?></button>
-                    </a>
-                </div>
+                <?= $block->getLayout()->createBlock(
+                    \Comave\SellerPayouts\Block\StripeButton::class,
+                    '',
+                    [
+                        'data' => [
+                            'label' => 'Sign In',
+                            'url' => $signInlink,
+                            'css_class' => 'stripe_signin_button'
+                        ]
+                    ]
+                )->toHtml() ?>
             <?php endif; ?>
         </form>
         <div id="stripe-loader" style="display:none;">
-            <img src="<?php echo $block->getViewFileUrl('Comave_SellerPayouts::images/loader.webp'); ?>" alt="Loading...">
-            <p><?php echo __('Verifying... Please wait.'); ?></p>
+            <img src="<?= $block->escapeUrl($block->getViewFileUrl('Comave_SellerPayouts::images/loader.webp')) ?>" alt="Loading...">
+            <p><?= $block->escapeHtml(__('Verifying... Please wait.')) ?></p>
         </div>
 </div>
 <script type="text/javascript">
     require(['jquery'], function($) {
         $('#stripe-connect-form-seller').on('submit', function(e) {
-            $('.stripe-connect').find('#stripe-loader').show(); // Show loader inside stripe-connect div
+            var $form = $(this);
+            var isAdminImpersonating = $form.data('admin-impersonating');
+
+            if (isAdminImpersonating) {
+                e.preventDefault();
+                return false;
+            }
+
+            $('.stripe-connect').find('#stripe-loader').show();
         });
     });
 </script>
+
+<style>
+.stripe-connect.disabled {
+    opacity: 0.5;
+    pointer-events: none;
+}
+</style>
